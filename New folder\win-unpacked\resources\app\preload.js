const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron');

// تعريض APIs آمنة للعملية المرسلة
contextBridge.exposeInMainWorld('electronAPI', {
    // حفظ ملف
    saveFile: (data, filename) => ipcRenderer.invoke('save-file', data, filename),
    
    // قراءة ملف
    readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
    
    // الاستماع للأحداث من العملية الرئيسية
    onExportData: (callback) => ipcRenderer.on('export-data', callback),
    onImportData: (callback) => ipcRenderer.on('import-data', callback),
    onShowAbout: (callback) => ipcRenderer.on('show-about', callback),
    
    // إزالة المستمعين
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
    
    // معلومات النظام
    platform: process.platform,
    isElectron: true
});
