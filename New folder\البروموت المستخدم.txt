PROMPT لإنشاء نظام إدارة نقاط البيع العربي - تكنوفلاش
================================================================

المطلوب: إنشاء نظام إدارة نقاط بيع شامل باللغة العربية مع واجهة احترافية

التقنيات المطلوبة:
- HTML5 + CSS3 + JavaScript (Vanilla JS)
- تصميم متجاوب (Responsive Design)
- دعم RTL للغة العربية
- localStorage لحفظ البيانات محلي<|im_start|>
- إمكانية تحويل لتطبيق سطح مكتب باستخدام Electron

المواصفات الوظيفية:
===================

1. نظام تسجيل الدخول:
   - شاشة تسجيل دخول بكلمة مرور
   - كلمة المرور الافتراضية: "123"
   - إمكانية تغيير كلمة المرور من الإعدادات
   - حفظ حالة تسجيل الدخول

2. لوحة المعلومات الرئيسية:
   - إحصائيات سريعة (إجمالي المبيعات، عدد المنتجات، العملاء)
   - مؤشرات الأداء اليومي والشهري
   - تنبيهات المخزون المنخفض
   - رسوم بيانية بسيطة للمبيعات
   - أرقام عربية هندية (٠-٩)

3. إدارة المنتجات:
   - إضافة/تعديل/حذف المنتجات
   - حقول: الاسم، الوصف، السعر، الكمية، الفئة
   - تصنيف المنتجات حسب الفئات
   - بحث وفلترة المنتجات
   - تنبيهات نفاد المخزون
   - تحديث الأسعار بشكل مجمع

4. نظام المبيعات:
   - واجهة بيع سريعة وسهلة
   - إضافة المنتجات لسلة المشتريات
   - حساب المجموع مع الضريبة تلقائ<|im_start|>
   - طرق دفع: نقد<|im_start|> / على الحساب
   - اختيار العميل للمبيعات الآجلة
   - طباعة فواتير احترافية
   - حفظ تاريخ المبيعات

5. إدارة العملاء:
   - إضافة/تعديل/حذف العملاء
   - حقول: الاسم، الهاتف، البريد، العنوان
   - تتبع رصيد كل عميل (مدين/دائن)
   - عميل افتراضي "ضيف" للمبيعات النقدية
   - تاريخ معاملات كل عميل
   - نظام إدارة الديون والمدفوعات
   - طباعة كشف حساب العميل

6. إدارة الموردين:
   - إضافة/تعديل/حذف الموردين
   - معلومات الاتصال الكاملة
   - تتبع المدفوعات للموردين
   - فواتير المشتريات
   - طرق دفع متعددة للمشتريات

7. نظام المشتريات:
   - تسجيل فواتير المشتريات
   - ربط المشتريات بالموردين
   - تحديث المخزون تلقائين
   - طباعة سندات الاستلام
   - تتبع تكلفة البضائع

8. إدارة الديون والمدفوعات:
   - صفحة مخصصة لإدارة الديون
   - قائمة العملاء المدينين
   - تسجيل دفعات من العملاء
   - طباعة إيصالات استلام المبلغ
   - تنبيهات الديون المتأخرة
   - إحصائيات الديون والتحصيلات

9. التقارير والإحصائيات:
   - تقارير المبيعات (يومي/شهري/سنوي)
   - تقارير المخزون والمنتجات
   - تقارير العملاء والديون
   - تقارير الموردين والمشتريات
   - تقارير الأرباح والخسائر
   - إمكانية طباعة التقارير

10. الإعدادات:
    - بيانات الشركة (الاسم، العنوان، الهاتف، البريد)
    - نسبة الضريبة القابلة للتعديل
    - إعدادات الطباعة
    - تغيير كلمة المرور
    - إعدادات النسخ الاحتياطي
    - تخصيص شعار الشركة

11. النسخ الاحتياطي:
    - تصدير البيانات بصيغة JSON
    - استيراد البيانات من ملف احتياطي
    - حفظ تلقائي للبيانات
    - استعادة البيانات في حالة الطوارئ

المواصفات التقنية:
==================

1. التصميم والواجهة:
   - تصميم Neumorphism عصري
   - ألوان متدرجة جذابة
   - واجهة عربية RTL كاملة
   - تصميم متجاوب لجميع الشاشات
   - ثيم مضيء وداكن قابل للتبديل
   - انتقالات سلسة بين الصفحات
   - أيقونات واضحة ومعبرة

2. الوظائف التقنية:
   - حفظ البيانات في localStorage
   - تشفير كلمة المرور
   - التحقق من صحة البيانات المدخلة
   - رسائل تنبيه وتأكيد واضحة
   - نظام بحث سريع وفعال
   - فلترة وترتيب البيانات

3. الطباعة:
   - تصميم فواتير احترافية
   - دعم طباعة الإيصالات
   - تخطيط مناسب للطباعة
   - إمكانية معاينة قبل الطباعة
   - دعم أحجام ورق مختلفة

4. الأمان:
   - حماية بكلمة مرور
   - تشفير البيانات الحساسة
   - منع الوصول غير المصرح
   - نظام صلاحيات بسيط

هيكل الملفات المطلوب:
======================

index.html - الصفحة الرئيسية
main.js - المنطق الأساسي والإعدادات
app.js - وظائف التطبيق الرئيسية
style.css - أنماط التصميم
dashboard.js - لوحة المعلومات
products.js - إدارة المنتجات (إذا لزم الأمر)
sales.js - نظام المبيعات (إذا لزم الأمر)
customers.js - إدارة العملاء (إذا لزم الأمر)
suppliers.js - إدارة الموردين (إذا لزم الأمر)
purchases.js - نظام المشتريات
reports.js - التقارير والإحصائيات
database.js - إدارة قاعدة البيانات المحلية

متطلبات خاصة:
===============

1. اللغة العربية:
   - جميع النصوص باللغة العربية
   - دعم الكتابة من اليمين لليسار
   - استخدام الأرقام العربية الهندية (٠-٩)
   - تنسيق التواريخ بالتقويم الهجري والميلادي
   - دعم العملة المحلية

2. سهولة الاستخدام:
   - واجهة بديهية وبسيطة
   - اختصارات لوحة المفاتيح
   - رسائل مساعدة وتوجيهية
   - تجربة مستخدم سلسة
   - أزرار واضحة ومفهومة

3. الأداء:
   - تحميل سريع للصفحات
   - استجابة فورية للأوامر
   - تحسين استهلاك الذاكرة
   - عمل سلس على الأجهزة الضعيفة

4. التوافق:
   - يعمل على جميع المتصفحات الحديثة
   - دعم أنظمة Windows/Mac/Linux
   - إمكانية التحويل لتطبيق سطح مكتب
   - عمل بدون اتصال إنترنت

ميزات إضافية مرغوبة:
=====================

1. نظام الإشعارات:
   - تنبيهات نفاد المخزون
   - تذكير بالديون المستحقة
   - إشعارات المبيعات الجديدة

2. الإحصائيات المتقدمة:
   - رسوم بيانية تفاعلية
   - مقارنات شهرية وسنوية
   - تحليل اتجاهات المبيعات
   - أفضل المنتجات مبيعاً

3. التخصيص:
   - تغيير ألوان الواجهة
   - تخصيص تخطيط الفواتير
   - إضافة شعار الشركة
   - حقول مخصصة للمنتجات

4. التكامل:
   - تصدير البيانات لـ Excel
   - طباعة باركود للمنتجات
   - ربط مع طابعات الإيصالات
   - دعم الماسحات الضوئية

معايير الجودة:
===============

1. الكود:
   - كود نظيف ومنظم
   - تعليقات واضحة بالعربية
   - اتباع أفضل الممارسات
   - سهولة الصيانة والتطوير

2. التصميم:
   - تصميم احترافي وجذاب
   - ألوان متناسقة ومريحة للعين
   - خطوط واضحة ومقروءة
   - تخطيط منطقي ومنظم

3. الوظائف:
   - جميع الوظائف تعمل بشكل صحيح
   - معالجة الأخطاء بشكل مناسب
   - حفظ البيانات بأمان
   - سرعة في الاستجابة

4. التوثيق:
   - دليل مستخدم شامل
   - تعليمات التثبيت والاستخدام
   - شرح الوظائف والميزات
   - نصائح لتحقيق أفضل استفادة

ملاحظات مهمة:
===============

- النظام يجب أن يكون مجاني بالكامل
- لا توجد قيود على الاستخدام أو عدد المعاملات
- يجب أن يعمل محلي应用查看 دون الحاجة لخادم
- البيانات محفوظة على جهاز المستخدم فقط
- إمكانية التطوير والتخصيص لاحق应用查看
- دعم فني من خلال التوثيق والأدلة

الهدف النهائي:
===============

إنشاء نظام إدارة نقاط بيع عربي شامل ومجاني يلبي احتياجات الشركات الصغيرة والمتوسطة، مع واجهة احترافية وسهولة في الاستخدام، وإمكانية التحويل لتطبيق سطح مكتب للتوزيع على المستخدمين.

اسم المشروع: "StoreMaster - System / H - TECH "
المطور: Eng / Hossam Osama
الترخيص: مجاني ومفتوح المصدر