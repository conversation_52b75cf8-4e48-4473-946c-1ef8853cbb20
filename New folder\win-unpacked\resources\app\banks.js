// ===== الحسابات البنكية =====

const banks = (() => {
    if (!window.bankAccounts) window.bankAccounts = [];
    if (!window.bankTransactions) window.bankTransactions = [];

    function showAddAccount(){
        const c = document.getElementById('banksContent');
        c.innerHTML = `
            <div class="form-container">
                <h4>إضافة حساب بنكي</h4>
                <div class="form-grid">
                    <input id="bk_name" class="neumorphic-input" placeholder="اسم البنك (أهلي/مصر/CIB)">
                    <input id="bk_account" class="neumorphic-input" placeholder="رقم الحساب/IBAN">
                    <input id="bk_currency" class="neumorphic-input" placeholder="العملة (جنيه/دولار/ريال)">
                </div>
                <div class="form-actions">
                    <button onclick="banks.saveAccount()" class="neumorphic-button primary">حفظ</button>
                </div>
            </div>`;
    }

    function saveAccount(){
        bankAccounts.push({ id: generateId(), name: document.getElementById('bk_name').value.trim(), account: document.getElementById('bk_account').value.trim(), currency: document.getElementById('bk_currency').value.trim(), balance: 0 });
        saveData(); if (typeof onDataChange === 'function') { try { onDataChange(); } catch(e) { console.error(e); } } showNotification('تم إضافة الحساب البنكي', 'success'); list();
    }

    function showDeposit(){ showTxnForm('deposit'); }
    function showWithdraw(){ showTxnForm('withdraw'); }
    function showInternalTransfer(){ showTxnForm('internal'); }
    function showWireTransfer(){ showTxnForm('wire'); }

    function showTxnForm(type){
        const c = document.getElementById('banksContent');
        c.innerHTML = `
            <div class="form-container">
                <h4>${type==='deposit'?'إيداع':type==='withdraw'?'سحب':type==='internal'?'تحويل داخلي':'تحويل خارجي'}</h4>
                <div class="form-grid">
                    <select id="bk_from" class="neumorphic-input">${(bankAccounts||[]).map(a=>`<option value="${a.id}">${a.name}-${a.account}</option>`).join('')}</select>
                    ${type==='internal' || type==='wire' ? `<select id="bk_to" class="neumorphic-input">${(bankAccounts||[]).map(a=>`<option value="${a.id}">${a.name}-${a.account}</option>`).join('')}</select>` : ''}
                    <input id="bk_amount" type="number" step="0.01" class="neumorphic-input" placeholder="المبلغ">
                    <input id="bk_desc" class="neumorphic-input" placeholder="الوصف">
                </div>
                <div class="form-actions">
                    <button onclick="banks.saveTxn('${type}')" class="neumorphic-button primary">حفظ</button>
                </div>
            </div>`;
    }

    function saveTxn(type){
        const fromId = document.getElementById('bk_from').value;
        const toId = (type==='internal'||type==='wire') ? document.getElementById('bk_to').value : null;
        const amount = safeNumber(parseFloat(document.getElementById('bk_amount').value),0);
        const desc = document.getElementById('bk_desc').value.trim();
        const from = bankAccounts.find(a=>a.id===fromId);
        const to = toId ? bankAccounts.find(a=>a.id===toId) : null;
        if (!from) { showNotification('اختر الحساب', 'error'); return; }
        if (amount<=0) { showNotification('أدخل مبلغ صحيح', 'error'); return; }
        const id = generateId();
        const date = new Date().toISOString();
        if (type==='deposit') { from.balance += amount; }
        else if (type==='withdraw') { from.balance -= amount; }
        else if (type==='internal') { if(!to){showNotification('اختر حساب التحويل إليه','error');return;} from.balance -= amount; to.balance += amount; }
        else if (type==='wire') { if(!to){showNotification('اختر حساب التحويل إليه','error');return;} from.balance -= amount; }
        bankTransactions.push({ id, type, fromId, toId, amount, desc, date });
        saveData(); if (typeof onDataChange === 'function') { try { onDataChange(); } catch(e) { console.error(e); } } showNotification('تم حفظ الحركة البنكية', 'success'); list();
    }

    function list(){
        const c = document.getElementById('banksContent');
        c.innerHTML = `
            <h4>الحسابات</h4>
            <div>${(bankAccounts||[]).map(a=>`<div class="summary-card">${a.name} - ${a.account} (${a.currency}): ${formatCurrency(a.balance)}</div>`).join('') || '<div style="color:#666;">لا توجد حسابات</div>'}</div>
            <h4 style="margin-top:20px;">آخر الحركات</h4>
            <div>${(bankTransactions||[]).slice(-20).reverse().map(t=>`<div class="transaction-item">${formatDateShort(t.date)} - ${t.type} - ${formatCurrency(t.amount)} - ${t.desc||''}</div>`).join('') || '<div style="color:#666;">لا توجد حركات</div>'}</div>
        `;
    }

    function showReports(){
        const c = document.getElementById('banksContent');
        const total = (bankAccounts||[]).reduce((s,a)=> s + a.balance, 0);
        c.innerHTML = `<div class="report-summary">إجمالي أرصدة البنوك: ${formatCurrency(total)}</div>`;
    }

    return { showAddAccount, saveAccount, showDeposit, showWithdraw, showInternalTransfer, showWireTransfer, saveTxn, list, showReports };
})();


