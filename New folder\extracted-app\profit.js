// ===== تقرير صافي الربح =====

// تهيئة صفحة صافي الربح
function initializeProfit() {
    // تعيين التواريخ الافتراضية (الشهر الحالي)
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    document.getElementById('profitStartDate').value = firstDay.toISOString().split('T')[0];
    document.getElementById('profitEndDate').value = lastDay.toISOString().split('T')[0];
    
    // إنشاء التقرير الافتراضي
    generateProfitReport();
}

// تغيير الفترة المحددة مسبقاً
function changeProfitPeriod() {
    const period = document.getElementById('profitPeriod').value;
    const today = new Date();
    let startDate, endDate;
    
    switch (period) {
        case 'today':
            startDate = endDate = today.toISOString().split('T')[0];
            break;
        case 'week':
            const weekStart = new Date(today);
            weekStart.setDate(today.getDate() - today.getDay());
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekStart.getDate() + 6);
            startDate = weekStart.toISOString().split('T')[0];
            endDate = weekEnd.toISOString().split('T')[0];
            break;
        case 'month':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
            endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0).toISOString().split('T')[0];
            break;
        case 'year':
            startDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
            endDate = new Date(today.getFullYear(), 11, 31).toISOString().split('T')[0];
            break;
        default:
            return; // فترة مخصصة - لا تغيير
    }
    
    document.getElementById('profitStartDate').value = startDate;
    document.getElementById('profitEndDate').value = endDate;
    
    generateProfitReport();
}

// إنشاء تقرير صافي الربح
function generateProfitReport() {
    const startDate = document.getElementById('profitStartDate').value;
    const endDate = document.getElementById('profitEndDate').value;
    
    if (!startDate || !endDate) {
        showNotification('يرجى تحديد فترة التقرير', 'error');
        return;
    }
    
    if (startDate > endDate) {
        showNotification('تاريخ البداية يجب أن يكون قبل تاريخ النهاية', 'error');
        return;
    }
    
    // حساب المبيعات
    const totalSales = calculateSalesForPeriod(startDate, endDate);
    
    // حساب تكلفة المشتريات
    const totalPurchases = calculatePurchasesForPeriod(startDate, endDate);
    
    // حساب تكلفة التصنيع
    const totalManufacturing = calculateManufacturingCostForPeriod(startDate, endDate);
    
    // حساب المصروفات
    const totalExpenses = calculateExpensesForPeriod(startDate, endDate);
    
    // حساب مرتبات الموظفين
    const totalSalaries = calculateSalariesForPeriod(startDate, endDate);
    
    // حساب قيمة الهالك
    const totalWastage = calculateWastageForPeriod(startDate, endDate);
    
    // حساب صافي الربح مع التحقق من صحة البيانات
    const safeTotalSales = safeNumber(totalSales, 0);
    const safeTotalPurchases = safeNumber(totalPurchases, 0);
    const safeTotalManufacturing = safeNumber(totalManufacturing, 0);
    const safeTotalExpenses = safeNumber(totalExpenses, 0);
    const safeTotalSalaries = safeNumber(totalSalaries, 0);
    const safeTotalWastage = safeNumber(totalWastage, 0);

    const netProfit = safeTotalSales - (safeTotalPurchases + safeTotalManufacturing + safeTotalExpenses + safeTotalSalaries + safeTotalWastage);
    
    // تحديث العرض
    updateProfitSummary({
        totalSales: safeTotalSales,
        totalPurchases: safeTotalPurchases,
        totalManufacturing: safeTotalManufacturing,
        totalExpenses: safeTotalExpenses,
        totalSalaries: safeTotalSalaries,
        totalWastage: safeTotalWastage,
        netProfit: safeNumber(netProfit, 0)
    });
    
    // إنشاء التفاصيل
    generateProfitDetails(startDate, endDate, {
        totalSales,
        totalPurchases,
        totalManufacturing,
        totalExpenses,
        totalSalaries,
        totalWastage,
        netProfit
    });
}

// تحديث ملخص الأرباح
function updateProfitSummary(data) {
    document.getElementById('totalSales').textContent = formatCurrency(data.totalSales);
    document.getElementById('totalPurchases').textContent = formatCurrency(data.totalPurchases);
    document.getElementById('totalManufacturing').textContent = formatCurrency(data.totalManufacturing);
    document.getElementById('totalExpenses').textContent = formatCurrency(data.totalExpenses);
    document.getElementById('totalSalaries').textContent = formatCurrency(data.totalSalaries);
    document.getElementById('totalWastage').textContent = formatCurrency(data.totalWastage);

    const netProfitElement = document.getElementById('netProfit');
    netProfitElement.textContent = formatCurrency(data.netProfit);
    
    // تغيير لون صافي الربح حسب القيمة
    const netProfitCard = netProfitElement.closest('.profit-card');
    netProfitCard.classList.remove('profit', 'loss');
    if (data.netProfit > 0) {
        netProfitCard.classList.add('profit');
    } else if (data.netProfit < 0) {
        netProfitCard.classList.add('loss');
    }
}

// حساب المبيعات للفترة
function calculateSalesForPeriod(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    // تضمين يوم النهاية بالكامل
    end.setHours(23,59,59,999);
    return sales
        .filter(sale => {
            const d = new Date(sale.date);
            return d >= start && d <= end;
        })
        .reduce((sum, sale) => sum + (typeof sale.total === 'number' ? sale.total : (sale.totalAmount || 0)), 0);
}

// حساب المشتريات للفترة
function calculatePurchasesForPeriod(startDate, endDate) {
    return purchases
        .filter(purchase => purchase.date >= startDate && purchase.date <= endDate)
        .reduce((sum, purchase) => sum + purchase.totalAmount, 0);
}

// حساب تكلفة التصنيع للفترة
function calculateManufacturingCostForPeriod(startDate, endDate) {
    return manufacturing
        .filter(record => record.date >= startDate && record.date <= endDate)
        .reduce((sum, record) => {
            // حساب تكلفة الخامات المستخدمة
            const ingredientsCost = record.ingredients.reduce((ingredientSum, ingredient) => {
                const product = products.find(p => p.id === ingredient.productId);
                const costPrice = product ? product.costPrice : 0;
                return ingredientSum + (ingredient.usedQuantity * costPrice);
            }, 0);
            return sum + ingredientsCost;
        }, 0);
}

// حساب المصروفات للفترة
function calculateExpensesForPeriod(startDate, endDate) {
    return expenses
        .filter(expense => expense.date >= startDate && expense.date <= endDate)
        .reduce((sum, expense) => sum + expense.amount, 0);
}

// حساب مرتبات الموظفين للفترة (تقدير شهري)
function calculateSalariesForPeriod(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const monthlyRatio = diffDays / 30; // تقدير نسبة الشهر
    
    const totalMonthlySalaries = employees
        .filter(e => e.status === 'active')
        .reduce((sum, e) => sum + e.salary, 0);
    
    return totalMonthlySalaries * monthlyRatio;
}

// إنشاء تفاصيل التقرير
function generateProfitDetails(startDate, endDate, data) {
    const container = document.getElementById('profitDetails');
    
    let html = '<div class="profit-details-content">';
    
    // معادلة الحساب
    html += '<div class="profit-formula">';
    html += '<h5>معادلة حساب صافي الربح:</h5>';
    html += '<p class="formula-text">';
    html += 'صافي الربح = المبيعات - (المشتريات + تكلفة التصنيع + المصروفات + المرتبات + قيمة الهالك)';
    html += '</p>';
    html += '<p class="formula-calculation">';
    html += `${data.netProfit.toFixed(2)} = ${data.totalSales.toFixed(2)} - (${data.totalPurchases.toFixed(2)} + ${data.totalManufacturing.toFixed(2)} + ${data.totalExpenses.toFixed(2)} + ${data.totalSalaries.toFixed(2)} + ${data.totalWastage.toFixed(2)})`;
    html += '</p>';
    html += '</div>';
    
    // تفاصيل المبيعات
    const salesInPeriod = sales.filter(sale => sale.date >= startDate && sale.date <= endDate);
    html += '<div class="detail-section">';
    html += '<h6>تفاصيل المبيعات:</h6>';
    html += `<p>عدد الفواتير: ${salesInPeriod.length}</p>`;
    html += `<p>إجمالي المبيعات: ${formatCurrency(data.totalSales)}</p>`;
    if (salesInPeriod.length > 0) {
        const avgSale = safeNumber(data.totalSales / salesInPeriod.length, 0);
        html += `<p>متوسط الفاتورة: ${formatCurrency(avgSale)}</p>`;
    }
    html += '</div>';
    
    // تفاصيل المشتريات
    const purchasesInPeriod = purchases.filter(purchase => purchase.date >= startDate && purchase.date <= endDate);
    html += '<div class="detail-section">';
    html += '<h6>تفاصيل المشتريات:</h6>';
    html += `<p>عدد الفواتير: ${purchasesInPeriod.length}</p>`;
    html += `<p>إجمالي المشتريات: ${formatCurrency(data.totalPurchases)}</p>`;
    html += '</div>';
    
    // تفاصيل التصنيع
    const manufacturingInPeriod = manufacturing.filter(record => record.date >= startDate && record.date <= endDate);
    html += '<div class="detail-section">';
    html += '<h6>تفاصيل التصنيع:</h6>';
    html += `<p>عدد العمليات: ${manufacturingInPeriod.length}</p>`;
    html += `<p>تكلفة الخامات: ${formatCurrency(data.totalManufacturing)}</p>`;
    html += '</div>';
    
    // تفاصيل المصروفات
    const expensesInPeriod = expenses.filter(expense => expense.date >= startDate && expense.date <= endDate);
    html += '<div class="detail-section">';
    html += '<h6>تفاصيل المصروفات:</h6>';
    html += `<p>عدد المصروفات: ${expensesInPeriod.length}</p>`;
    html += `<p>إجمالي المصروفات: ${formatCurrency(data.totalExpenses)}</p>`;
    html += '</div>';
    
    // تفاصيل الهالك
    const wastageInPeriod = wastage.filter(w => w.date >= startDate && w.date <= endDate);
    html += '<div class="detail-section">';
    html += '<h6>تفاصيل الهالك:</h6>';
    html += `<p>عدد السجلات: ${wastageInPeriod.length}</p>`;
    html += `<p>قيمة الهالك: ${formatCurrency(data.totalWastage)}</p>`;
    html += '</div>';
    
    // نسب الأرباح
    html += '<div class="detail-section">';
    html += '<h6>نسب الأرباح:</h6>';
    if (data.totalSales > 0) {
        const profitMargin = (data.netProfit / data.totalSales) * 100;
        html += `<p>هامش الربح: ${profitMargin.toFixed(2)}%</p>`;
    }
    const totalCosts = data.totalPurchases + data.totalManufacturing + data.totalExpenses + data.totalSalaries + data.totalWastage;
    if (totalCosts > 0) {
        const costRatio = (totalCosts / data.totalSales) * 100;
        html += `<p>نسبة التكاليف: ${costRatio.toFixed(2)}%</p>`;
    }
    html += '</div>';
    
    html += '</div>';
    container.innerHTML = html;
}

// إضافة مستمع لتغيير الفترة
document.addEventListener('DOMContentLoaded', function() {
    const profitPeriod = document.getElementById('profitPeriod');
    if (profitPeriod) {
        profitPeriod.addEventListener('change', changeProfitPeriod);
    }
});
