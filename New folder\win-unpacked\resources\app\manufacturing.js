// ===== إدارة التصنيع والإنتاج =====

// متغيرات التصنيع
let currentRecipeIngredients = [];
let editingRecipeId = null;

// تهيئة صفحة التصنيع
function initializeManufacturing() {
    loadProductsToSelect('ingredientProduct');
    loadProductsToSelect('manufacturingRecipe', 'recipe');
    loadEmployeesToSelect('manufacturingEmployee');
    displayRecipesList();
    displayManufacturingHistory();
    
    // تعيين التاريخ الحالي
    document.getElementById('manufacturingDate').value = new Date().toISOString().split('T')[0];
}

// تحميل المنتجات في القائمة المنسدلة
function loadProductsToSelect(selectId, type = 'product') {
    const select = document.getElementById(selectId);
    select.innerHTML = type === 'recipe' ? '<option value="">اختر الوصفة</option>' : '<option value="">اختر المنتج/الخامة</option>';
    
    if (type === 'recipe') {
        recipes.forEach(recipe => {
            const option = document.createElement('option');
            option.value = recipe.id;
            option.textContent = `${recipe.name} (${recipe.quantity} وحدة)`;
            select.appendChild(option);
        });
    } else {
        products.forEach(product => {
            const option = document.createElement('option');
            option.value = product.id;
            option.textContent = `${product.name} - متوفر: ${product.stockQuantity} ${product.unit}`;
            select.appendChild(option);
        });
    }
}

// تحميل الموظفين في القائمة المنسدلة
function loadEmployeesToSelect(selectId) {
    const select = document.getElementById(selectId);
    select.innerHTML = '<option value="">الموظف المسئول</option>';
    
    employees.forEach(employee => {
        if (employee.status === 'active') {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = employee.name;
            select.appendChild(option);
        }
    });
}

// إضافة خامة للوصفة
function addIngredient() {
    const productId = document.getElementById('ingredientProduct').value;
    const quantity = parseFloat(document.getElementById('ingredientQuantity').value);
    
    if (!productId || !quantity || quantity <= 0) {
        showNotification('يرجى اختيار المنتج وإدخال كمية صحيحة', 'error');
        return;
    }
    
    const product = products.find(p => p.id === productId);
    if (!product) {
        showNotification('المنتج غير موجود', 'error');
        return;
    }
    
    // التحقق من عدم تكرار المنتج
    if (currentRecipeIngredients.find(ing => ing.productId === productId)) {
        showNotification('هذا المنتج موجود بالفعل في الوصفة', 'error');
        return;
    }
    
    currentRecipeIngredients.push({
        productId: productId,
        productName: product.name,
        productUnit: product.unit,
        quantity: quantity,
        availableStock: product.stockQuantity
    });
    
    displayIngredientsList();
    
    // مسح الحقول
    document.getElementById('ingredientProduct').value = '';
    document.getElementById('ingredientQuantity').value = '';
}

// عرض قائمة الخامات
function displayIngredientsList() {
    const container = document.getElementById('ingredientsList');
    
    if (currentRecipeIngredients.length === 0) {
        container.innerHTML = '<p>لا توجد خامات مضافة</p>';
        return;
    }
    
    let html = '<div class="ingredients-table">';
    html += '<div class="table-header">';
    html += '<span>المنتج</span><span>الكمية المطلوبة</span><span>المتوفر</span><span>الإجراءات</span>';
    html += '</div>';
    
    currentRecipeIngredients.forEach((ingredient, index) => {
        const isAvailable = ingredient.availableStock >= ingredient.quantity;
        html += `<div class="table-row ${!isAvailable ? 'insufficient-stock' : ''}">`;
        html += `<span>${ingredient.productName}</span>`;
        html += `<span>${ingredient.quantity} ${ingredient.productUnit}</span>`;
        html += `<span>${ingredient.availableStock} ${ingredient.productUnit}</span>`;
        html += `<span><button onclick="removeIngredient(${index})" class="delete-btn">حذف</button></span>`;
        html += '</div>';
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// حذف خامة من الوصفة
function removeIngredient(index) {
    currentRecipeIngredients.splice(index, 1);
    displayIngredientsList();
}

// حفظ الوصفة
function saveRecipe() {
    const name = document.getElementById('recipeName').value.trim();
    const quantity = parseFloat(document.getElementById('recipeQuantity').value);
    
    if (!name || !quantity || quantity <= 0) {
        showNotification('يرجى إدخال اسم المنتج والكمية المنتجة', 'error');
        return;
    }
    
    if (currentRecipeIngredients.length === 0) {
        showNotification('يرجى إضافة خامة واحدة على الأقل', 'error');
        return;
    }
    
    const recipe = {
        id: editingRecipeId || generateId(),
        name: name,
        quantity: quantity,
        ingredients: [...currentRecipeIngredients],
        createdAt: editingRecipeId ? recipes.find(r => r.id === editingRecipeId).createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    
    if (editingRecipeId) {
        const index = recipes.findIndex(r => r.id === editingRecipeId);
        recipes[index] = recipe;
        showNotification('تم تحديث الوصفة بنجاح', 'success');
    } else {
        recipes.push(recipe);
        showNotification('تم حفظ الوصفة بنجاح', 'success');
    }
    
    saveData();
    clearRecipeForm();
    displayRecipesList();
    loadProductsToSelect('manufacturingRecipe', 'recipe');
}

// مسح نموذج الوصفة
function clearRecipeForm() {
    document.getElementById('recipeName').value = '';
    document.getElementById('recipeQuantity').value = '';
    currentRecipeIngredients = [];
    editingRecipeId = null;
    displayIngredientsList();
}

// عرض قائمة الوصفات
function displayRecipesList() {
    const container = document.getElementById('recipesList');
    
    if (recipes.length === 0) {
        container.innerHTML = '<p>لا توجد وصفات محفوظة</p>';
        return;
    }
    
    let html = '<div class="recipes-grid">';
    
    recipes.forEach(recipe => {
        html += `<div class="recipe-card">`;
        html += `<h5>${recipe.name}</h5>`;
        html += `<p>الكمية المنتجة: ${recipe.quantity} وحدة</p>`;
        html += `<p>عدد الخامات: ${recipe.ingredients.length}</p>`;
        html += `<div class="recipe-actions">`;
        html += `<button onclick="viewRecipe('${recipe.id}')" class="view-btn">عرض</button>`;
        html += `<button onclick="editRecipe('${recipe.id}')" class="edit-btn">تعديل</button>`;
        html += `<button onclick="deleteRecipe('${recipe.id}')" class="delete-btn">حذف</button>`;
        html += `</div>`;
        html += `</div>`;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// عرض تفاصيل الوصفة
function viewRecipe(recipeId) {
    const recipe = recipes.find(r => r.id === recipeId);
    if (!recipe) return;
    
    let details = `الوصفة: ${recipe.name}\n`;
    details += `الكمية المنتجة: ${recipe.quantity} وحدة\n\n`;
    details += `الخامات المطلوبة:\n`;
    
    recipe.ingredients.forEach(ingredient => {
        details += `- ${ingredient.productName}: ${ingredient.quantity} ${ingredient.productUnit}\n`;
    });
    
    alert(details);
}

// تعديل الوصفة
function editRecipe(recipeId) {
    const recipe = recipes.find(r => r.id === recipeId);
    if (!recipe) return;
    
    editingRecipeId = recipeId;
    document.getElementById('recipeName').value = recipe.name;
    document.getElementById('recipeQuantity').value = recipe.quantity;
    currentRecipeIngredients = [...recipe.ingredients];
    
    displayIngredientsList();
    showNotification('تم تحميل الوصفة للتعديل', 'info');
}

// حذف الوصفة
function deleteRecipe(recipeId) {
    if (!confirm('هل أنت متأكد من حذف هذه الوصفة؟')) return;
    
    const index = recipes.findIndex(r => r.id === recipeId);
    if (index !== -1) {
        recipes.splice(index, 1);
        saveData();
        displayRecipesList();
        loadProductsToSelect('manufacturingRecipe', 'recipe');
        showNotification('تم حذف الوصفة بنجاح', 'success');
    }
}

// معاينة عملية التصنيع
function previewManufacturing() {
    const recipeId = document.getElementById('manufacturingRecipe').value;
    const quantity = parseFloat(document.getElementById('manufacturingQuantity').value);
    
    if (!recipeId || !quantity || quantity <= 0) {
        document.getElementById('manufacturingPreview').innerHTML = '';
        return;
    }
    
    const recipe = recipes.find(r => r.id === recipeId);
    if (!recipe) return;
    
    const multiplier = quantity / recipe.quantity;
    let html = '<div class="manufacturing-preview-content">';
    html += `<h5>معاينة عملية التصنيع</h5>`;
    html += `<p><strong>المنتج النهائي:</strong> ${recipe.name}</p>`;
    html += `<p><strong>الكمية المطلوب إنتاجها:</strong> ${quantity} وحدة</p>`;
    html += `<h6>الخامات المطلوبة:</h6>`;

    let canManufacture = true;
    let totalCost = 0;

    recipe.ingredients.forEach(ingredient => {
        const requiredQuantity = safeNumber(ingredient.quantity * multiplier, 0);
        const product = products.find(p => p.id === ingredient.productId);
        const available = getSafeQuantity(product);
        const costPrice = getSafeCostPrice(product);
        const ingredientCost = requiredQuantity * costPrice;
        const sufficient = available >= requiredQuantity;

        totalCost += ingredientCost;

        if (!sufficient) canManufacture = false;

        html += `<div class="ingredient-preview ${!sufficient ? 'insufficient' : ''}">`;
        html += `${ingredient.productName}: ${requiredQuantity.toFixed(2)} ${ingredient.productUnit} `;
        html += `(متوفر: ${available.toFixed(2)} ${ingredient.productUnit}) `;
        html += `- التكلفة: ${formatCurrency(ingredientCost)}`;
        html += `</div>`;
    });

    // إضافة معلومات التكلفة
    const safeTotalCost = safeNumber(totalCost, 0);
    const safeQuantity = safeNumber(quantity, 1);
    const unitCost = safeTotalCost / safeQuantity;
    const suggestedPrice = unitCost * 1.3;

    html += `<div class="cost-summary">`;
    html += `<h6>ملخص التكلفة:</h6>`;
    html += `<p><strong>إجمالي تكلفة الخامات:</strong> ${formatCurrency(safeTotalCost)}</p>`;
    html += `<p><strong>تكلفة الوحدة الواحدة:</strong> ${formatCurrency(unitCost)}</p>`;
    html += `<p><strong>سعر البيع المقترح (هامش 30%):</strong> ${formatCurrency(suggestedPrice)}</p>`;
    html += `</div>`;
    
    if (!canManufacture) {
        html += '<div class="warning">⚠️ لا يمكن إتمام عملية التصنيع - خامات غير كافية</div>';
    }
    
    html += '</div>';
    document.getElementById('manufacturingPreview').innerHTML = html;
}

// بدء عملية التصنيع
function startManufacturing() {
    const recipeId = document.getElementById('manufacturingRecipe').value;
    const quantity = parseFloat(document.getElementById('manufacturingQuantity').value);
    const date = document.getElementById('manufacturingDate').value;
    const employeeId = document.getElementById('manufacturingEmployee').value;
    
    if (!recipeId || !quantity || quantity <= 0 || !date || !employeeId) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    const recipe = recipes.find(r => r.id === recipeId);
    if (!recipe) {
        showNotification('الوصفة غير موجودة', 'error');
        return;
    }
    
    const multiplier = quantity / recipe.quantity;
    
    // التحقق من توفر الخامات
    for (let ingredient of recipe.ingredients) {
        const requiredQuantity = ingredient.quantity * multiplier;
        const product = products.find(p => p.id === ingredient.productId);
        
        if (!product || product.stockQuantity < requiredQuantity) {
            showNotification(`خامة غير كافية: ${ingredient.productName}`, 'error');
            return;
        }
    }
    
    // حساب التكلفة الإجمالية
    let totalCost = 0;
    const ingredientsWithCost = recipe.ingredients.map(ingredient => {
        const requiredQuantity = safeNumber(ingredient.quantity * multiplier, 0);
        const product = products.find(p => p.id === ingredient.productId);
        const costPrice = getSafeCostPrice(product);
        const ingredientCost = requiredQuantity * costPrice;
        totalCost += ingredientCost;

        return {
            ...ingredient,
            usedQuantity: requiredQuantity,
            costPrice: costPrice,
            totalCost: safeNumber(ingredientCost, 0)
        };
    });

    // خصم الخامات من المخزون
    recipe.ingredients.forEach(ingredient => {
        const requiredQuantity = ingredient.quantity * multiplier;
        updateProductStock(ingredient.productId, requiredQuantity, 'subtract');
    });

    // إضافة المنتج النهائي للمخزون (إذا كان موجوداً)
    const finalProduct = products.find(p => p.name === recipe.name);
    if (finalProduct) {
        updateProductStock(finalProduct.id, quantity, 'add');

        // تحديث سعر التكلفة للمنتج النهائي
        const safeTotalCost = safeNumber(totalCost, 0);
        const safeQuantity = safeNumber(quantity, 1);
        const unitCost = safeTotalCost / safeQuantity;
        finalProduct.costPrice = safeNumber(unitCost, 0);

        // اقتراح سعر بيع جديد إذا لم يكن محدداً
        const currentSellingPrice = getSafeSellingPrice(finalProduct);
        if (currentSellingPrice === 0) {
            finalProduct.sellingPrice = safeNumber(unitCost * 1.3, 0); // هامش ربح 30%
        }
    }

    // حفظ عملية التصنيع
    const manufacturingRecord = {
        id: generateId(),
        recipeId: recipeId,
        recipeName: recipe.name,
        quantity: quantity,
        date: date,
        employeeId: employeeId,
        employeeName: employees.find(e => e.id === employeeId)?.name || 'غير محدد',
        ingredients: ingredientsWithCost,
        totalCost: safeNumber(totalCost, 0),
        unitCost: safeNumber(totalCost / quantity, 0),
        createdAt: new Date().toISOString()
    };
    
    manufacturing.push(manufacturingRecord);
    saveData();
    
    clearManufacturingForm();
    displayManufacturingHistory();
    displayProducts();
    displayProductsList();
    
    showNotification('تم إتمام عملية التصنيع بنجاح', 'success');
}

// مسح نموذج التصنيع
function clearManufacturingForm() {
    document.getElementById('manufacturingRecipe').value = '';
    document.getElementById('manufacturingQuantity').value = '';
    document.getElementById('manufacturingEmployee').value = '';
    document.getElementById('manufacturingPreview').innerHTML = '';
}

// عرض سجل عمليات التصنيع
function displayManufacturingHistory() {
    const container = document.getElementById('manufacturingHistory');
    
    if (manufacturing.length === 0) {
        container.innerHTML = '<p>لا توجد عمليات تصنيع مسجلة</p>';
        return;
    }
    
    let html = '<div class="manufacturing-table">';
    html += '<div class="table-header">';
    html += '<span>التاريخ</span><span>المنتج</span><span>الكمية</span><span>التكلفة الإجمالية</span><span>تكلفة الوحدة</span><span>الموظف</span><span>الإجراءات</span>';
    html += '</div>';

    manufacturing.slice().reverse().forEach(record => {
        html += `<div class="table-row">`;
        html += `<span>${new Date(record.date).toLocaleDateString('ar-SA')}</span>`;
        html += `<span>${record.recipeName}</span>`;
        html += `<span>${record.quantity} وحدة</span>`;
        html += `<span>${formatCurrency(record.totalCost)}</span>`;
        html += `<span>${formatCurrency(record.unitCost)}</span>`;
        html += `<span>${record.employeeName}</span>`;
        html += `<span>`;
        html += `<button onclick="viewManufacturingDetails('${record.id}')" class="view-btn">تفاصيل</button>`;
        html += `<button onclick="deleteManufacturingRecord('${record.id}')" class="delete-btn">حذف</button>`;
        html += `</span>`;
        html += '</div>';
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// عرض تفاصيل عملية التصنيع
function viewManufacturingDetails(recordId) {
    const record = manufacturing.find(m => m.id === recordId);
    if (!record) return;
    
    let details = `عملية التصنيع\n`;
    details += `التاريخ: ${new Date(record.date).toLocaleDateString('ar-SA')}\n`;
    details += `المنتج النهائي: ${record.recipeName}\n`;
    details += `الكمية المنتجة: ${record.quantity} وحدة\n`;
    details += `الموظف المسئول: ${record.employeeName}\n`;

    if (record.totalCost !== undefined && record.totalCost !== null) {
        details += `إجمالي التكلفة: ${formatCurrency(record.totalCost)}\n`;
        details += `تكلفة الوحدة: ${formatCurrency(record.unitCost)}\n`;
    }

    details += `\nالخامات المستخدمة:\n`;

    record.ingredients.forEach(ingredient => {
        const usedQty = safeNumber(ingredient.usedQuantity, 0);
        details += `- ${ingredient.productName}: ${usedQty.toFixed(2)} ${ingredient.productUnit}`;
        if (ingredient.totalCost !== undefined && ingredient.totalCost !== null) {
            details += ` - التكلفة: ${formatCurrency(ingredient.totalCost)}`;
        }
        details += `\n`;
    });
    
    alert(details);
}

// حذف سجل التصنيع
function deleteManufacturingRecord(recordId) {
    if (!confirm('هل أنت متأكد من حذف هذا السجل؟')) return;
    
    const index = manufacturing.findIndex(m => m.id === recordId);
    if (index !== -1) {
        manufacturing.splice(index, 1);
        saveData();
        displayManufacturingHistory();
        showNotification('تم حذف السجل بنجاح', 'success');
    }
}

// ===== إدارة Excel =====

// متغيرات Excel
let excelWorkbook = null;
let excelSheets = {};
let currentSheetName = '';
let excelData = [];
let excelHeaders = [];

// استيراد ملف Excel
function importExcelFile() {
    const fileInput = document.getElementById('excelFileInput');
    const file = fileInput.files[0];

    if (!file) {
        showNotification('يرجى اختيار ملف Excel أولاً', 'error');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = new Uint8Array(e.target.result);
            excelWorkbook = XLSX.read(data, { type: 'array' });

            // تحميل جميع أوراق العمل
            excelSheets = {};
            excelWorkbook.SheetNames.forEach(sheetName => {
                const worksheet = excelWorkbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                excelSheets[sheetName] = {
                    headers: jsonData.length > 0 ? (jsonData[0] || []) : [],
                    data: jsonData.length > 0 ? jsonData.slice(1) : [],
                    originalData: jsonData
                };
            });

            // عرض أوراق العمل
            displaySheetTabs();

            // تحديد الورقة الأولى كافتراضية
            if (excelWorkbook.SheetNames.length > 0) {
                switchToSheet(excelWorkbook.SheetNames[0]);
                showNotification(`تم استيراد ملف Excel بنجاح (${excelWorkbook.SheetNames.length} ورقة عمل)`, 'success');
            } else {
                showNotification('الملف فارغ أو غير صالح', 'error');
            }
        } catch (error) {
            console.error('خطأ في قراءة ملف Excel:', error);
            showNotification('خطأ في قراءة ملف Excel. تأكد من صحة الملف.', 'error');
        }
    };

    reader.readAsArrayBuffer(file);
}

// عرض تبويبات أوراق العمل
function displaySheetTabs() {
    const tabsContainer = document.getElementById('excelSheetTabs');

    if (!excelWorkbook || excelWorkbook.SheetNames.length === 0) {
        tabsContainer.innerHTML = '';
        return;
    }

    let tabsHtml = '';
    excelWorkbook.SheetNames.forEach((sheetName, index) => {
        const isActive = sheetName === currentSheetName;
        const rowCount = excelSheets[sheetName].data.length;
        const colCount = excelSheets[sheetName].headers.length;

        tabsHtml += `
            <div class="sheet-tab ${isActive ? 'active' : ''}" onclick="switchToSheet('${sheetName}')">
                <span class="sheet-name">${sheetName}</span>
                <span class="sheet-size">(${rowCount} × ${colCount})</span>
            </div>
        `;
    });

    tabsContainer.innerHTML = tabsHtml;
}

// التبديل إلى ورقة عمل محددة
function switchToSheet(sheetName) {
    if (!excelSheets[sheetName]) {
        showNotification('ورقة العمل غير موجودة', 'error');
        return;
    }

    currentSheetName = sheetName;
    excelHeaders = [...excelSheets[sheetName].headers];
    excelData = excelSheets[sheetName].data.map(row => [...row]);

    displaySheetTabs(); // تحديث التبويبات
    displayExcelData();
    updateSheetInfo();
}

// تحديث معلومات الورقة الحالية
function updateSheetInfo() {
    const sheetNameElement = document.getElementById('currentSheetName');
    const dimensionsElement = document.getElementById('sheetDimensions');

    if (currentSheetName) {
        sheetNameElement.textContent = `الورقة الحالية: ${currentSheetName}`;
        dimensionsElement.textContent = `الأبعاد: ${excelData.length} صف × ${excelHeaders.length} عمود`;
    } else {
        sheetNameElement.textContent = 'لا توجد أوراق';
        dimensionsElement.textContent = '';
    }
}

// عرض بيانات Excel
function displayExcelData() {
    const container = document.getElementById('excelDataContainer');
    const tableHead = document.getElementById('excelTableHead');
    const tableBody = document.getElementById('excelTableBody');

    if (excelHeaders.length === 0) {
        container.style.display = 'none';
        return;
    }

    container.style.display = 'block';

    // إنشاء رأس الجدول
    let headHtml = '<tr>';
    excelHeaders.forEach((header, index) => {
        headHtml += `<th contenteditable="true" onblur="updateHeader(${index}, this.textContent)">${header || `عمود ${index + 1}`}</th>`;
    });
    headHtml += '<th>الإجراءات</th></tr>';
    tableHead.innerHTML = headHtml;

    // إنشاء صفوف البيانات
    let bodyHtml = '';
    excelData.forEach((row, rowIndex) => {
        bodyHtml += '<tr>';
        excelHeaders.forEach((header, colIndex) => {
            const cellValue = row[colIndex] || '';
            bodyHtml += `<td contenteditable="true" onblur="updateCell(${rowIndex}, ${colIndex}, this.textContent)">${cellValue}</td>`;
        });
        bodyHtml += `<td><button onclick="deleteExcelRow(${rowIndex})" class="delete-btn">حذف</button></td>`;
        bodyHtml += '</tr>';
    });
    tableBody.innerHTML = bodyHtml;
}

// تحديث رأس العمود
function updateHeader(index, newValue) {
    excelHeaders[index] = newValue;
}

// تحديث خلية
function updateCell(rowIndex, colIndex, newValue) {
    if (!excelData[rowIndex]) {
        excelData[rowIndex] = [];
    }
    excelData[rowIndex][colIndex] = newValue;
}

// إضافة صف جديد
function addExcelRow() {
    if (excelHeaders.length === 0) {
        // إنشاء رؤوس افتراضية
        excelHeaders = ['العمود 1', 'العمود 2', 'العمود 3'];
    }

    const newRow = new Array(excelHeaders.length).fill('');
    excelData.push(newRow);
    displayExcelData();
}

// حذف صف
function deleteExcelRow(rowIndex) {
    if (confirm('هل أنت متأكد من حذف هذا الصف؟')) {
        excelData.splice(rowIndex, 1);
        displayExcelData();
    }
}

// حفظ التغييرات
function saveExcelChanges() {
    // يمكن إضافة منطق حفظ البيانات هنا
    showNotification('تم حفظ التغييرات بنجاح', 'success');
}

// تصدير إلى Excel
function exportToExcel() {
    if (excelHeaders.length === 0 && excelData.length === 0) {
        showNotification('لا توجد بيانات للتصدير', 'error');
        return;
    }

    const ws = XLSX.utils.aoa_to_sheet([excelHeaders, ...excelData]);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'البيانات');

    const fileName = `manufacturing_data_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);

    showNotification('تم تصدير الملف بنجاح', 'success');
}

// مسح البيانات
function clearExcelData() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
        excelData = [];
        excelHeaders = [];
        document.getElementById('excelDataContainer').style.display = 'none';
        document.getElementById('excelFileInput').value = '';
        showNotification('تم مسح البيانات', 'info');
    }
}

// إضافة مستمع للتغيير في حقول التصنيع
document.addEventListener('DOMContentLoaded', function() {
    const manufacturingRecipe = document.getElementById('manufacturingRecipe');
    const manufacturingQuantity = document.getElementById('manufacturingQuantity');

    if (manufacturingRecipe) {
        manufacturingRecipe.addEventListener('change', previewManufacturing);
    }

    if (manufacturingQuantity) {
        manufacturingQuantity.addEventListener('input', previewManufacturing);
    }
});
