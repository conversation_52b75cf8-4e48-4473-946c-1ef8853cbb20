// ===== إدارة الهالك والأصناف التالفة =====

// تهيئة صفحة الهالك
function initializeWastage() {
    loadProductsToWastageSelect();
    loadEmployeesToWastageSelect();
    displayWastageList();
    generateWastageReport();
    
    // تعيين التاريخ الحالي
    document.getElementById('wastageDate').value = new Date().toISOString().split('T')[0];
}

// تحميل المنتجات في قائمة الهالك
function loadProductsToWastageSelect() {
    const select = document.getElementById('wastageProduct');
    select.innerHTML = '<option value="">اختر الصنف</option>';
    
    products.forEach(product => {
        if (product.stockQuantity > 0) {
            const option = document.createElement('option');
            option.value = product.id;
            option.textContent = `${product.name} - متوفر: ${product.stockQuantity} ${product.unit}`;
            select.appendChild(option);
        }
    });
}

// تحميل الموظفين في قائمة الهالك
function loadEmployeesToWastageSelect() {
    const select = document.getElementById('wastageEmployee');
    select.innerHTML = '<option value="">الموظف المسئول</option>';
    
    employees.forEach(employee => {
        if (employee.status === 'active') {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = employee.name;
            select.appendChild(option);
        }
    });
}

// حفظ الهالك
function saveWastage() {
    const productId = document.getElementById('wastageProduct').value;
    const quantity = parseFloat(document.getElementById('wastageQuantity').value);
    const reason = document.getElementById('wastageReason').value;
    const date = document.getElementById('wastageDate').value;
    const employeeId = document.getElementById('wastageEmployee').value;
    const notes = document.getElementById('wastageNotes').value.trim();
    
    // التحقق من صحة البيانات
    if (!productId || !quantity || quantity <= 0 || !reason || !date || !employeeId) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    const product = products.find(p => p.id === productId);
    if (!product) {
        showNotification('المنتج غير موجود', 'error');
        return;
    }
    
    if (product.stockQuantity < quantity) {
        showNotification(`الكمية المتوفرة غير كافية. المتوفر: ${product.stockQuantity} ${product.unit}`, 'error');
        return;
    }
    
    const employee = employees.find(e => e.id === employeeId);
    if (!employee) {
        showNotification('الموظف غير موجود', 'error');
        return;
    }
    
    // إنشاء سجل الهالك
    const wastageRecord = {
        id: generateId(),
        productId: productId,
        productName: product.name,
        productUnit: product.unit,
        quantity: quantity,
        reason: reason,
        date: date,
        employeeId: employeeId,
        employeeName: employee.name,
        notes: notes,
        costValue: safeNumber(quantity * getSafeCostPrice(product), 0), // قيمة الهالك بسعر التكلفة
        createdAt: new Date().toISOString()
    };
    
    // خصم الكمية من المخزون
    updateProductStock(productId, quantity, 'subtract');
    
    // حفظ سجل الهالك
    wastage.push(wastageRecord);
    saveData();
    
    // تحديث العرض
    clearWastageForm();
    displayWastageList();
    generateWastageReport();
    loadProductsToWastageSelect();
    displayProducts();
    displayProductsList();
    
    showNotification('تم تسجيل الهالك بنجاح وخصم الكمية من المخزون', 'success');
}

// مسح نموذج الهالك
function clearWastageForm() {
    document.getElementById('wastageProduct').value = '';
    document.getElementById('wastageQuantity').value = '';
    document.getElementById('wastageReason').value = '';
    document.getElementById('wastageEmployee').value = '';
    document.getElementById('wastageNotes').value = '';
}

// عرض قائمة الهالك
function displayWastageList() {
    const container = document.getElementById('wastageList');
    
    if (wastage.length === 0) {
        container.innerHTML = '<p>لا توجد سجلات هالك</p>';
        return;
    }
    
    let html = '<div class="wastage-table">';
    html += '<div class="table-header">';
    html += '<span>التاريخ</span><span>الصنف</span><span>الكمية</span><span>السبب</span><span>الموظف</span><span>القيمة</span><span>الإجراءات</span>';
    html += '</div>';
    
    wastage.slice().reverse().forEach(record => {
        html += `<div class="table-row">`;
        html += `<span>${new Date(record.date).toLocaleDateString('ar-SA')}</span>`;
        html += `<span>${record.productName}</span>`;
        html += `<span>${record.quantity} ${record.productUnit}</span>`;
        html += `<span>${record.reason}</span>`;
        html += `<span>${record.employeeName}</span>`;
        html += `<span>${formatCurrency(record.costValue)}</span>`;
        html += `<span>`;
        html += `<button onclick="viewWastageDetails('${record.id}')" class="view-btn">تفاصيل</button>`;
        html += `<button onclick="deleteWastageRecord('${record.id}')" class="delete-btn">حذف</button>`;
        html += `</span>`;
        html += '</div>';
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// فلترة الهالك
function filterWastage() {
    const startDate = document.getElementById('wastageFilterStart').value;
    const endDate = document.getElementById('wastageFilterEnd').value;
    const reason = document.getElementById('wastageFilterReason').value;
    
    let filteredWastage = [...wastage];
    
    if (startDate) {
        filteredWastage = filteredWastage.filter(w => w.date >= startDate);
    }
    
    if (endDate) {
        filteredWastage = filteredWastage.filter(w => w.date <= endDate);
    }
    
    if (reason) {
        filteredWastage = filteredWastage.filter(w => w.reason === reason);
    }
    
    displayFilteredWastage(filteredWastage);
}

// عرض الهالك المفلتر
function displayFilteredWastage(filteredWastage) {
    const container = document.getElementById('wastageList');
    
    if (filteredWastage.length === 0) {
        container.innerHTML = '<p>لا توجد سجلات تطابق الفلترة</p>';
        return;
    }
    
    let html = '<div class="wastage-table">';
    html += '<div class="table-header">';
    html += '<span>التاريخ</span><span>الصنف</span><span>الكمية</span><span>السبب</span><span>الموظف</span><span>القيمة</span><span>الإجراءات</span>';
    html += '</div>';
    
    filteredWastage.slice().reverse().forEach(record => {
        html += `<div class="table-row">`;
        html += `<span>${new Date(record.date).toLocaleDateString('ar-SA')}</span>`;
        html += `<span>${record.productName}</span>`;
        html += `<span>${record.quantity} ${record.productUnit}</span>`;
        html += `<span>${record.reason}</span>`;
        html += `<span>${record.employeeName}</span>`;
        html += `<span>${formatCurrency(record.costValue)}</span>`;
        html += `<span>`;
        html += `<button onclick="viewWastageDetails('${record.id}')" class="view-btn">تفاصيل</button>`;
        html += `<button onclick="deleteWastageRecord('${record.id}')" class="delete-btn">حذف</button>`;
        html += `</span>`;
        html += '</div>';
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// عرض تفاصيل الهالك
function viewWastageDetails(recordId) {
    const record = wastage.find(w => w.id === recordId);
    if (!record) return;
    
    let details = `تفاصيل الهالك\n`;
    details += `التاريخ: ${new Date(record.date).toLocaleDateString('ar-SA')}\n`;
    details += `الصنف: ${record.productName}\n`;
    details += `الكمية: ${record.quantity} ${record.productUnit}\n`;
    details += `السبب: ${record.reason}\n`;
    details += `الموظف المسئول: ${record.employeeName}\n`;
    details += `قيمة الهالك: ${formatCurrency(record.costValue)}\n`;
    
    if (record.notes) {
        details += `الملاحظات: ${record.notes}\n`;
    }
    
    details += `تاريخ التسجيل: ${new Date(record.createdAt).toLocaleString('ar-SA')}`;
    
    alert(details);
}

// حذف سجل الهالك
function deleteWastageRecord(recordId) {
    if (!confirm('هل أنت متأكد من حذف هذا السجل؟\nملاحظة: لن يتم إرجاع الكمية للمخزون')) return;
    
    const index = wastage.findIndex(w => w.id === recordId);
    if (index !== -1) {
        wastage.splice(index, 1);
        saveData();
        displayWastageList();
        generateWastageReport();
        showNotification('تم حذف السجل بنجاح', 'success');
    }
}

// إنشاء تقرير الهالك
function generateWastageReport() {
    const container = document.getElementById('wastageReport');
    
    if (wastage.length === 0) {
        container.innerHTML = '<p>لا توجد بيانات لإنشاء التقرير</p>';
        return;
    }
    
    // حساب الإحصائيات
    const totalWastageValue = wastage.reduce((sum, w) => sum + safeNumber(w.costValue, 0), 0);
    const wastageByReason = {};
    const wastageByProduct = {};

    wastage.forEach(w => {
        const costValue = safeNumber(w.costValue, 0);
        const quantity = safeNumber(w.quantity, 0);

        // تجميع حسب السبب
        if (!wastageByReason[w.reason]) {
            wastageByReason[w.reason] = { count: 0, value: 0 };
        }
        wastageByReason[w.reason].count++;
        wastageByReason[w.reason].value += costValue;

        // تجميع حسب المنتج
        if (!wastageByProduct[w.productName]) {
            wastageByProduct[w.productName] = { quantity: 0, value: 0, unit: w.productUnit };
        }
        wastageByProduct[w.productName].quantity += quantity;
        wastageByProduct[w.productName].value += costValue;
    });
    
    let html = '<div class="wastage-report-content">';
    
    // الإحصائيات العامة
    html += '<div class="report-section">';
    html += '<h5>الإحصائيات العامة</h5>';
    html += `<p><strong>إجمالي عدد السجلات:</strong> ${wastage.length}</p>`;
    html += `<p><strong>إجمالي قيمة الهالك:</strong> ${formatCurrency(totalWastageValue)}</p>`;
    html += '</div>';
    
    // الهالك حسب السبب
    html += '<div class="report-section">';
    html += '<h5>الهالك حسب السبب</h5>';
    html += '<div class="reason-stats">';
    Object.entries(wastageByReason).forEach(([reason, data]) => {
        html += `<div class="stat-item">`;
        html += `<span>${reason}:</span>`;
        html += `<span>${data.count} حالة - ${formatCurrency(data.value)}</span>`;
        html += `</div>`;
    });
    html += '</div>';
    html += '</div>';
    
    // الهالك حسب المنتج
    html += '<div class="report-section">';
    html += '<h5>الهالك حسب المنتج</h5>';
    html += '<div class="product-stats">';
    Object.entries(wastageByProduct).forEach(([productName, data]) => {
        html += `<div class="stat-item">`;
        html += `<span>${productName}:</span>`;
        html += `<span>${safeNumber(data.quantity, 0).toFixed(2)} ${data.unit} - ${formatCurrency(data.value)}</span>`;
        html += `</div>`;
    });
    html += '</div>';
    html += '</div>';
    
    html += '</div>';
    container.innerHTML = html;
}

// حساب إجمالي قيمة الهالك لفترة معينة
function calculateWastageForPeriod(startDate, endDate) {
    return wastage
        .filter(w => w.date >= startDate && w.date <= endDate)
        .reduce((sum, w) => sum + w.costValue, 0);
}
