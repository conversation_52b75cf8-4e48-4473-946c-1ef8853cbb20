// ===== إدارة الحضور والانصراف =====

// تهيئة صفحة الحضور والانصراف
function initializeAttendance() {
    loadEmployeesToAttendanceSelect();
    loadEmployeesToAttendanceSelect('attendanceFilterEmployee');
    displayAttendanceList();
    generateWorkHoursReport();
    
    // تعيين التاريخ الحالي
    document.getElementById('attendanceDate').value = new Date().toISOString().split('T')[0];
}

// تحميل الموظفين في قائمة الحضور
function loadEmployeesToAttendanceSelect(selectId = 'attendanceEmployee') {
    const select = document.getElementById(selectId);
    if (!select) return;
    
    if (selectId === 'attendanceFilterEmployee') {
        select.innerHTML = '<option value="">جميع الموظفين</option>';
    } else {
        select.innerHTML = '<option value="">اختر الموظف</option>';
    }
    
    employees.forEach(employee => {
        if (employee.status === 'active') {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = employee.name;
            select.appendChild(option);
        }
    });
}

// تسجيل حضور سريع
function quickCheckIn() {
    const employeeId = document.getElementById('attendanceEmployee').value;
    
    if (!employeeId) {
        showNotification('يرجى اختيار الموظف أولاً', 'error');
        return;
    }
    
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5);
    const currentDate = now.toISOString().split('T')[0];
    
    document.getElementById('attendanceDate').value = currentDate;
    document.getElementById('checkInTime').value = currentTime;
    
    showNotification('تم تسجيل وقت الحضور', 'success');
}

// تسجيل انصراف سريع
function quickCheckOut() {
    const employeeId = document.getElementById('attendanceEmployee').value;
    
    if (!employeeId) {
        showNotification('يرجى اختيار الموظف أولاً', 'error');
        return;
    }
    
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5);
    
    document.getElementById('checkOutTime').value = currentTime;
    
    showNotification('تم تسجيل وقت الانصراف', 'success');
}

// حفظ سجل الحضور والانصراف
function saveAttendance() {
    const employeeId = document.getElementById('attendanceEmployee').value;
    const date = document.getElementById('attendanceDate').value;
    const checkInTime = document.getElementById('checkInTime').value;
    const checkOutTime = document.getElementById('checkOutTime').value;
    const notes = document.getElementById('attendanceNotes').value.trim();
    
    // التحقق من صحة البيانات
    if (!employeeId || !date || !checkInTime) {
        showNotification('يرجى ملء الحقول المطلوبة (الموظف، التاريخ، وقت الحضور)', 'error');
        return;
    }
    
    const employee = employees.find(e => e.id === employeeId);
    if (!employee) {
        showNotification('الموظف غير موجود', 'error');
        return;
    }
    
    // التحقق من عدم تكرار السجل لنفس الموظف في نفس اليوم
    const existingRecord = attendance.find(a => a.employeeId === employeeId && a.date === date);
    if (existingRecord) {
        showNotification('يوجد سجل حضور لهذا الموظف في نفس التاريخ', 'error');
        return;
    }
    
    // حساب ساعات العمل
    let workHours = 0;
    let isLate = false;
    let isEarlyLeave = false;
    
    if (checkInTime && checkOutTime) {
        const checkIn = new Date(`2000-01-01T${checkInTime}:00`);
        const checkOut = new Date(`2000-01-01T${checkOutTime}:00`);
        
        if (checkOut > checkIn) {
            workHours = (checkOut - checkIn) / (1000 * 60 * 60); // تحويل إلى ساعات
        }
        
        // التحقق من التأخير (افتراض بداية العمل 8:00 صباحاً)
        const standardStartTime = new Date(`2000-01-01T08:00:00`);
        if (checkIn > standardStartTime) {
            isLate = true;
        }
        
        // التحقق من الانصراف المبكر (افتراض نهاية العمل 5:00 مساءً)
        const standardEndTime = new Date(`2000-01-01T17:00:00`);
        if (checkOut < standardEndTime && checkOutTime) {
            isEarlyLeave = true;
        }
    }
    
    // إنشاء سجل الحضور
    const attendanceRecord = {
        id: generateId(),
        employeeId: employeeId,
        employeeName: employee.name,
        date: date,
        checkInTime: checkInTime,
        checkOutTime: checkOutTime || null,
        workHours: workHours,
        isLate: isLate,
        isEarlyLeave: isEarlyLeave,
        notes: notes,
        createdAt: new Date().toISOString()
    };
    
    attendance.push(attendanceRecord);
    saveData();
    
    clearAttendanceForm();
    displayAttendanceList();
    generateWorkHoursReport();
    
    showNotification('تم حفظ سجل الحضور والانصراف بنجاح', 'success');
}

// مسح نموذج الحضور
function clearAttendanceForm() {
    document.getElementById('attendanceEmployee').value = '';
    document.getElementById('checkInTime').value = '';
    document.getElementById('checkOutTime').value = '';
    document.getElementById('attendanceNotes').value = '';
}

// عرض قائمة الحضور والانصراف
function displayAttendanceList() {
    const container = document.getElementById('attendanceList');
    
    if (attendance.length === 0) {
        container.innerHTML = '<p>لا توجد سجلات حضور وانصراف</p>';
        return;
    }
    
    let html = '<div class="attendance-table">';
    html += '<div class="table-header">';
    html += '<span>التاريخ</span><span>الموظف</span><span>الحضور</span><span>الانصراف</span><span>ساعات العمل</span><span>الحالة</span><span>الإجراءات</span>';
    html += '</div>';
    
    attendance.slice().reverse().forEach(record => {
        let statusClass = '';
        let statusText = 'عادي';
        
        if (record.isLate && record.isEarlyLeave) {
            statusClass = 'late-early';
            statusText = 'تأخير وانصراف مبكر';
        } else if (record.isLate) {
            statusClass = 'late';
            statusText = 'تأخير';
        } else if (record.isEarlyLeave) {
            statusClass = 'early';
            statusText = 'انصراف مبكر';
        }
        
        html += `<div class="table-row ${statusClass}">`;
        html += `<span>${new Date(record.date).toLocaleDateString('ar-SA')}</span>`;
        html += `<span>${record.employeeName}</span>`;
        html += `<span>${record.checkInTime}</span>`;
        html += `<span>${record.checkOutTime || 'لم ينصرف'}</span>`;
        html += `<span>${record.workHours.toFixed(2)} ساعة</span>`;
        html += `<span class="${statusClass}">${statusText}</span>`;
        html += `<span>`;
        html += `<button onclick="editAttendanceRecord('${record.id}')" class="edit-btn">تعديل</button>`;
        html += `<button onclick="deleteAttendanceRecord('${record.id}')" class="delete-btn">حذف</button>`;
        html += `</span>`;
        html += '</div>';
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// فلترة الحضور والانصراف
function filterAttendance() {
    const employeeId = document.getElementById('attendanceFilterEmployee').value;
    const startDate = document.getElementById('attendanceFilterStart').value;
    const endDate = document.getElementById('attendanceFilterEnd').value;
    
    let filteredAttendance = [...attendance];
    
    if (employeeId) {
        filteredAttendance = filteredAttendance.filter(a => a.employeeId === employeeId);
    }
    
    if (startDate) {
        filteredAttendance = filteredAttendance.filter(a => a.date >= startDate);
    }
    
    if (endDate) {
        filteredAttendance = filteredAttendance.filter(a => a.date <= endDate);
    }
    
    displayFilteredAttendance(filteredAttendance);
}

// عرض الحضور المفلتر
function displayFilteredAttendance(filteredAttendance) {
    const container = document.getElementById('attendanceList');
    
    if (filteredAttendance.length === 0) {
        container.innerHTML = '<p>لا توجد سجلات تطابق الفلترة</p>';
        return;
    }
    
    let html = '<div class="attendance-table">';
    html += '<div class="table-header">';
    html += '<span>التاريخ</span><span>الموظف</span><span>الحضور</span><span>الانصراف</span><span>ساعات العمل</span><span>الحالة</span><span>الإجراءات</span>';
    html += '</div>';
    
    filteredAttendance.slice().reverse().forEach(record => {
        let statusClass = '';
        let statusText = 'عادي';
        
        if (record.isLate && record.isEarlyLeave) {
            statusClass = 'late-early';
            statusText = 'تأخير وانصراف مبكر';
        } else if (record.isLate) {
            statusClass = 'late';
            statusText = 'تأخير';
        } else if (record.isEarlyLeave) {
            statusClass = 'early';
            statusText = 'انصراف مبكر';
        }
        
        html += `<div class="table-row ${statusClass}">`;
        html += `<span>${new Date(record.date).toLocaleDateString('ar-SA')}</span>`;
        html += `<span>${record.employeeName}</span>`;
        html += `<span>${record.checkInTime}</span>`;
        html += `<span>${record.checkOutTime || 'لم ينصرف'}</span>`;
        html += `<span>${record.workHours.toFixed(2)} ساعة</span>`;
        html += `<span class="${statusClass}">${statusText}</span>`;
        html += `<span>`;
        html += `<button onclick="editAttendanceRecord('${record.id}')" class="edit-btn">تعديل</button>`;
        html += `<button onclick="deleteAttendanceRecord('${record.id}')" class="delete-btn">حذف</button>`;
        html += `</span>`;
        html += '</div>';
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// تعديل سجل الحضور
function editAttendanceRecord(recordId) {
    const record = attendance.find(a => a.id === recordId);
    if (!record) return;
    
    document.getElementById('attendanceEmployee').value = record.employeeId;
    document.getElementById('attendanceDate').value = record.date;
    document.getElementById('checkInTime').value = record.checkInTime;
    document.getElementById('checkOutTime').value = record.checkOutTime || '';
    document.getElementById('attendanceNotes').value = record.notes || '';
    
    // حذف السجل القديم ليتم استبداله بالجديد
    deleteAttendanceRecord(recordId, false);
    
    showNotification('تم تحميل السجل للتعديل', 'info');
}

// حذف سجل الحضور
function deleteAttendanceRecord(recordId, showConfirm = true) {
    if (showConfirm && !confirm('هل أنت متأكد من حذف هذا السجل؟')) return;
    
    const index = attendance.findIndex(a => a.id === recordId);
    if (index !== -1) {
        attendance.splice(index, 1);
        saveData();
        displayAttendanceList();
        generateWorkHoursReport();
        
        if (showConfirm) {
            showNotification('تم حذف السجل بنجاح', 'success');
        }
    }
}

// إنشاء تقرير ساعات العمل
function generateWorkHoursReport() {
    const container = document.getElementById('workHoursReport');
    
    if (attendance.length === 0) {
        container.innerHTML = '<p>لا توجد بيانات لإنشاء التقرير</p>';
        return;
    }
    
    // تجميع البيانات حسب الموظف
    const employeeStats = {};
    
    attendance.forEach(record => {
        if (!employeeStats[record.employeeId]) {
            employeeStats[record.employeeId] = {
                name: record.employeeName,
                totalHours: 0,
                totalDays: 0,
                lateDays: 0,
                earlyLeaveDays: 0
            };
        }
        
        const stats = employeeStats[record.employeeId];
        stats.totalHours += record.workHours;
        stats.totalDays++;
        
        if (record.isLate) stats.lateDays++;
        if (record.isEarlyLeave) stats.earlyLeaveDays++;
    });
    
    let html = '<div class="work-hours-report-content">';
    html += '<h5>تقرير ساعات العمل</h5>';
    
    html += '<div class="employees-stats">';
    Object.values(employeeStats).forEach(stats => {
        const avgHours = stats.totalHours / stats.totalDays;
        
        html += `<div class="employee-stat-card">`;
        html += `<h6>${stats.name}</h6>`;
        html += `<div class="stat-grid">`;
        html += `<div class="stat-item">`;
        html += `<span>إجمالي الأيام:</span>`;
        html += `<span>${stats.totalDays}</span>`;
        html += `</div>`;
        html += `<div class="stat-item">`;
        html += `<span>إجمالي الساعات:</span>`;
        html += `<span>${stats.totalHours.toFixed(2)} ساعة</span>`;
        html += `</div>`;
        html += `<div class="stat-item">`;
        html += `<span>متوسط الساعات:</span>`;
        html += `<span>${avgHours.toFixed(2)} ساعة/يوم</span>`;
        html += `</div>`;
        html += `<div class="stat-item">`;
        html += `<span>أيام التأخير:</span>`;
        html += `<span>${stats.lateDays}</span>`;
        html += `</div>`;
        html += `<div class="stat-item">`;
        html += `<span>أيام الانصراف المبكر:</span>`;
        html += `<span>${stats.earlyLeaveDays}</span>`;
        html += `</div>`;
        html += `</div>`;
        html += `</div>`;
    });
    html += '</div>';
    
    html += '</div>';
    container.innerHTML = html;
}
