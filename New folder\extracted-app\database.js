// ===== إدارة قاعدة البيانات =====

// تعريف هياكل البيانات

// 1. المنتجات (Products)
function createProduct(name, code, unit, stockQuantity, costPrice, sellPrice) {
    return {
        id: generateId(),
        name: sanitizeInput(name),
        code: sanitizeInput(code),
        unit: sanitizeInput(unit),
        stockQuantity: parseFloat(stockQuantity) || 0,
        costPrice: parseFloat(costPrice) || 0,
        sellPrice: parseFloat(sellPrice) || 0,
        hasRecipe: false,        // هل يحتوي على وصفة
        recipeId: null,          // معرف الوصفة المرتبطة
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
}

// 2. الموردون (Suppliers)
function createSupplier(name, notes = '') {
    return {
        id: generateId(),
        name: sanitizeInput(name),
        notes: sanitizeInput(notes),
        balance: 0, // الرصيد المستحق للمورد
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
}

// 3. المشتريات (Purchases)
function createPurchase(date, paymentMethod, supplierId, supplierInvoiceNumber, totalAmount, paidAmount, remainingAmount) {
    return {
        id: generateId(),
        date: date,
        paymentMethod: paymentMethod, // 'cash' أو 'credit'
        supplierId: supplierId,
        supplierInvoiceNumber: sanitizeInput(supplierInvoiceNumber),
        totalAmount: parseFloat(totalAmount) || 0,
        paidAmount: parseFloat(paidAmount) || 0,
        remainingAmount: parseFloat(remainingAmount) || 0,
        createdAt: new Date().toISOString()
    };
}

// 4. أصناف المشتريات (Purchase_Items)
function createPurchaseItem(purchaseId, productId, unit, quantity, costPrice, sellPrice, totalValue) {
    return {
        purchaseId: purchaseId,
        productId: productId,
        unit: sanitizeInput(unit),
        quantity: parseFloat(quantity) || 0,
        costPrice: parseFloat(costPrice) || 0,
        sellPrice: parseFloat(sellPrice) || 0,
        totalValue: parseFloat(totalValue) || 0
    };
}

// 5. المبيعات (Sales)
function createSale(date, paymentMethod, customerId, notes, includeTax, totalAmount, paidAmount, remainingAmount) {
    return {
        id: generateId(),
        date: date,
        paymentMethod: paymentMethod,
        customerId: customerId,
        notes: sanitizeInput(notes),
        includeTax: includeTax,
        totalAmount: parseFloat(totalAmount) || 0,
        paidAmount: parseFloat(paidAmount) || 0,
        remainingAmount: parseFloat(remainingAmount) || 0,
        createdAt: new Date().toISOString()
    };
}

// 6. أصناف المبيعات (Sale_Items)
function createSaleItem(saleId, productId, unit, quantity, price, total) {
    return {
        saleId: saleId,
        productId: productId,
        unit: sanitizeInput(unit),
        quantity: parseFloat(quantity) || 0,
        price: parseFloat(price) || 0,
        total: parseFloat(total) || 0
    };
}

// 7. العملاء (Customers)
function createCustomer(code, name, taxNumber, address, phone, email, currentBalance = 0) {
    return {
        id: generateId(),
        code: sanitizeInput(code),
        name: sanitizeInput(name),
        taxNumber: sanitizeInput(taxNumber),
        address: sanitizeInput(address),
        phone: sanitizeInput(phone),
        email: sanitizeInput(email),
        currentBalance: parseFloat(currentBalance) || 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
}

// 9. سندات النقدية (Cash_Vouchers)
function createCashVoucher(type, amount, date, description) {
    return {
        id: generateId(),
        type: type, // 'receipt' أو 'payment'
        amount: parseFloat(amount) || 0,
        date: date,
        description: sanitizeInput(description),
        createdAt: new Date().toISOString()
    };
}

// 10. المصروفات (Expenses)
function createExpense(amount, date, description) {
    return {
        id: generateId(),
        amount: parseFloat(amount) || 0,
        date: date,
        description: sanitizeInput(description),
        createdAt: new Date().toISOString()
    };
}

// 11. وصفات المنتجات (Product_Recipes) - الرُسبي
function createProductRecipe(productId, name, ingredients) {
    return {
        id: generateId(),
        productId: productId,
        name: sanitizeInput(name),
        ingredients: ingredients || [], // مصفوفة من {productId, quantity, unit}
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
}

// ===== وظائف إدارة البيانات =====

// البحث عن منتج بالكود أو الاسم
function findProduct(searchTerm) {
    return products.find(p => 
        p.code.toLowerCase() === searchTerm.toLowerCase() || 
        p.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
}

// البحث عن مورد بالاسم
function findSupplier(name) {
    return suppliers.find(s => s.name.toLowerCase().includes(name.toLowerCase()));
}

// البحث عن عميل بالكود أو الاسم
function findCustomer(searchTerm) {
    return customers.find(c =>
        c.code.toLowerCase() === searchTerm.toLowerCase() ||
        c.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
}

// البحث عن وصفة منتج بمعرف المنتج
function findProductRecipe(productId) {
    return productRecipes.find(r => r.productId === productId);
}

// التحقق من توفر مكونات الوصفة
function checkRecipeIngredients(recipe, requiredQuantity = 1) {
    const missingIngredients = [];

    recipe.ingredients.forEach(ingredient => {
        const product = products.find(p => p.id === ingredient.productId);
        if (!product) {
            missingIngredients.push({
                name: 'منتج غير موجود',
                required: ingredient.quantity * requiredQuantity,
                available: 0
            });
        } else {
            const requiredAmount = ingredient.quantity * requiredQuantity;
            if (product.stockQuantity < requiredAmount) {
                missingIngredients.push({
                    name: product.name,
                    required: requiredAmount,
                    available: product.stockQuantity,
                    unit: ingredient.unit || product.unit
                });
            }
        }
    });

    return missingIngredients;
}

// خصم مكونات الوصفة من المخزون
function deductRecipeIngredients(recipe, quantity = 1) {
    const deductedIngredients = [];

    recipe.ingredients.forEach(ingredient => {
        const product = products.find(p => p.id === ingredient.productId);
        if (product) {
            const deductAmount = ingredient.quantity * quantity;
            product.stockQuantity -= deductAmount;
            product.updatedAt = new Date().toISOString();

            deductedIngredients.push({
                productId: ingredient.productId,
                name: product.name,
                deductedQuantity: deductAmount,
                unit: ingredient.unit || product.unit,
                remainingStock: product.stockQuantity
            });
        }
    });

    return deductedIngredients;
}

// تحديث رصيد المنتج
function updateProductStock(productId, quantity, operation = 'add') {
    const product = products.find(p => p.id === productId);
    if (product) {
        if (operation === 'add') {
            product.stockQuantity += quantity;
        } else if (operation === 'subtract') {
            product.stockQuantity -= quantity;
        }
        product.updatedAt = new Date().toISOString();
        saveData();
        return true;
    }
    return false;
}

// تحديث أسعار المنتج
function updateProductPrices(productId, costPrice, sellPrice) {
    const product = products.find(p => p.id === productId);
    if (product) {
        if (costPrice !== undefined) product.costPrice = parseFloat(costPrice);
        if (sellPrice !== undefined) product.sellPrice = parseFloat(sellPrice);
        product.updatedAt = new Date().toISOString();
        saveData();
        return true;
    }
    return false;
}

// تحديث رصيد العميل
function updateCustomerBalance(customerId, amount, operation = 'add') {
    const customer = customers.find(c => c.id === customerId);
    if (customer) {
        if (operation === 'add') {
            customer.currentBalance += amount;
        } else if (operation === 'subtract') {
            customer.currentBalance -= amount;
        }
        customer.updatedAt = new Date().toISOString();
        saveData();
        return true;
    }
    return false;
}

// تحديث رصيد المورد
function updateSupplierBalance(supplierId, amount, operation = 'add') {
    const supplier = suppliers.find(s => s.id === supplierId);
    if (supplier) {
        if (operation === 'add') {
            supplier.balance += amount;
        } else if (operation === 'subtract') {
            supplier.balance -= amount;
        }
        supplier.updatedAt = new Date().toISOString();
        saveData();
        return true;
    }
    return false;
}

// الحصول على أصناف المشتريات لفاتورة معينة
function getPurchaseItems(purchaseId) {
    return purchaseItems.filter(item => item.purchaseId === purchaseId);
}

// الحصول على أصناف المبيعات لفاتورة معينة
function getSaleItems(saleId) {
    return saleItems.filter(item => item.saleId === saleId);
}

// حذف فاتورة مشتريات وأصنافها
function deletePurchase(purchaseId) {
    // حذف أصناف المشتريات أولاً
    purchaseItems = purchaseItems.filter(item => item.purchaseId !== purchaseId);
    
    // حذف فاتورة المشتريات
    purchases = purchases.filter(purchase => purchase.id !== purchaseId);
    
    saveData();
    return true;
}

// حذف فاتورة مبيعات وأصنافها
function deleteSale(saleId) {
    // حذف أصناف المبيعات أولاً
    saleItems = saleItems.filter(item => item.saleId !== saleId);
    
    // حذف فاتورة المبيعات
    sales = sales.filter(sale => sale.id !== saleId);
    
    saveData();
    return true;
}

// التحقق من توفر الكمية في المخزون
function checkStockAvailability(productId, requiredQuantity) {
    const product = products.find(p => p.id === productId);
    return product && product.stockQuantity >= requiredQuantity;
}

// الحصول على المنتجات ذات المخزون المنخفض
function getLowStockProducts(threshold = 5) {
    return products.filter(p => p.stockQuantity <= threshold);
}

// 8. المرتجعات (Returns)
function createReturn(originalInvoiceId, originalInvoiceNumber, customerName, totalAmount) {
    return {
        id: generateId(),
        originalInvoiceId: originalInvoiceId,
        originalInvoiceNumber: originalInvoiceNumber,
        date: new Date().toISOString(),
        customerName: sanitizeInput(customerName),
        totalAmount: parseFloat(totalAmount) || 0,
        createdAt: new Date().toISOString()
    };
}

// 9. أصناف المرتجعات (Return_Items)
function createReturnItem(returnId, productId, quantity, price, totalValue) {
    return {
        returnId: returnId,
        productId: productId,
        quantity: parseFloat(quantity) || 0,
        price: parseFloat(price) || 0,
        totalValue: parseFloat(totalValue) || 0
    };
}

// حذف مرتجع وأصنافه
function deleteReturn(returnId) {
    // حذف أصناف المرتجع
    if (typeof returnItems !== 'undefined') {
        returnItems = returnItems.filter(item => item.returnId !== returnId);
    }

    // حذف المرتجع
    if (typeof returns !== 'undefined') {
        returns = returns.filter(returnRecord => returnRecord.id !== returnId);
    }

    saveData();
    return true;
}
