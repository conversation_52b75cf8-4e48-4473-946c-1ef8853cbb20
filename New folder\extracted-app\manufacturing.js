// ===== إدارة التصنيع والإنتاج =====

// متغيرات التصنيع
let materials = []; // قائمة الخامات
let finalProducts = []; // قائمة المنتجات النهائية
let currentRecipeIngredients = [];
let editingRecipeId = null;

// تهيئة صفحة التصنيع
function initializeManufacturing() {
    loadMaterials();
    loadFinalProducts();
    loadProductsToSelect('ingredientProduct');
    loadProductsToSelect('manufacturingRecipe', 'recipe');
    loadEmployeesToSelect('manufacturingEmployee');
    displayRecipesList();
    displayManufacturingHistory();
    updateMaterialsTable();
    updateFinalProductsTable();

    // تعيين التاريخ الحالي
    document.getElementById('manufacturingDate').value = new Date().toISOString().split('T')[0];
}

// ===== إدارة الخامات =====

// إضافة خامة جديدة
function addMaterial() {
    const name = document.getElementById('materialName').value.trim();
    const unit = document.getElementById('materialUnit').value.trim();
    const cost = parseFloat(document.getElementById('materialCost').value) || 0;
    const stock = parseFloat(document.getElementById('materialStock').value) || 0;

    if (!name || !unit) {
        showNotification('يرجى إدخال اسم الخامة والوحدة', 'error');
        return;
    }

    // التحقق من عدم تكرار الخامة
    if (materials.find(m => m.name.toLowerCase() === name.toLowerCase())) {
        showNotification('هذه الخامة موجودة بالفعل', 'error');
        return;
    }

    const material = {
        id: Date.now(),
        name: name,
        unit: unit,
        cost: cost,
        stock: stock,
        createdAt: new Date().toISOString()
    };

    materials.push(material);
    saveMaterials();
    updateMaterialsTable();
    clearMaterialForm();
    showNotification(`تم إضافة الخامة "${name}" بنجاح`, 'success');
}

// مسح نموذج الخامة
function clearMaterialForm() {
    document.getElementById('materialName').value = '';
    document.getElementById('materialUnit').value = '';
    document.getElementById('materialCost').value = '';
    document.getElementById('materialStock').value = '';
}

// تحديث جدول الخامات
function updateMaterialsTable() {
    const container = document.getElementById('materialsTable');

    if (materials.length === 0) {
        container.innerHTML = '<p class="no-data">لا توجد خامات مضافة</p>';
        return;
    }

    let html = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>اسم الخامة</th>
                    <th>الوحدة</th>
                    <th>التكلفة/وحدة</th>
                    <th>المخزون</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
    `;

    materials.forEach(material => {
        html += `
            <tr>
                <td>${material.name}</td>
                <td>${material.unit}</td>
                <td>${material.cost.toFixed(2)} ج.م</td>
                <td>${material.stock} ${material.unit}</td>
                <td>
                    <button onclick="editMaterial(${material.id})" class="btn-small edit">تعديل</button>
                    <button onclick="deleteMaterial(${material.id})" class="btn-small delete">حذف</button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table>';
    container.innerHTML = html;
}

// حفظ الخامات
function saveMaterials() {
    localStorage.setItem('materials', JSON.stringify(materials));
}

// تحميل الخامات
function loadMaterials() {
    const saved = localStorage.getItem('materials');
    if (saved) {
        materials = JSON.parse(saved);
    }
}

// تعديل خامة
function editMaterial(id) {
    const material = materials.find(m => m.id === id);
    if (!material) return;

    document.getElementById('materialName').value = material.name;
    document.getElementById('materialUnit').value = material.unit;
    document.getElementById('materialCost').value = material.cost;
    document.getElementById('materialStock').value = material.stock;

    // تغيير زر الإضافة إلى تحديث
    const addBtn = document.querySelector('button[onclick="addMaterial()"]');
    addBtn.textContent = 'تحديث الخامة';
    addBtn.setAttribute('onclick', `updateMaterial(${id})`);
}

// تحديث خامة
function updateMaterial(id) {
    const name = document.getElementById('materialName').value.trim();
    const unit = document.getElementById('materialUnit').value.trim();
    const cost = parseFloat(document.getElementById('materialCost').value) || 0;
    const stock = parseFloat(document.getElementById('materialStock').value) || 0;

    if (!name || !unit) {
        showNotification('يرجى إدخال اسم الخامة والوحدة', 'error');
        return;
    }

    const materialIndex = materials.findIndex(m => m.id === id);
    if (materialIndex === -1) return;

    materials[materialIndex] = {
        ...materials[materialIndex],
        name: name,
        unit: unit,
        cost: cost,
        stock: stock,
        updatedAt: new Date().toISOString()
    };

    saveMaterials();
    updateMaterialsTable();
    clearMaterialForm();

    // إعادة زر الإضافة
    const updateBtn = document.querySelector(`button[onclick="updateMaterial(${id})"]`);
    updateBtn.textContent = 'إضافة خامة جديدة';
    updateBtn.setAttribute('onclick', 'addMaterial()');

    showNotification('تم تحديث الخامة بنجاح', 'success');
}

// حذف خامة
function deleteMaterial(id) {
    const material = materials.find(m => m.id === id);
    if (!material) return;

    if (confirm(`هل أنت متأكد من حذف الخامة "${material.name}"؟`)) {
        materials = materials.filter(m => m.id !== id);
        saveMaterials();
        updateMaterialsTable();
        showNotification('تم حذف الخامة بنجاح', 'success');
    }
}

// ===== إدارة المنتجات النهائية =====

// إضافة منتج نهائي جديد
function addFinalProduct() {
    const name = document.getElementById('finalProductName').value.trim();
    const price = parseFloat(document.getElementById('finalProductPrice').value) || 0;
    const description = document.getElementById('finalProductDescription').value.trim();

    if (!name) {
        showNotification('يرجى إدخال اسم المنتج', 'error');
        return;
    }

    // التحقق من عدم تكرار المنتج
    if (finalProducts.find(p => p.name.toLowerCase() === name.toLowerCase())) {
        showNotification('هذا المنتج موجود بالفعل', 'error');
        return;
    }

    const product = {
        id: Date.now(),
        name: name,
        price: price,
        description: description,
        createdAt: new Date().toISOString()
    };

    finalProducts.push(product);
    saveFinalProducts();
    updateFinalProductsTable();
    clearProductForm();
    showNotification(`تم إضافة المنتج "${name}" بنجاح`, 'success');
}

// مسح نموذج المنتج
function clearProductForm() {
    document.getElementById('finalProductName').value = '';
    document.getElementById('finalProductPrice').value = '';
    document.getElementById('finalProductDescription').value = '';
}

// تحديث جدول المنتجات النهائية
function updateFinalProductsTable() {
    const container = document.getElementById('finalProductsTable');

    if (finalProducts.length === 0) {
        container.innerHTML = '<p class="no-data">لا توجد منتجات مضافة</p>';
        return;
    }

    let html = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>اسم المنتج</th>
                    <th>سعر البيع</th>
                    <th>الوصف</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
    `;

    finalProducts.forEach(product => {
        html += `
            <tr>
                <td>${product.name}</td>
                <td>${product.price.toFixed(2)} ج.م</td>
                <td>${product.description || 'لا يوجد وصف'}</td>
                <td>
                    <button onclick="editFinalProduct(${product.id})" class="btn-small edit">تعديل</button>
                    <button onclick="deleteFinalProduct(${product.id})" class="btn-small delete">حذف</button>
                </td>
            </tr>
        `;
    });

    html += '</tbody></table>';
    container.innerHTML = html;
}

// تعديل منتج نهائي
function editFinalProduct(id) {
    const product = finalProducts.find(p => p.id === id);
    if (!product) return;

    document.getElementById('finalProductName').value = product.name;
    document.getElementById('finalProductPrice').value = product.price;
    document.getElementById('finalProductDescription').value = product.description || '';

    // تغيير زر الإضافة إلى تحديث
    const addBtn = document.querySelector('button[onclick="addFinalProduct()"]');
    addBtn.textContent = 'تحديث المنتج';
    addBtn.setAttribute('onclick', `updateFinalProduct(${id})`);
}

// تحديث منتج نهائي
function updateFinalProduct(id) {
    const name = document.getElementById('finalProductName').value.trim();
    const price = parseFloat(document.getElementById('finalProductPrice').value) || 0;
    const description = document.getElementById('finalProductDescription').value.trim();

    if (!name) {
        showNotification('يرجى إدخال اسم المنتج', 'error');
        return;
    }

    const productIndex = finalProducts.findIndex(p => p.id === id);
    if (productIndex === -1) return;

    finalProducts[productIndex] = {
        ...finalProducts[productIndex],
        name: name,
        price: price,
        description: description,
        updatedAt: new Date().toISOString()
    };

    saveFinalProducts();
    updateFinalProductsTable();
    clearProductForm();

    // إعادة زر الإضافة
    const updateBtn = document.querySelector(`button[onclick="updateFinalProduct(${id})"]`);
    updateBtn.textContent = 'إضافة منتج نهائي';
    updateBtn.setAttribute('onclick', 'addFinalProduct()');

    showNotification('تم تحديث المنتج بنجاح', 'success');
}

// حذف منتج نهائي
function deleteFinalProduct(id) {
    const product = finalProducts.find(p => p.id === id);
    if (!product) return;

    if (confirm(`هل أنت متأكد من حذف المنتج "${product.name}"؟`)) {
        finalProducts = finalProducts.filter(p => p.id !== id);
        saveFinalProducts();
        updateFinalProductsTable();
        showNotification('تم حذف المنتج بنجاح', 'success');
    }
}

// حفظ المنتجات النهائية
function saveFinalProducts() {
    localStorage.setItem('finalProducts', JSON.stringify(finalProducts));
}

// تحميل المنتجات النهائية
function loadFinalProducts() {
    const saved = localStorage.getItem('finalProducts');
    if (saved) {
        finalProducts = JSON.parse(saved);
    }
}

// توليد معرف فريد
function generateId() {
    return Date.now() + Math.random().toString(36).substr(2, 9);
}

// تحميل المنتجات في القائمة المنسدلة
function loadProductsToSelect(selectId, type = 'product') {
    const select = document.getElementById(selectId);
    select.innerHTML = type === 'recipe' ? '<option value="">اختر الوصفة</option>' : '<option value="">اختر المنتج/الخامة</option>';

    if (type === 'recipe') {
        recipes.forEach(recipe => {
            const option = document.createElement('option');
            option.value = recipe.id;
            option.textContent = `${recipe.name} (${recipe.quantity} وحدة)`;
            select.appendChild(option);
        });
    } else {
        // إضافة الخامات أولاً
        if (materials.length > 0) {
            const materialsGroup = document.createElement('optgroup');
            materialsGroup.label = 'الخامات';
            materials.forEach(material => {
                const option = document.createElement('option');
                option.value = `material_${material.id}`;
                option.textContent = `${material.name} - متوفر: ${material.stock} ${material.unit}`;
                materialsGroup.appendChild(option);
            });
            select.appendChild(materialsGroup);
        }

        // إضافة المنتجات الموجودة (إن وجدت)
        if (typeof products !== 'undefined' && products.length > 0) {
            const productsGroup = document.createElement('optgroup');
            productsGroup.label = 'المنتجات';
            products.forEach(product => {
                const option = document.createElement('option');
                option.value = `product_${product.id}`;
                option.textContent = `${product.name} - متوفر: ${product.stockQuantity} ${product.unit}`;
                productsGroup.appendChild(option);
            });
            select.appendChild(productsGroup);
        }
    }
}

// تحميل الموظفين في القائمة المنسدلة
function loadEmployeesToSelect(selectId) {
    const select = document.getElementById(selectId);
    select.innerHTML = '<option value="">الموظف المسئول</option>';
    
    employees.forEach(employee => {
        if (employee.status === 'active') {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = employee.name;
            select.appendChild(option);
        }
    });
}

// إضافة خامة للوصفة
function addIngredient() {
    const productId = document.getElementById('ingredientProduct').value;
    const quantity = parseFloat(document.getElementById('ingredientQuantity').value);

    if (!productId || !quantity || quantity <= 0) {
        showNotification('يرجى اختيار الخامة وإدخال كمية صحيحة', 'error');
        return;
    }

    let product = null;
    let productType = '';

    // تحديد نوع العنصر (خامة أم منتج)
    if (productId.startsWith('material_')) {
        const materialId = parseInt(productId.replace('material_', ''));
        product = materials.find(m => m.id === materialId);
        productType = 'material';
    } else if (productId.startsWith('product_')) {
        const realProductId = productId.replace('product_', '');
        product = products.find(p => p.id === realProductId);
        productType = 'product';
    }

    if (!product) {
        showNotification('العنصر المحدد غير موجود', 'error');
        return;
    }

    // التحقق من عدم تكرار العنصر
    if (currentRecipeIngredients.find(ing => ing.productId === productId)) {
        showNotification('هذا العنصر موجود بالفعل في الوصفة', 'error');
        return;
    }

    const ingredient = {
        productId: productId,
        productName: product.name,
        productUnit: product.unit,
        quantity: quantity,
        availableStock: productType === 'material' ? product.stock : product.stockQuantity,
        cost: product.cost || 0,
        type: productType
    };

    currentRecipeIngredients.push(ingredient);
    displayIngredientsList();

    // مسح الحقول
    document.getElementById('ingredientProduct').value = '';
    document.getElementById('ingredientQuantity').value = '';
}

// عرض قائمة الخامات
function displayIngredientsList() {
    const container = document.getElementById('ingredientsList');

    if (currentRecipeIngredients.length === 0) {
        container.innerHTML = '<p class="no-data">لا توجد خامات مضافة</p>';
        return;
    }

    let html = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>النوع</th>
                    <th>الاسم</th>
                    <th>الكمية المطلوبة</th>
                    <th>المتوفر</th>
                    <th>التكلفة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
    `;

    let totalCost = 0;

    currentRecipeIngredients.forEach((ingredient, index) => {
        const isAvailable = ingredient.availableStock >= ingredient.quantity;
        const itemCost = (ingredient.cost || 0) * ingredient.quantity;
        totalCost += itemCost;

        html += `
            <tr class="${!isAvailable ? 'insufficient-stock' : ''}">
                <td>${ingredient.type === 'material' ? '🧱 خامة' : '📦 منتج'}</td>
                <td>${ingredient.productName}</td>
                <td>${ingredient.quantity} ${ingredient.productUnit}</td>
                <td class="${!isAvailable ? 'text-danger' : ''}">${ingredient.availableStock} ${ingredient.productUnit}</td>
                <td>${itemCost.toFixed(2)} ج.م</td>
                <td>
                    <button onclick="removeIngredient(${index})" class="btn-small delete">حذف</button>
                </td>
            </tr>
        `;
    });

    html += `
            </tbody>
        </table>
        <div class="recipe-summary">
            <div class="total-cost">
                <strong>إجمالي التكلفة: ${totalCost.toFixed(2)} ج.م</strong>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

// حذف خامة من الوصفة
function removeIngredient(index) {
    currentRecipeIngredients.splice(index, 1);
    displayIngredientsList();
}

// حفظ الوصفة
function saveRecipe() {
    const name = document.getElementById('recipeName').value.trim();
    const quantity = parseFloat(document.getElementById('recipeQuantity').value);

    if (!name || !quantity || quantity <= 0) {
        showNotification('يرجى إدخال اسم المنتج والكمية المنتجة', 'error');
        return;
    }

    if (currentRecipeIngredients.length === 0) {
        showNotification('يرجى إضافة خامة واحدة على الأقل', 'error');
        return;
    }

    // حساب التكلفة الإجمالية
    const totalCost = currentRecipeIngredients.reduce((sum, ingredient) => {
        return sum + ((ingredient.cost || 0) * ingredient.quantity);
    }, 0);

    const recipe = {
        id: editingRecipeId || generateId(),
        name: name,
        quantity: quantity,
        ingredients: [...currentRecipeIngredients],
        totalCost: totalCost,
        costPerUnit: totalCost / quantity,
        createdAt: editingRecipeId ? recipes.find(r => r.id === editingRecipeId).createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    // التحقق من وجود المنتج النهائي وإضافته إذا لم يكن موجوداً
    const existingProduct = finalProducts.find(p => p.name.toLowerCase() === name.toLowerCase());
    if (!existingProduct) {
        const suggestedPrice = totalCost * 1.5; // هامش ربح 50%
        const shouldAddProduct = confirm(`هل تريد إضافة "${name}" كمنتج نهائي؟\nالتكلفة: ${totalCost.toFixed(2)} ج.م\nالسعر المقترح: ${suggestedPrice.toFixed(2)} ج.م`);

        if (shouldAddProduct) {
            const newProduct = {
                id: Date.now(),
                name: name,
                price: suggestedPrice,
                description: `منتج مصنع - ${quantity} وحدة`,
                createdAt: new Date().toISOString()
            };
            finalProducts.push(newProduct);
            saveFinalProducts();
            updateFinalProductsTable();
        }
    }

    if (editingRecipeId) {
        const index = recipes.findIndex(r => r.id === editingRecipeId);
        recipes[index] = recipe;
        showNotification('تم تحديث الوصفة بنجاح', 'success');
    } else {
        recipes.push(recipe);
        showNotification('تم حفظ الوصفة بنجاح', 'success');
    }

    saveData();
    clearRecipeForm();
    displayRecipesList();
    loadProductsToSelect('manufacturingRecipe', 'recipe');
}

// مسح نموذج الوصفة
function clearRecipeForm() {
    document.getElementById('recipeName').value = '';
    document.getElementById('recipeQuantity').value = '';
    currentRecipeIngredients = [];
    editingRecipeId = null;
    displayIngredientsList();
}

// عرض قائمة الوصفات
function displayRecipesList() {
    const container = document.getElementById('recipesList');
    
    if (recipes.length === 0) {
        container.innerHTML = '<p>لا توجد وصفات محفوظة</p>';
        return;
    }
    
    let html = '<div class="recipes-grid">';
    
    recipes.forEach(recipe => {
        html += `<div class="recipe-card">`;
        html += `<h5>${recipe.name}</h5>`;
        html += `<p>الكمية المنتجة: ${recipe.quantity} وحدة</p>`;
        html += `<p>عدد الخامات: ${recipe.ingredients.length}</p>`;
        html += `<div class="recipe-actions">`;
        html += `<button onclick="viewRecipe('${recipe.id}')" class="view-btn">عرض</button>`;
        html += `<button onclick="editRecipe('${recipe.id}')" class="edit-btn">تعديل</button>`;
        html += `<button onclick="deleteRecipe('${recipe.id}')" class="delete-btn">حذف</button>`;
        html += `</div>`;
        html += `</div>`;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// عرض تفاصيل الوصفة
function viewRecipe(recipeId) {
    const recipe = recipes.find(r => r.id === recipeId);
    if (!recipe) return;
    
    let details = `الوصفة: ${recipe.name}\n`;
    details += `الكمية المنتجة: ${recipe.quantity} وحدة\n\n`;
    details += `الخامات المطلوبة:\n`;
    
    recipe.ingredients.forEach(ingredient => {
        details += `- ${ingredient.productName}: ${ingredient.quantity} ${ingredient.productUnit}\n`;
    });
    
    alert(details);
}

// تعديل الوصفة
function editRecipe(recipeId) {
    const recipe = recipes.find(r => r.id === recipeId);
    if (!recipe) return;
    
    editingRecipeId = recipeId;
    document.getElementById('recipeName').value = recipe.name;
    document.getElementById('recipeQuantity').value = recipe.quantity;
    currentRecipeIngredients = [...recipe.ingredients];
    
    displayIngredientsList();
    showNotification('تم تحميل الوصفة للتعديل', 'info');
}

// حذف الوصفة
function deleteRecipe(recipeId) {
    if (!confirm('هل أنت متأكد من حذف هذه الوصفة؟')) return;
    
    const index = recipes.findIndex(r => r.id === recipeId);
    if (index !== -1) {
        recipes.splice(index, 1);
        saveData();
        displayRecipesList();
        loadProductsToSelect('manufacturingRecipe', 'recipe');
        showNotification('تم حذف الوصفة بنجاح', 'success');
    }
}

// معاينة عملية التصنيع
function previewManufacturing() {
    const recipeId = document.getElementById('manufacturingRecipe').value;
    const quantity = parseFloat(document.getElementById('manufacturingQuantity').value);
    
    if (!recipeId || !quantity || quantity <= 0) {
        document.getElementById('manufacturingPreview').innerHTML = '';
        return;
    }
    
    const recipe = recipes.find(r => r.id === recipeId);
    if (!recipe) return;
    
    const multiplier = quantity / recipe.quantity;
    let html = '<div class="manufacturing-preview-content">';
    html += `<h5>معاينة عملية التصنيع</h5>`;
    html += `<p><strong>المنتج النهائي:</strong> ${recipe.name}</p>`;
    html += `<p><strong>الكمية المطلوب إنتاجها:</strong> ${quantity} وحدة</p>`;
    html += `<h6>الخامات المطلوبة:</h6>`;

    let canManufacture = true;
    let totalCost = 0;

    recipe.ingredients.forEach(ingredient => {
        const requiredQuantity = safeNumber(ingredient.quantity * multiplier, 0);
        const product = products.find(p => p.id === ingredient.productId);
        const available = getSafeQuantity(product);
        const costPrice = getSafeCostPrice(product);
        const ingredientCost = requiredQuantity * costPrice;
        const sufficient = available >= requiredQuantity;

        totalCost += ingredientCost;

        if (!sufficient) canManufacture = false;

        html += `<div class="ingredient-preview ${!sufficient ? 'insufficient' : ''}">`;
        html += `${ingredient.productName}: ${requiredQuantity.toFixed(2)} ${ingredient.productUnit} `;
        html += `(متوفر: ${available.toFixed(2)} ${ingredient.productUnit}) `;
        html += `- التكلفة: ${formatCurrency(ingredientCost)}`;
        html += `</div>`;
    });

    // إضافة معلومات التكلفة
    const safeTotalCost = safeNumber(totalCost, 0);
    const safeQuantity = safeNumber(quantity, 1);
    const unitCost = safeTotalCost / safeQuantity;
    const suggestedPrice = unitCost * 1.3;

    html += `<div class="cost-summary">`;
    html += `<h6>ملخص التكلفة:</h6>`;
    html += `<p><strong>إجمالي تكلفة الخامات:</strong> ${formatCurrency(safeTotalCost)}</p>`;
    html += `<p><strong>تكلفة الوحدة الواحدة:</strong> ${formatCurrency(unitCost)}</p>`;
    html += `<p><strong>سعر البيع المقترح (هامش 30%):</strong> ${formatCurrency(suggestedPrice)}</p>`;
    html += `</div>`;
    
    if (!canManufacture) {
        html += '<div class="warning">⚠️ لا يمكن إتمام عملية التصنيع - خامات غير كافية</div>';
    }
    
    html += '</div>';
    document.getElementById('manufacturingPreview').innerHTML = html;
}

// بدء عملية التصنيع
function startManufacturing() {
    const recipeId = document.getElementById('manufacturingRecipe').value;
    const quantity = parseFloat(document.getElementById('manufacturingQuantity').value);
    const date = document.getElementById('manufacturingDate').value;
    const employeeId = document.getElementById('manufacturingEmployee').value;
    
    if (!recipeId || !quantity || quantity <= 0 || !date || !employeeId) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    const recipe = recipes.find(r => r.id === recipeId);
    if (!recipe) {
        showNotification('الوصفة غير موجودة', 'error');
        return;
    }
    
    const multiplier = quantity / recipe.quantity;
    
    // التحقق من توفر الخامات
    for (let ingredient of recipe.ingredients) {
        const requiredQuantity = ingredient.quantity * multiplier;
        const product = products.find(p => p.id === ingredient.productId);
        
        if (!product || product.stockQuantity < requiredQuantity) {
            showNotification(`خامة غير كافية: ${ingredient.productName}`, 'error');
            return;
        }
    }
    
    // حساب التكلفة الإجمالية
    let totalCost = 0;
    const ingredientsWithCost = recipe.ingredients.map(ingredient => {
        const requiredQuantity = safeNumber(ingredient.quantity * multiplier, 0);
        const product = products.find(p => p.id === ingredient.productId);
        const costPrice = getSafeCostPrice(product);
        const ingredientCost = requiredQuantity * costPrice;
        totalCost += ingredientCost;

        return {
            ...ingredient,
            usedQuantity: requiredQuantity,
            costPrice: costPrice,
            totalCost: safeNumber(ingredientCost, 0)
        };
    });

    // خصم الخامات من المخزون
    recipe.ingredients.forEach(ingredient => {
        const requiredQuantity = ingredient.quantity * multiplier;
        updateProductStock(ingredient.productId, requiredQuantity, 'subtract');
    });

    // إضافة المنتج النهائي للمخزون (إذا كان موجوداً)
    const finalProduct = products.find(p => p.name === recipe.name);
    if (finalProduct) {
        updateProductStock(finalProduct.id, quantity, 'add');

        // تحديث سعر التكلفة للمنتج النهائي
        const safeTotalCost = safeNumber(totalCost, 0);
        const safeQuantity = safeNumber(quantity, 1);
        const unitCost = safeTotalCost / safeQuantity;
        finalProduct.costPrice = safeNumber(unitCost, 0);

        // اقتراح سعر بيع جديد إذا لم يكن محدداً
        const currentSellingPrice = getSafeSellingPrice(finalProduct);
        if (currentSellingPrice === 0) {
            finalProduct.sellingPrice = safeNumber(unitCost * 1.3, 0); // هامش ربح 30%
        }
    }

    // حفظ عملية التصنيع
    const manufacturingRecord = {
        id: generateId(),
        recipeId: recipeId,
        recipeName: recipe.name,
        quantity: quantity,
        date: date,
        employeeId: employeeId,
        employeeName: employees.find(e => e.id === employeeId)?.name || 'غير محدد',
        ingredients: ingredientsWithCost,
        totalCost: safeNumber(totalCost, 0),
        unitCost: safeNumber(totalCost / quantity, 0),
        createdAt: new Date().toISOString()
    };
    
    manufacturing.push(manufacturingRecord);
    saveData();
    
    clearManufacturingForm();
    displayManufacturingHistory();
    displayProducts();
    displayProductsList();
    
    showNotification('تم إتمام عملية التصنيع بنجاح', 'success');
}

// مسح نموذج التصنيع
function clearManufacturingForm() {
    document.getElementById('manufacturingRecipe').value = '';
    document.getElementById('manufacturingQuantity').value = '';
    document.getElementById('manufacturingEmployee').value = '';
    document.getElementById('manufacturingPreview').innerHTML = '';
}

// عرض سجل عمليات التصنيع
function displayManufacturingHistory() {
    const container = document.getElementById('manufacturingHistory');
    
    if (manufacturing.length === 0) {
        container.innerHTML = '<p>لا توجد عمليات تصنيع مسجلة</p>';
        return;
    }
    
    let html = '<div class="manufacturing-table">';
    html += '<div class="table-header">';
    html += '<span>التاريخ</span><span>المنتج</span><span>الكمية</span><span>التكلفة الإجمالية</span><span>تكلفة الوحدة</span><span>الموظف</span><span>الإجراءات</span>';
    html += '</div>';

    manufacturing.slice().reverse().forEach(record => {
        html += `<div class="table-row">`;
        html += `<span>${new Date(record.date).toLocaleDateString('ar-SA')}</span>`;
        html += `<span>${record.recipeName}</span>`;
        html += `<span>${record.quantity} وحدة</span>`;
        html += `<span>${formatCurrency(record.totalCost)}</span>`;
        html += `<span>${formatCurrency(record.unitCost)}</span>`;
        html += `<span>${record.employeeName}</span>`;
        html += `<span>`;
        html += `<button onclick="viewManufacturingDetails('${record.id}')" class="view-btn">تفاصيل</button>`;
        html += `<button onclick="deleteManufacturingRecord('${record.id}')" class="delete-btn">حذف</button>`;
        html += `</span>`;
        html += '</div>';
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// عرض تفاصيل عملية التصنيع
function viewManufacturingDetails(recordId) {
    const record = manufacturing.find(m => m.id === recordId);
    if (!record) return;
    
    let details = `عملية التصنيع\n`;
    details += `التاريخ: ${new Date(record.date).toLocaleDateString('ar-SA')}\n`;
    details += `المنتج النهائي: ${record.recipeName}\n`;
    details += `الكمية المنتجة: ${record.quantity} وحدة\n`;
    details += `الموظف المسئول: ${record.employeeName}\n`;

    if (record.totalCost !== undefined && record.totalCost !== null) {
        details += `إجمالي التكلفة: ${formatCurrency(record.totalCost)}\n`;
        details += `تكلفة الوحدة: ${formatCurrency(record.unitCost)}\n`;
    }

    details += `\nالخامات المستخدمة:\n`;

    record.ingredients.forEach(ingredient => {
        const usedQty = safeNumber(ingredient.usedQuantity, 0);
        details += `- ${ingredient.productName}: ${usedQty.toFixed(2)} ${ingredient.productUnit}`;
        if (ingredient.totalCost !== undefined && ingredient.totalCost !== null) {
            details += ` - التكلفة: ${formatCurrency(ingredient.totalCost)}`;
        }
        details += `\n`;
    });
    
    alert(details);
}

// حذف سجل التصنيع
function deleteManufacturingRecord(recordId) {
    if (!confirm('هل أنت متأكد من حذف هذا السجل؟')) return;
    
    const index = manufacturing.findIndex(m => m.id === recordId);
    if (index !== -1) {
        manufacturing.splice(index, 1);
        saveData();
        displayManufacturingHistory();
        showNotification('تم حذف السجل بنجاح', 'success');
    }
}

// ===== إدارة Excel =====

// متغيرات Excel
let excelWorkbook = null;
let excelSheets = {};
let currentSheetName = '';
let excelData = [];
let excelHeaders = [];

// استيراد ملف Excel
function importExcelFile() {
    const fileInput = document.getElementById('excelFileInput');
    const file = fileInput.files[0];

    if (!file) {
        showNotification('يرجى اختيار ملف Excel أولاً', 'error');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = new Uint8Array(e.target.result);
            excelWorkbook = XLSX.read(data, { type: 'array' });

            // تحميل جميع أوراق العمل
            excelSheets = {};
            excelWorkbook.SheetNames.forEach(sheetName => {
                const worksheet = excelWorkbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                excelSheets[sheetName] = {
                    headers: jsonData.length > 0 ? (jsonData[0] || []) : [],
                    data: jsonData.length > 0 ? jsonData.slice(1) : [],
                    originalData: jsonData
                };
            });

            // عرض أوراق العمل
            displaySheetTabs();

            // تحديد الورقة الأولى كافتراضية
            if (excelWorkbook.SheetNames.length > 0) {
                switchToSheet(excelWorkbook.SheetNames[0]);
                showNotification(`تم استيراد ملف Excel بنجاح (${excelWorkbook.SheetNames.length} ورقة عمل)`, 'success');
            } else {
                showNotification('الملف فارغ أو غير صالح', 'error');
            }
        } catch (error) {
            console.error('خطأ في قراءة ملف Excel:', error);
            showNotification('خطأ في قراءة ملف Excel. تأكد من صحة الملف.', 'error');
        }
    };

    reader.readAsArrayBuffer(file);
}

// عرض تبويبات أوراق العمل
function displaySheetTabs() {
    const tabsContainer = document.getElementById('excelSheetTabs');

    if (!excelWorkbook || excelWorkbook.SheetNames.length === 0) {
        tabsContainer.innerHTML = '';
        return;
    }

    let tabsHtml = '';
    excelWorkbook.SheetNames.forEach((sheetName, index) => {
        const isActive = sheetName === currentSheetName;
        const rowCount = excelSheets[sheetName].data.length;
        const colCount = excelSheets[sheetName].headers.length;

        tabsHtml += `
            <div class="sheet-tab ${isActive ? 'active' : ''}" onclick="switchToSheet('${sheetName}')">
                <span class="sheet-name">${sheetName}</span>
                <span class="sheet-size">(${rowCount} × ${colCount})</span>
            </div>
        `;
    });

    tabsContainer.innerHTML = tabsHtml;
}

// التبديل إلى ورقة عمل محددة
function switchToSheet(sheetName) {
    if (!excelSheets[sheetName]) {
        showNotification('ورقة العمل غير موجودة', 'error');
        return;
    }

    currentSheetName = sheetName;
    excelHeaders = [...excelSheets[sheetName].headers];
    excelData = excelSheets[sheetName].data.map(row => [...row]);

    displaySheetTabs(); // تحديث التبويبات
    displayExcelData();
    updateSheetInfo();
}

// تحديث معلومات الورقة الحالية
function updateSheetInfo() {
    const sheetNameElement = document.getElementById('currentSheetName');
    const dimensionsElement = document.getElementById('sheetDimensions');

    if (currentSheetName) {
        sheetNameElement.textContent = `الورقة الحالية: ${currentSheetName}`;
        dimensionsElement.textContent = `الأبعاد: ${excelData.length} صف × ${excelHeaders.length} عمود`;
    } else {
        sheetNameElement.textContent = 'لا توجد أوراق';
        dimensionsElement.textContent = '';
    }
}

// عرض بيانات Excel
function displayExcelData() {
    const container = document.getElementById('excelDataContainer');
    const tableHead = document.getElementById('excelTableHead');
    const tableBody = document.getElementById('excelTableBody');
    const tableWrapper = document.getElementById('excelTableWrapper');

    if (excelHeaders.length === 0 && excelData.length === 0) {
        container.style.display = 'none';
        return;
    }

    container.style.display = 'block';

    // التأكد من وجود headers حتى لو كانت فارغة
    if (excelHeaders.length === 0 && excelData.length > 0) {
        const maxCols = Math.max(...excelData.map(row => row.length));
        excelHeaders = Array.from({length: maxCols}, (_, i) => `عمود ${i + 1}`);
    }

    // إنشاء رأس الجدول
    let headHtml = '<tr>';
    headHtml += '<th style="width: 50px; background: #f0f0f0;">#</th>'; // رقم الصف
    excelHeaders.forEach((header, index) => {
        headHtml += `<th contenteditable="true" onblur="updateHeader(${index}, this.textContent)" style="min-width: 120px; background: #f8f9fa;">${header || `عمود ${index + 1}`}</th>`;
    });
    headHtml += '<th style="width: 100px; background: #f0f0f0;">الإجراءات</th></tr>';
    tableHead.innerHTML = headHtml;

    // إنشاء صفوف البيانات
    let bodyHtml = '';
    excelData.forEach((row, rowIndex) => {
        bodyHtml += '<tr>';
        bodyHtml += `<td class="row-number" style="background: #f8f9fa; text-align: center; font-weight: bold;">${rowIndex + 1}</td>`;

        excelHeaders.forEach((header, colIndex) => {
            const cellValue = row[colIndex] !== undefined ? row[colIndex] : '';
            bodyHtml += `<td contenteditable="true" onblur="updateCell(${rowIndex}, ${colIndex}, this.textContent)" style="min-width: 120px; padding: 8px;">${cellValue}</td>`;
        });
        bodyHtml += `<td style="text-align: center;"><button onclick="deleteExcelRow(${rowIndex})" class="delete-btn" title="حذف الصف" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">🗑️</button></td>`;
        bodyHtml += '</tr>';
    });

    tableBody.innerHTML = bodyHtml;

    // تحديث معلومات الورقة
    updateSheetInfo();

    // جعل الجدول قابل للتمرير
    if (tableWrapper) {
        tableWrapper.style.maxHeight = '70vh';
        tableWrapper.style.overflowY = 'auto';
        tableWrapper.style.overflowX = 'auto';
        tableWrapper.style.border = '1px solid #ddd';
        tableWrapper.style.borderRadius = '8px';
    }
}

// تحديث رأس العمود
function updateHeader(index, newValue) {
    excelHeaders[index] = newValue;
}

// تحديث خلية
function updateCell(rowIndex, colIndex, newValue) {
    if (!excelData[rowIndex]) {
        excelData[rowIndex] = [];
    }
    excelData[rowIndex][colIndex] = newValue;
}

// إضافة صف جديد
function addExcelRow() {
    if (excelHeaders.length === 0) {
        showNotification('يجب إضافة أعمدة أولاً أو استيراد ملف Excel', 'error');
        return;
    }

    const newRow = new Array(excelHeaders.length).fill('');
    excelData.push(newRow);

    // تحديث البيانات في الورقة الحالية
    if (currentSheetName && excelSheets[currentSheetName]) {
        excelSheets[currentSheetName].data = [...excelData];
    }

    displayExcelData();
    showNotification('تم إضافة صف جديد', 'success');
}

// إضافة عمود جديد
function addExcelColumn() {
    const columnName = prompt('أدخل اسم العمود الجديد:');
    if (!columnName) return;

    excelHeaders.push(columnName);

    // إضافة خلية فارغة لكل صف موجود
    excelData.forEach(row => {
        row.push('');
    });

    // تحديث البيانات في الورقة الحالية
    if (currentSheetName && excelSheets[currentSheetName]) {
        excelSheets[currentSheetName].headers = [...excelHeaders];
        excelSheets[currentSheetName].data = [...excelData];
    }

    displayExcelData();
    showNotification(`تم إضافة العمود "${columnName}"`, 'success');
}

// حذف صف
function deleteExcelRow(rowIndex) {
    if (confirm('هل أنت متأكد من حذف هذا الصف؟')) {
        excelData.splice(rowIndex, 1);

        // تحديث البيانات في الورقة الحالية
        if (currentSheetName && excelSheets[currentSheetName]) {
            excelSheets[currentSheetName].data = [...excelData];
        }

        displayExcelData();
        showNotification('تم حذف الصف', 'success');
    }
}

// حفظ التغييرات
function saveExcelChanges() {
    // يمكن إضافة منطق حفظ البيانات هنا
    showNotification('تم حفظ التغييرات بنجاح', 'success');
}

// تصدير الورقة الحالية
function exportCurrentSheet() {
    if (!currentSheetName || !excelHeaders.length) {
        showNotification('لا توجد بيانات للتصدير', 'error');
        return;
    }

    // إنشاء workbook جديد
    const wb = XLSX.utils.book_new();

    // تحضير البيانات للتصدير
    const exportData = [excelHeaders, ...excelData];
    const ws = XLSX.utils.aoa_to_sheet(exportData);

    // إضافة الورقة إلى الـ workbook
    XLSX.utils.book_append_sheet(wb, ws, currentSheetName);

    // تصدير الملف
    const fileName = `${currentSheetName}_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);

    showNotification(`تم تصدير الورقة "${currentSheetName}" بنجاح`, 'success');
}

// تصدير جميع الأوراق
function exportToExcel() {
    if (Object.keys(excelSheets).length === 0) {
        showNotification('لا توجد بيانات للتصدير', 'error');
        return;
    }

    const wb = XLSX.utils.book_new();

    // إضافة كل ورقة عمل
    Object.keys(excelSheets).forEach(sheetName => {
        const sheet = excelSheets[sheetName];
        const exportData = [sheet.headers, ...sheet.data];
        const ws = XLSX.utils.aoa_to_sheet(exportData);
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
    });

    const fileName = `manufacturing_data_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);

    showNotification('تم تصدير جميع الأوراق بنجاح', 'success');
}

// مسح البيانات
function clearExcelData() {
    if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
        excelData = [];
        excelHeaders = [];
        document.getElementById('excelDataContainer').style.display = 'none';
        document.getElementById('excelFileInput').value = '';
        showNotification('تم مسح البيانات', 'info');
    }
}

// إضافة مستمع للتغيير في حقول التصنيع
document.addEventListener('DOMContentLoaded', function() {
    const manufacturingRecipe = document.getElementById('manufacturingRecipe');
    const manufacturingQuantity = document.getElementById('manufacturingQuantity');

    if (manufacturingRecipe) {
        manufacturingRecipe.addEventListener('change', previewManufacturing);
    }

    if (manufacturingQuantity) {
        manufacturingQuantity.addEventListener('input', previewManufacturing);
    }
});
