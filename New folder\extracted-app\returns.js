// ===== إدارة المرتجعات =====

// متغيرات المرتجعات
let currentInvoiceForReturn = null;
let returnItems = [];

// عرض قسم المرتجعات
function showReturnsSection() {
    const returnsSection = document.getElementById('returnsSection');
    const salesContainer = document.querySelector('.sales-container');

    if (returnsSection.classList.contains('hidden')) {
        returnsSection.classList.remove('hidden');
        salesContainer.style.display = 'none';
    } else {
        returnsSection.classList.add('hidden');
        salesContainer.style.display = 'grid';
    }
}

// البحث عن الفاتورة
function searchInvoice() {
    const invoiceNumber = document.getElementById('returnInvoiceNumber').value.trim();
    const invoiceDate = document.getElementById('returnInvoiceDate').value;

    if (!invoiceNumber && !invoiceDate) {
        showNotification('يرجى إدخال رقم الفاتورة أو التاريخ للبحث', 'error');
        return;
    }

    // البحث في المبيعات
    let foundInvoice = null;

    if (invoiceNumber) {
        foundInvoice = sales.find(sale => sale.invoiceNumber && sale.invoiceNumber.toString() === invoiceNumber);
    } else if (invoiceDate) {
        const searchDate = new Date(invoiceDate).toDateString();
        foundInvoice = sales.find(sale => new Date(sale.date).toDateString() === searchDate);
    }

    if (!foundInvoice) {
        showNotification('لم يتم العثور على الفاتورة', 'error');
        document.getElementById('invoiceDetails').classList.add('hidden');
        document.getElementById('returnItems').classList.add('hidden');
        return;
    }

    currentInvoiceForReturn = foundInvoice;
    displayInvoiceDetails(foundInvoice);
}

// عرض تفاصيل الفاتورة
function displayInvoiceDetails(invoice) {
    const invoiceInfo = document.getElementById('invoiceInfo');
    const invoiceItems = document.getElementById('invoiceItems');
    const invoiceDetails = document.getElementById('invoiceDetails');
    const returnItemsSection = document.getElementById('returnItems');

    // معلومات الفاتورة
    invoiceInfo.innerHTML = `
        <div class="invoice-header">
            <div><strong>رقم الفاتورة:</strong> ${invoice.invoiceNumber || invoice.id}</div>
            <div><strong>التاريخ:</strong> ${new Date(invoice.date).toLocaleDateString('ar-EG')}</div>
            <div><strong>العميل:</strong> ${invoice.customerName || 'عميل نقدي'}</div>
            <div><strong>إجمالي الفاتورة:</strong> ${invoice.total.toFixed(2)} ج.م</div>
        </div>
    `;

    // عناصر الفاتورة
    let itemsHtml = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الإجمالي</th>
                    <th>إرجاع</th>
                </tr>
            </thead>
            <tbody>
    `;

    invoice.items.forEach((item, index) => {
        const product = products.find(p => p.id === item.productId);
        const productName = product ? product.name : 'منتج محذوف';

        itemsHtml += `
            <tr>
                <td>${productName}</td>
                <td>${item.quantity} ${item.unit || 'قطعة'}</td>
                <td>${item.price.toFixed(2)} ج.م</td>
                <td>${(item.quantity * item.price).toFixed(2)} ج.م</td>
                <td>
                    <input type="checkbox" id="returnCheck_${index}" onchange="toggleReturnItem(${index}, ${item.quantity}, ${item.price})">
                    <input type="number" id="returnQty_${index}"
                           placeholder="الكمية"
                           min="0"
                           max="${item.quantity}"
                           step="0.01"
                           style="width: 80px; margin-right: 10px;"
                           disabled>
                </td>
            </tr>
        `;
    });

    itemsHtml += '</tbody></table>';
    invoiceItems.innerHTML = itemsHtml;

    // إظهار الأقسام
    invoiceDetails.classList.remove('hidden');
    returnItemsSection.classList.remove('hidden');

    // مسح قائمة المرتجعات السابقة
    returnItems = [];
    updateReturnTotal();
}

// تفعيل/إلغاء تفعيل عنصر للمرتجع
function toggleReturnItem(index, maxQuantity, price) {
    const checkbox = document.getElementById(`returnCheck_${index}`);
    const qtyInput = document.getElementById(`returnQty_${index}`);

    if (checkbox.checked) {
        qtyInput.disabled = false;
        qtyInput.value = maxQuantity; // تعيين الكمية الكاملة افتراضياً
        qtyInput.addEventListener('input', updateReturnTotal);

        // إضافة العنصر لقائمة المرتجعات
        const item = currentInvoiceForReturn.items[index];
        returnItems[index] = {
            productId: item.productId,
            quantity: maxQuantity,
            price: price,
            maxQuantity: maxQuantity
        };
    } else {
        qtyInput.disabled = true;
        qtyInput.value = '';
        qtyInput.removeEventListener('input', updateReturnTotal);

        // إزالة العنصر من قائمة المرتجعات
        delete returnItems[index];
    }

    updateReturnTotal();
}

// تحديث إجمالي المرتجع
function updateReturnTotal() {
    let total = 0;

    returnItems.forEach((item, index) => {
        if (item) {
            const qtyInput = document.getElementById(`returnQty_${index}`);
            const quantity = parseFloat(qtyInput.value) || 0;

            if (quantity > item.maxQuantity) {
                qtyInput.value = item.maxQuantity;
                showNotification(`الكمية لا يمكن أن تزيد عن ${item.maxQuantity}`, 'warning');
                return;
            }

            item.quantity = quantity;
            total += quantity * item.price;
        }
    });

    document.getElementById('returnTotal').textContent = total.toFixed(2);
}

// إلغاء المرتجع
function cancelReturn() {
    document.getElementById('returnsSection').classList.add('hidden');
    document.querySelector('.sales-container').style.display = 'grid';

    // مسح البيانات
    document.getElementById('returnInvoiceNumber').value = '';
    document.getElementById('returnInvoiceDate').value = '';
    document.getElementById('invoiceDetails').classList.add('hidden');
    document.getElementById('returnItems').classList.add('hidden');
    currentInvoiceForReturn = null;
    returnItems = [];
}

// تنفيذ المرتجع
function processReturn() {
    if (!currentInvoiceForReturn) {
        showNotification('يرجى اختيار فاتورة أولاً', 'error');
        return;
    }

    // التحقق من وجود عناصر للمرتجع
    const validReturnItems = returnItems.filter(item => item && item.quantity > 0);

    if (validReturnItems.length === 0) {
        showNotification('يرجى اختيار عناصر للمرتجع', 'error');
        return;
    }

    // تأكيد المرتجع
    const totalReturn = validReturnItems.reduce((sum, item) => sum + (item.quantity * item.price), 0);

    if (!confirm(`هل أنت متأكد من تنفيذ المرتجع؟\nإجمالي المرتجع: ${totalReturn.toFixed(2)} ج.م`)) {
        return;
    }

    // تنفيذ المرتجع
    executeReturn(validReturnItems, totalReturn);
}

// تنفيذ عملية المرتجع
function executeReturn(returnItemsList, totalReturn) {
    try {
        // إنشاء سجل المرتجع
        const returnRecord = {
            id: Date.now(),
            originalInvoiceId: currentInvoiceForReturn.id,
            originalInvoiceNumber: currentInvoiceForReturn.invoiceNumber || currentInvoiceForReturn.id,
            date: new Date().toISOString(),
            items: returnItemsList.map((item, index) => ({
                productId: item.productId,
                quantity: item.quantity,
                price: item.price,
                total: item.quantity * item.price
            })),
            total: totalReturn,
            customerName: currentInvoiceForReturn.customerName || 'عميل نقدي'
        };

        // حفظ المرتجع
        if (!returns) returns = [];
        returns.push(returnRecord);

        // إرجاع الكميات للمخزن
        returnItemsList.forEach((item, index) => {
            const product = products.find(p => p.id === item.productId);
            if (product) {
                product.stockQuantity += item.quantity;
            }
        });

        // حفظ البيانات
        saveData();

        // إظهار رسالة النجاح
        showNotification(`تم تنفيذ المرتجع بنجاح\nرقم المرتجع: ${returnRecord.id}\nالمبلغ المرتجع: ${totalReturn.toFixed(2)} ج.م`, 'success');

        // مسح النموذج
        cancelReturn();

        // تحديث عرض المنتجات
        if (typeof displayProducts === 'function') {
            displayProducts();
        }

    } catch (error) {
        console.error('خطأ في تنفيذ المرتجع:', error);
        showNotification('حدث خطأ أثناء تنفيذ المرتجع', 'error');
    }
}

// فتح نافذة المرتجع (الوظيفة الأصلية)
function openReturnModal(saleId) {
    const sale = sales.find(s => s.id === saleId);
    if (!sale) {
        showNotification('فاتورة المبيعات غير موجودة', 'error');
        return;
    }
    
    // الحصول على أصناف الفاتورة
    const saleItemsForSale = saleItems.filter(item => item.saleId === saleId);
    if (saleItemsForSale.length === 0) {
        showNotification('لا توجد أصناف في هذه الفاتورة', 'error');
        return;
    }
    
    // إنشاء نافذة المرتجع
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'returnModal';
    
    let itemsHtml = '';
    saleItemsForSale.forEach((item, index) => {
        const product = products.find(p => p.id === item.productId);
        const productName = product ? product.name : 'منتج محذوف';
        
        itemsHtml += `
            <div class="return-item">
                <div class="return-item-info">
                    <strong>${productName}</strong><br>
                    <span>الكمية المباعة: ${item.quantity} ${item.unit}</span><br>
                    <span>السعر: ${item.price.toFixed(2)} ${settings.currencySymbol}</span>
                </div>
                <div class="return-item-controls">
                    <label>
                        <input type="checkbox" id="returnCheck_${index}" onchange="toggleReturnItem(${index})">
                        إرجاع
                    </label>
                    <input type="number" id="returnQty_${index}" 
                           placeholder="الكمية" 
                           min="0" 
                           max="${item.quantity}" 
                           step="0.01" 
                           disabled
                           class="neumorphic-input"
                           style="width: 100px; margin: 5px 0;">
                </div>
            </div>
        `;
    });
    
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>إرجاع أصناف - فاتورة رقم ${sale.invoiceNumber || sale.id.slice(-8)}</h3>
                <button class="close-btn" onclick="closeReturnModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="sale-info">
                    <p><strong>رقم الفاتورة:</strong> ${sale.invoiceNumber || sale.id.slice(-8)}</p>
                    <p><strong>تاريخ الفاتورة:</strong> ${new Date(sale.date).toLocaleDateString('ar-SA')}</p>
                    <p><strong>العميل:</strong> ${customers.find(c => c.id === sale.customerId)?.name || 'غير محدد'}</p>
                    <p><strong>إجمالي الفاتورة:</strong> ${(sale.totalAmount || sale.total).toFixed(2)} ${settings.currencySymbol}</p>
                </div>
                
                <h4>اختر الأصناف المراد إرجاعها:</h4>
                <div class="return-items-list">
                    ${itemsHtml}
                </div>
                
                <div class="return-reason">
                    <label for="returnReason"><strong>سبب المرتجع:</strong></label>
                    <select id="returnReason" class="neumorphic-input">
                        <option value="">اختر السبب</option>
                        <option value="عيب في المنتج">عيب في المنتج</option>
                        <option value="منتج خاطئ">منتج خاطئ</option>
                        <option value="عدم رضا العميل">عدم رضا العميل</option>
                        <option value="تلف أثناء النقل">تلف أثناء النقل</option>
                        <option value="أخرى">أخرى</option>
                    </select>
                </div>
                
                <div class="return-notes">
                    <label for="returnNotes"><strong>ملاحظات إضافية:</strong></label>
                    <textarea id="returnNotes" class="neumorphic-input" rows="3" placeholder="ملاحظات اختيارية"></textarea>
                </div>
                
                <div class="return-employee">
                    <label for="returnEmployee"><strong>الموظف المسئول:</strong></label>
                    <select id="returnEmployee" class="neumorphic-input">
                        <option value="">اختر الموظف</option>
                    </select>
                </div>
            </div>
            <div class="modal-actions">
                <button onclick="processReturn('${saleId}')" class="neumorphic-button primary">تأكيد المرتجع</button>
                <button onclick="closeReturnModal()" class="neumorphic-button secondary">إلغاء</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // تحميل قائمة الموظفين
    loadEmployeesToReturnSelect();
    
    // حفظ بيانات الأصناف للاستخدام لاحقاً
    window.currentReturnItems = saleItemsForSale;
}

// تحميل الموظفين في قائمة المرتجع
function loadEmployeesToReturnSelect() {
    const select = document.getElementById('returnEmployee');
    if (!select) return;
    
    select.innerHTML = '<option value="">اختر الموظف</option>';
    
    employees.forEach(employee => {
        if (employee.status === 'active') {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = employee.name;
            select.appendChild(option);
        }
    });
}

// تفعيل/إلغاء تفعيل صنف للإرجاع
function toggleReturnItem(index) {
    const checkbox = document.getElementById(`returnCheck_${index}`);
    const qtyInput = document.getElementById(`returnQty_${index}`);
    
    if (checkbox.checked) {
        qtyInput.disabled = false;
        qtyInput.value = window.currentReturnItems[index].quantity; // الكمية الكاملة افتراضياً
        qtyInput.focus();
    } else {
        qtyInput.disabled = true;
        qtyInput.value = '';
    }
}

// معالجة المرتجع
function processReturn(saleId) {
    const reason = document.getElementById('returnReason').value;
    const notes = document.getElementById('returnNotes').value.trim();
    const employeeId = document.getElementById('returnEmployee').value;
    
    if (!reason) {
        showNotification('يرجى اختيار سبب المرتجع', 'error');
        return;
    }
    
    if (!employeeId) {
        showNotification('يرجى اختيار الموظف المسئول', 'error');
        return;
    }
    
    // جمع الأصناف المختارة للإرجاع
    const returnItems = [];
    window.currentReturnItems.forEach((item, index) => {
        const checkbox = document.getElementById(`returnCheck_${index}`);
        const qtyInput = document.getElementById(`returnQty_${index}`);
        
        if (checkbox.checked) {
            const returnQty = parseFloat(qtyInput.value);
            
            if (!returnQty || returnQty <= 0) {
                showNotification(`يرجى إدخال كمية صحيحة للصنف رقم ${index + 1}`, 'error');
                return;
            }
            
            if (returnQty > item.quantity) {
                showNotification(`الكمية المدخلة أكبر من الكمية المباعة للصنف رقم ${index + 1}`, 'error');
                return;
            }
            
            returnItems.push({
                productId: item.productId,
                originalQuantity: item.quantity,
                returnQuantity: returnQty,
                price: item.price,
                unit: item.unit
            });
        }
    });
    
    if (returnItems.length === 0) {
        showNotification('يرجى اختيار صنف واحد على الأقل للإرجاع', 'error');
        return;
    }
    
    // الحصول على معلومات الفاتورة
    const sale = sales.find(s => s.id === saleId);
    const invoiceNumber = sale ? (sale.invoiceNumber || sale.id.slice(-8)) : 'غير محدد';

    // إنشاء سجل المرتجع
    const returnRecord = {
        id: generateId(),
        saleId: saleId,
        invoiceNumber: invoiceNumber,
        date: new Date().toISOString().split('T')[0],
        time: new Date().toTimeString().slice(0, 5),
        reason: reason,
        notes: notes,
        employeeId: employeeId,
        employeeName: employees.find(e => e.id === employeeId)?.name || 'غير محدد',
        items: returnItems,
        totalValue: returnItems.reduce((sum, item) => sum + safeNumber(item.returnQuantity * item.price, 0), 0),
        createdAt: new Date().toISOString()
    };
    
    // إضافة الكميات للمخزون
    returnItems.forEach(item => {
        updateProductStock(item.productId, item.returnQuantity, 'add');
    });
    
    // حفظ سجل المرتجع
    returns.push(returnRecord);
    saveData();
    
    // تحديث العرض
    displayProducts();
    displayProductsList();
    
    // إغلاق النافذة
    closeReturnModal();
    
    showNotification('تم تسجيل المرتجع بنجاح وإضافة الكميات للمخزون', 'success');
    
    // عرض تفاصيل المرتجع
    setTimeout(() => {
        showReturnDetails(returnRecord.id);
    }, 1000);
}

// إغلاق نافذة المرتجع
function closeReturnModal() {
    const modal = document.getElementById('returnModal');
    if (modal) {
        modal.remove();
    }
    window.currentReturnItems = null;
}

// عرض تفاصيل المرتجع
function showReturnDetails(returnId) {
    const returnRecord = returns.find(r => r.id === returnId);
    if (!returnRecord) return;
    
    let details = `تفاصيل المرتجع\n`;
    details += `رقم المرتجع: ${returnRecord.id.slice(-8)}\n`;
    details += `رقم الفاتورة الأصلية: ${returnRecord.invoiceNumber}\n`;
    details += `التاريخ: ${new Date(returnRecord.date).toLocaleDateString('ar-SA')}\n`;
    details += `الوقت: ${returnRecord.time}\n`;
    details += `السبب: ${returnRecord.reason}\n`;
    details += `الموظف المسئول: ${returnRecord.employeeName}\n`;
    details += `القيمة الإجمالية: ${formatCurrency(returnRecord.totalValue)}\n\n`;
    details += `الأصناف المرتجعة:\n`;
    
    returnRecord.items.forEach((item, index) => {
        const product = products.find(p => p.id === item.productId);
        const productName = product ? product.name : 'منتج محذوف';
        details += `${index + 1}. ${productName}: ${item.returnQuantity} ${item.unit} - ${(item.returnQuantity * item.price).toFixed(2)} ${settings.currencySymbol}\n`;
    });
    
    if (returnRecord.notes) {
        details += `\nملاحظات: ${returnRecord.notes}`;
    }
    
    alert(details);
}

// عرض قائمة المرتجعات
function displayReturnsList() {
    // هذه الوظيفة يمكن إضافتها لعرض قائمة شاملة بجميع المرتجعات
    if (returns.length === 0) {
        return '<p>لا توجد مرتجعات مسجلة</p>';
    }
    
    let html = '<div class="returns-table">';
    html += '<div class="table-header">';
    html += '<span>التاريخ</span><span>رقم الفاتورة</span><span>السبب</span><span>القيمة</span><span>الموظف</span><span>الإجراءات</span>';
    html += '</div>';
    
    returns.slice().reverse().forEach(returnRecord => {
        html += `<div class="table-row">`;
        html += `<span>${new Date(returnRecord.date).toLocaleDateString('ar-SA')}</span>`;
        html += `<span>${returnRecord.invoiceNumber || returnRecord.saleId.slice(-8)}</span>`;
        html += `<span>${returnRecord.reason}</span>`;
        html += `<span>${formatCurrency(returnRecord.totalValue)}</span>`;
        html += `<span>${returnRecord.employeeName}</span>`;
        html += `<span>`;
        html += `<button onclick="showReturnDetails('${returnRecord.id}')" class="view-btn">تفاصيل</button>`;
        html += `<button onclick="deleteReturn('${returnRecord.id}')" class="delete-btn">حذف</button>`;
        html += `</span>`;
        html += '</div>';
    });
    
    html += '</div>';
    return html;
}

// حذف مرتجع
function deleteReturn(returnId) {
    if (!confirm('هل أنت متأكد من حذف هذا المرتجع؟\nملاحظة: لن يتم خصم الكميات من المخزون')) return;
    
    const index = returns.findIndex(r => r.id === returnId);
    if (index !== -1) {
        returns.splice(index, 1);
        saveData();
        showNotification('تم حذف المرتجع بنجاح', 'success');
    }
}

// حساب إجمالي قيمة المرتجعات لفترة معينة
function calculateReturnsForPeriod(startDate, endDate) {
    return returns
        .filter(r => r.date >= startDate && r.date <= endDate)
        .reduce((sum, r) => sum + r.totalValue, 0);
}
