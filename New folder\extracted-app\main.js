// متغيرات عامة
let currentUser = null;
let cart = [];

// الجداول الجديدة حسب المتطلبات
let products = [];           // المنتجات
let suppliers = [];          // الموردون
let purchases = [];          // المشتريات
let purchaseItems = [];      // أصناف المشتريات
let sales = [];              // المبيعات
let saleItems = [];          // أصناف المبيعات
let customers = [];          // العملاء
let cashVouchers = [];       // سندات النقدية
let expenses = [];           // المصروفات
let customerPayments = [];   // مدفوعات العملاء

// الجداول الجديدة للميزات المطلوبة
let recipes = [];            // الوصفات التصنيعية
let manufacturing = [];      // عمليات التصنيع
let wastage = [];           // الهالك
let employees = [];         // الموظفين
let attendance = [];        // الحضور والانصراف
let returns = [];           // المرتجعات

// إعدادات النظام المحدثة
let settings = {
    // إعدادات الشركة
    companyName: 'تكنوفلاش لحلول المبيعات',
    activity: 'برمجيات مبيعات - نقاط بيع - أجهزة مكتبية',
    commercialRegister: '12345678',
    phone: '0123456789',
    address: 'مصر – القاهرة – شارع 123 – عمارة 456 – الدور 7',
    email: '<EMAIL>',
    customField1: 'إسماعيل جلال', // المالك / المدير
    customField2: '87654321', // الرقم الضريبي

    // إعدادات العملة
    mainCurrency: 'ريال سعودي',
    subCurrency: 'هللة',
    currencySymbol: 'ر.س',

    // إعدادات الضريبة
    isTaxable: true,
    taxRate: 0.15,
    companyTaxNumber: '',

    // إعدادات الفواتير
    showCompanyInInvoice: true,

    // إعدادات النظام
    password: '123'
};

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    loadData();
    initializeApp();

    // إعداد مستمعي أحداث Electron
    if (window.electronAPI) {
        // تصدير البيانات
        window.electronAPI.onExportData(() => {
            exportData();
        });

        // استيراد البيانات
        window.electronAPI.onImportData((_, filePath) => {
            importData(filePath);
        });

        // إظهار نافذة حول البرنامج
        window.electronAPI.onShowAbout(() => {
            showAboutDialog();
        });
    }
    
    // إضافة مستمع لضغطة Enter في حقل كلمة المرور
    document.getElementById('passwordInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            login();
        }
    });
    
    // تحديث تاريخ المصروف إلى اليوم الحالي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('expenseDate').value = today;
    
    // إعداد فلتر التقارير
    document.getElementById('reportPeriod').addEventListener('change', function() {
        const customRange = document.getElementById('customDateRange');
        if (this.value === 'custom') {
            customRange.classList.remove('hidden');
        } else {
            customRange.classList.add('hidden');
            generateReport();
        }
    });
});

// تحميل البيانات من localStorage
function loadData() {
    try {
        // تحميل المنتجات
        const savedProducts = localStorage.getItem('pos_products');
        if (savedProducts) {
            products = JSON.parse(savedProducts);
        } else {
            // بيانات تجريبية للمنتجات
            products = [
                {
                    id: 1,
                    name: 'قلم أزرق',
                    code: 'PEN001',
                    unit: 'قطعة',
                    stockQuantity: 50,
                    costPrice: 1.50,
                    sellPrice: 2.00,
                    createdAt: new Date().toISOString()
                },
                {
                    id: 2,
                    name: 'دفتر A4',
                    code: 'NOTE001',
                    unit: 'قطعة',
                    stockQuantity: 3,
                    costPrice: 8.00,
                    sellPrice: 12.00,
                    createdAt: new Date().toISOString()
                },
                {
                    id: 3,
                    name: 'مبراة',
                    code: 'SHARP001',
                    unit: 'قطعة',
                    stockQuantity: 25,
                    costPrice: 0.75,
                    sellPrice: 1.25,
                    createdAt: new Date().toISOString()
                }
            ];
            saveData();
        }

        // تحميل الموردون
        const savedSuppliers = localStorage.getItem('pos_suppliers');
        if (savedSuppliers) {
            suppliers = JSON.parse(savedSuppliers);
        }

        // تحميل المشتريات
        const savedPurchases = localStorage.getItem('pos_purchases');
        if (savedPurchases) {
            purchases = JSON.parse(savedPurchases);
        }

        // تحميل أصناف المشتريات
        const savedPurchaseItems = localStorage.getItem('pos_purchase_items');
        if (savedPurchaseItems) {
            purchaseItems = JSON.parse(savedPurchaseItems);
        }

        // تحميل المبيعات
        const savedSales = localStorage.getItem('pos_sales');
        if (savedSales) {
            sales = JSON.parse(savedSales);
        }

        // تحميل أصناف المبيعات
        const savedSaleItems = localStorage.getItem('pos_sale_items');
        if (savedSaleItems) {
            saleItems = JSON.parse(savedSaleItems);
        }

        // تحميل العملاء
        const savedCustomers = localStorage.getItem('pos_customers');
        if (savedCustomers) {
            customers = JSON.parse(savedCustomers);
        }

        // تحميل سندات النقدية
        const savedCashVouchers = localStorage.getItem('pos_cash_vouchers');
        if (savedCashVouchers) {
            cashVouchers = JSON.parse(savedCashVouchers);
        }

        // تحميل المصروفات
        const savedExpenses = localStorage.getItem('pos_expenses');
        if (savedExpenses) {
            expenses = JSON.parse(savedExpenses);
        }

        // تحميل مدفوعات العملاء
        const savedCustomerPayments = localStorage.getItem('pos_customer_payments');
        if (savedCustomerPayments) {
            customerPayments = JSON.parse(savedCustomerPayments);
        }

        // تحميل الإعدادات
        const savedSettings = localStorage.getItem('pos_settings');
        if (savedSettings) {
            const loadedSettings = JSON.parse(savedSettings);
            // الحفاظ على كلمة المرور الجديدة وعدم استبدالها بالقديمة
            settings = { ...settings, ...loadedSettings, password: '123' };
        }

        // تحميل السلة
        const savedCart = localStorage.getItem('pos_cart');
        if (savedCart) {
            cart = JSON.parse(savedCart);
        }

        // تحميل البيانات الجديدة
        const savedRecipes = localStorage.getItem('pos_recipes');
        if (savedRecipes) {
            recipes = JSON.parse(savedRecipes);
        }

        const savedManufacturing = localStorage.getItem('pos_manufacturing');
        if (savedManufacturing) {
            manufacturing = JSON.parse(savedManufacturing);
        }

        const savedWastage = localStorage.getItem('pos_wastage');
        if (savedWastage) {
            wastage = JSON.parse(savedWastage);
        }

        const savedEmployees = localStorage.getItem('pos_employees');
        if (savedEmployees) {
            employees = JSON.parse(savedEmployees);
        }

        const savedAttendance = localStorage.getItem('pos_attendance');
        if (savedAttendance) {
            attendance = JSON.parse(savedAttendance);
        }

        const savedReturns = localStorage.getItem('pos_returns');
        if (savedReturns) {
            returns = JSON.parse(savedReturns);
        }
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        showNotification('خطأ في تحميل البيانات', 'error');
    }
}

// حفظ البيانات في localStorage
function saveData() {
    try {
        localStorage.setItem('pos_products', JSON.stringify(products));
        localStorage.setItem('pos_suppliers', JSON.stringify(suppliers));
        localStorage.setItem('pos_purchases', JSON.stringify(purchases));
        localStorage.setItem('pos_purchase_items', JSON.stringify(purchaseItems));
        localStorage.setItem('pos_sales', JSON.stringify(sales));
        localStorage.setItem('pos_sale_items', JSON.stringify(saleItems));
        localStorage.setItem('pos_customers', JSON.stringify(customers));
        localStorage.setItem('pos_cash_vouchers', JSON.stringify(cashVouchers));
        localStorage.setItem('pos_expenses', JSON.stringify(expenses));
        localStorage.setItem('pos_customer_payments', JSON.stringify(customerPayments));
        localStorage.setItem('pos_settings', JSON.stringify(settings));
        localStorage.setItem('pos_cart', JSON.stringify(cart));

        // حفظ البيانات الجديدة
        localStorage.setItem('pos_recipes', JSON.stringify(recipes));
        localStorage.setItem('pos_manufacturing', JSON.stringify(manufacturing));
        localStorage.setItem('pos_wastage', JSON.stringify(wastage));
        localStorage.setItem('pos_employees', JSON.stringify(employees));
        localStorage.setItem('pos_attendance', JSON.stringify(attendance));
        localStorage.setItem('pos_returns', JSON.stringify(returns));

        // تحديث لوحة المعلومات إذا كانت مفتوحة
        if (typeof onDataChange === 'function') {
            onDataChange();
        }

        // تحديث المؤشرات السريعة
        if (typeof updateQuickIndicators === 'function') {
            updateQuickIndicators();
        }
    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
        showNotification('خطأ في حفظ البيانات', 'error');
    }
}

// تهيئة التطبيق
function initializeApp() {
    // عرض معلومات المطور في وحدة التحكم
    console.log('%c🎯 نظام إدارة نقاط البيع v1.0', 'color: #667eea; font-size: 16px; font-weight: bold;');
    console.log('%c📱 تم تطويره بواسطة تكنوفلاش', 'color: #74b9ff; font-size: 14px;');
    console.log('%c🔗 https://www.youtube.com/@Techno_flash', 'color: #00b894; font-size: 12px;');

    // إنشاء العميل الافتراضي "ضيف" إذا لم يكن موجوداً
    createDefaultGuestCustomer();

    // حفظ كلمة المرور الجديدة
    saveData();

    // تحديث إعدادات الواجهة
    updateSettingsForm();

    // تحديث عرض البيانات
    displayProducts();
    displayProductsList();
    displaySuppliersList();
    displayCustomersList();
    displayExpensesList();
    updateCart();
    checkLowStock();

    // تهيئة لوحة المعلومات
    initializeDashboard();

    // تهيئة الصفحات الجديدة
    if (typeof initializeManufacturing === 'function') initializeManufacturing();
    if (typeof initializeWastage === 'function') initializeWastage();
    if (typeof initializeEmployees === 'function') initializeEmployees();
    if (typeof initializeAttendance === 'function') initializeAttendance();
    if (typeof initializeProfit === 'function') initializeProfit();

    // تحديث عرض الضريبة
    refreshTaxDisplay();

    // تحديث المؤشرات السريعة
    updateQuickIndicators();

    // تحميل الثيم المحفوظ
    loadTheme();

    // تحديث التقارير
    generateReport();
}

// تحديث نموذج الإعدادات
function updateSettingsForm() {
    // إعدادات الشركة
    document.getElementById('companyName').value = settings.companyName || '';
    document.getElementById('activity').value = settings.activity || '';
    document.getElementById('commercialRegister').value = settings.commercialRegister || '';
    document.getElementById('companyPhone').value = settings.phone || '';
    document.getElementById('companyAddress').value = settings.address || '';
    document.getElementById('companyEmail').value = settings.email || '';
    document.getElementById('customField1').value = settings.customField1 || '';
    document.getElementById('customField2').value = settings.customField2 || '';

    // إعدادات العملة
    document.getElementById('mainCurrency').value = settings.mainCurrency || '';
    document.getElementById('subCurrency').value = settings.subCurrency || '';
    document.getElementById('currencySymbol').value = settings.currencySymbol || '';

    // إعدادات الضريبة
    document.getElementById('isTaxable').checked = settings.isTaxable || false;
    document.getElementById('taxRate').value = (settings.taxRate * 100) || 15;
    document.getElementById('companyTaxNumber').value = settings.companyTaxNumber || '';

    // إعدادات الفواتير
    document.getElementById('showCompanyInInvoice').checked = settings.showCompanyInInvoice !== false;
}

// تسجيل الدخول
function login() {
    const username = document.getElementById('usernameInput').value;
    const password = document.getElementById('passwordInput').value;
    const errorDiv = document.getElementById('loginError');

    // بيانات تسجيل الدخول الجديدة
    const validUsername = 'admin';
    const validPassword = 'admin123';

    if (username === validUsername && password === validPassword) {
        currentUser = 'admin';
        document.getElementById('loginScreen').classList.add('hidden');
        document.getElementById('mainApp').classList.remove('hidden');
        errorDiv.textContent = '';
        showNotification('مرحباً بك في StoreMaster', 'success');
    } else {
        errorDiv.textContent = 'اسم المستخدم أو كلمة المرور غير صحيحة';
        document.getElementById('usernameInput').value = '';
        document.getElementById('passwordInput').value = '';
    }
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        currentUser = null;
        cart = [];
        saveData();
        document.getElementById('loginScreen').classList.remove('hidden');
        document.getElementById('mainApp').classList.add('hidden');
        document.getElementById('passwordInput').value = '';
        showNotification('تم تسجيل الخروج بنجاح', 'info');
    }
}

// عرض التبويبات
function showTab(tabName, clickedElement) {
    // إخفاء جميع التبويبات
    const tabs = document.querySelectorAll('.tab-content');
    tabs.forEach(tab => tab.classList.remove('active'));

    // إزالة الفئة النشطة من جميع أزرار التنقل
    const navTabs = document.querySelectorAll('.nav-tab');
    navTabs.forEach(tab => tab.classList.remove('active'));

    // عرض التبويب المحدد
    document.getElementById(tabName + 'Tab').classList.add('active');

    // إضافة الفئة النشطة للزر المحدد
    if (clickedElement) {
        clickedElement.classList.add('active');
    } else {
        // البحث عن الزر المناسب إذا لم يتم تمريره
        const targetButton = document.querySelector(`[onclick*="showTab('${tabName}')"]`);
        if (targetButton) {
            targetButton.classList.add('active');
        }
    }
    
    // تحديث البيانات حسب التبويب
    switch(tabName) {
        case 'dashboard':
            updateDashboard();
            refreshDashboardStats();
            break;
        case 'purchases':
            document.getElementById('purchasesContent').innerHTML = showPurchasesPage();
            break;
        case 'sales':
            displayProducts();
            updateCart();
            checkLowStock();
            break;
        case 'products':
            displayProductsList();
            break;
        case 'suppliers':
            displaySuppliersList();
            break;
        case 'customers':
            displayCustomersList();
            break;
        case 'debts':
            displayDebtsPage();
            break;
        case 'expenses':
            displayExpensesList();
            break;
        case 'reports':
            generateReport();
            break;
        case 'settings':
            updateSettingsForm();
            break;
        case 'manufacturing':
            if (typeof initializeManufacturing === 'function') initializeManufacturing();
            break;
        case 'wastage':
            if (typeof initializeWastage === 'function') initializeWastage();
            break;
        case 'employees':
            if (typeof initializeEmployees === 'function') initializeEmployees();
            break;
        case 'attendance':
            if (typeof initializeAttendance === 'function') initializeAttendance();
            break;
        case 'profit':
            if (typeof initializeProfit === 'function') initializeProfit();
            break;
    }
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#00b894' : type === 'error' ? '#e17055' : '#74b9ff'};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-weight: bold;
        max-width: 300px;
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;
    
    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);
    
    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// إضافة أنماط الحركة للإشعارات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// تنسيق العملة
function formatCurrency(amount) {
    return `${amount.toFixed(2)} ${settings.currencySymbol || 'ر.س'}`;
}

// الحصول على نسبة الضريبة للعرض
function getTaxRateDisplay() {
    return convertToArabicNumerals((settings.taxRate * 100).toFixed(0)) + '%';
}

// الحصول على تسمية الضريبة مع النسبة (للإعدادات والتقارير)
function getTaxLabel() {
    return `الضريبة (${getTaxRateDisplay()})`;
}

// الحصول على تسمية الضريبة للمبيعات (بدون نسبة مئوية)
function getSalesTaxLabel() {
    return 'الضريبة';
}

// تحديث عرض الضريبة في الصفحة النشطة
function refreshTaxDisplay() {
    // تحديث صفحة المبيعات إذا كانت مفتوحة
    if (document.getElementById('salesTab').classList.contains('active')) {
        const taxLabelElement = document.getElementById('taxLabel');
        if (taxLabelElement) {
            taxLabelElement.textContent = getSalesTaxLabel() + ':';
        }
        updateCart(); // إعادة حساب الضريبة
    }
}

// تحديث عرض العملة في جميع عناصر الصفحة الحالية
function refreshCurrencyDisplay() {
    // تحديث لوحة المعلومات إذا كانت مفتوحة
    if (document.getElementById('dashboardTab').classList.contains('active')) {
        updateDashboard();
        refreshDashboardStats();
    }

    // تحديث صفحة المبيعات إذا كانت مفتوحة
    if (document.getElementById('salesTab').classList.contains('active')) {
        updateCart();
    }

    // تحديث صفحة المنتجات إذا كانت مفتوحة
    if (document.getElementById('productsTab').classList.contains('active')) {
        displayProducts();
        displayProductsList();
    }

    // تحديث صفحة الموردين إذا كانت مفتوحة
    if (document.getElementById('suppliersTab').classList.contains('active')) {
        displaySuppliersList();
    }

    // تحديث صفحة العملاء إذا كانت مفتوحة
    if (document.getElementById('customersTab').classList.contains('active')) {
        displayCustomersList();
    }

    // تحديث صفحة المصروفات إذا كانت مفتوحة
    if (document.getElementById('expensesTab').classList.contains('active')) {
        displayExpensesList();
    }

    // تحديث المؤشرات السريعة
    updateQuickIndicators();
}

// تحويل الأرقام الإنجليزية إلى العربية الهندية
function convertToArabicNumerals(str) {
    const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return str.replace(/[0-9]/g, function(match) {
        return arabicNumerals[parseInt(match)];
    });
}

// أسماء الأشهر العربية
const arabicMonths = [
    "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
    "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
];

// تنسيق التاريخ الميلادي بالعربية (مثال: ١٥ يناير ٢٠٢٤)
function formatDate(date) {
    const d = new Date(date);
    const day = convertToArabicNumerals(d.getDate().toString());
    const month = arabicMonths[d.getMonth()];
    const year = convertToArabicNumerals(d.getFullYear().toString());
    return `${day} ${month} ${year}`;
}

// تنسيق التاريخ المختصر الميلادي (مثال: ٢٠٢٤/٠١/١٥)
function formatDateShort(date) {
    const d = new Date(date);
    const day = d.getDate().toString().padStart(2, '0');
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const year = d.getFullYear().toString();
    return convertToArabicNumerals(`${year}/${month}/${day}`);
}

// إنشاء معرف فريد
function generateId() {
    return Date.now() + Math.random().toString(36).substring(2, 11);
}

// التحقق من صحة البيانات
function validateInput(value, type) {
    switch(type) {
        case 'text':
            return value && value.trim().length > 0;
        case 'number':
            return !isNaN(value) && parseFloat(value) > 0;
        case 'email':
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return !value || emailRegex.test(value);
        case 'phone':
            const phoneRegex = /^[0-9+\-\s()]+$/;
            return !value || phoneRegex.test(value);
        default:
            return true;
    }
}

// تنظيف النص
function sanitizeInput(input) {
    return input.toString().trim().replace(/[<>]/g, '');
}

// البحث في المصفوفة
function searchArray(array, searchTerm, fields) {
    if (!searchTerm) return array;
    
    const term = searchTerm.toLowerCase();
    return array.filter(item => {
        return fields.some(field => {
            const value = item[field];
            return value && value.toString().toLowerCase().includes(term);
        });
    });
}

// ترتيب المصفوفة
function sortArray(array, field, direction = 'asc') {
    return array.sort((a, b) => {
        const aVal = a[field];
        const bVal = b[field];
        
        if (direction === 'asc') {
            return aVal > bVal ? 1 : -1;
        } else {
            return aVal < bVal ? 1 : -1;
        }
    });
}

// تصدير البيانات
async function exportData() {
    const data = {
        products,
        customers,
        expenses,
        sales,
        customerPayments,
        suppliers,
        purchases,
        purchaseItems,
        settings: { ...settings, password: undefined }, // استبعاد كلمة المرور
        exportDate: new Date().toISOString()
    };

    const jsonData = JSON.stringify(data, null, 2);
    const filename = `pos_backup_${new Date().toISOString().split('T')[0]}.json`;

    // التحقق من وجود Electron API
    if (window.electronAPI) {
        try {
            const result = await window.electronAPI.saveFile(jsonData, filename);
            if (result.success) {
                showNotification('تم تصدير البيانات بنجاح', 'success');
            } else {
                showNotification('فشل في تصدير البيانات', 'error');
            }
        } catch (error) {
            showNotification('خطأ في تصدير البيانات', 'error');
        }
    } else {
        // الطريقة التقليدية للمتصفح
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
        showNotification('تم تصدير البيانات بنجاح', 'success');
    }
}

// استيراد البيانات
async function importData(file) {
    try {
        let jsonData;

        // التحقق من وجود Electron API
        if (window.electronAPI && typeof file === 'string') {
            // في Electron، file هو مسار الملف
            const result = await window.electronAPI.readFile(file);
            if (!result.success) {
                showNotification('فشل في قراءة الملف', 'error');
                return;
            }
            jsonData = result.data;
        } else {
            // في المتصفح، file هو File object
            jsonData = await new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target.result);
                reader.onerror = reject;
                reader.readAsText(file);
            });
        }

        const data = JSON.parse(jsonData);

        if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
            if (data.products) products = data.products;
            if (data.suppliers) suppliers = data.suppliers;
            if (data.purchases) purchases = data.purchases;
            if (data.purchaseItems) purchaseItems = data.purchaseItems;
            if (data.sales) sales = data.sales;
            if (data.saleItems) saleItems = data.saleItems;
            if (data.customers) customers = data.customers;
            if (data.cashVouchers) cashVouchers = data.cashVouchers;
            if (data.expenses) expenses = data.expenses;
            if (data.customerPayments) customerPayments = data.customerPayments;
            if (data.settings) settings = { ...settings, ...data.settings, password: '123' };

            saveData();
            initializeApp();
            showNotification('تم استيراد البيانات بنجاح', 'success');
        }
    } catch (error) {
        showNotification('خطأ في تنسيق ملف البيانات', 'error');
        console.error('Import error:', error);
    }
}

// حفظ جميع الإعدادات
function saveAllSettings() {
    try {
        // إعدادات الشركة
        settings.companyName = document.getElementById('companyName').value.trim();
        settings.activity = document.getElementById('activity').value.trim();
        settings.commercialRegister = document.getElementById('commercialRegister').value.trim();
        settings.phone = document.getElementById('companyPhone').value.trim();
        settings.address = document.getElementById('companyAddress').value.trim();
        settings.email = document.getElementById('companyEmail').value.trim();
        settings.customField1 = document.getElementById('customField1').value.trim();
        settings.customField2 = document.getElementById('customField2').value.trim();

        // إعدادات العملة
        settings.mainCurrency = document.getElementById('mainCurrency').value.trim();
        settings.subCurrency = document.getElementById('subCurrency').value.trim();
        settings.currencySymbol = document.getElementById('currencySymbol').value.trim();

        // إعدادات الضريبة
        settings.isTaxable = document.getElementById('isTaxable').checked;
        settings.taxRate = parseFloat(document.getElementById('taxRate').value) / 100 || 0.15;
        settings.companyTaxNumber = document.getElementById('companyTaxNumber').value.trim();

        // إعدادات الفواتير
        settings.showCompanyInInvoice = document.getElementById('showCompanyInInvoice').checked;

        // حفظ البيانات
        saveData();

        // تحديث عرض العملة والضريبة فورياً
        refreshCurrencyDisplay();
        refreshTaxDisplay();

        showNotification('تم حفظ الإعدادات بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في حفظ الإعدادات:', error);
        showNotification('حدث خطأ أثناء حفظ الإعدادات', 'error');
    }
}

// تغيير كلمة المرور
function changePassword() {
    const currentPassword = document.getElementById('currentPassword').value.trim();
    const newPassword = document.getElementById('newPassword').value.trim();
    const confirmPassword = document.getElementById('confirmPassword').value.trim();

    // التحقق من صحة البيانات
    if (!currentPassword) {
        showNotification('يرجى إدخال كلمة المرور الحالية', 'error');
        return;
    }

    if (currentPassword !== settings.password) {
        showNotification('كلمة المرور الحالية غير صحيحة', 'error');
        return;
    }

    if (!newPassword || newPassword.length < 6) {
        showNotification('يرجى إدخال كلمة مرور جديدة (6 أحرف على الأقل)', 'error');
        return;
    }

    if (newPassword !== confirmPassword) {
        showNotification('كلمة المرور الجديدة وتأكيدها غير متطابقين', 'error');
        return;
    }

    try {
        settings.password = newPassword;
        saveData();

        // مسح الحقول
        document.getElementById('currentPassword').value = '';
        document.getElementById('newPassword').value = '';
        document.getElementById('confirmPassword').value = '';

        showNotification('تم تغيير كلمة المرور بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في تغيير كلمة المرور:', error);
        showNotification('حدث خطأ أثناء تغيير كلمة المرور', 'error');
    }
}

// تحديث المؤشرات السريعة
function updateQuickIndicators() {
    const totalProducts = products.length;
    const lowStockProducts = products.filter(p => p.stockQuantity <= 5).length;
    const totalCustomers = customers.length;
    const totalSuppliers = suppliers.length;

    document.getElementById('totalProductsIndicator').textContent = convertToArabicNumerals(totalProducts.toString());
    document.getElementById('lowStockIndicator').textContent = convertToArabicNumerals(lowStockProducts.toString());
    document.getElementById('customersIndicator').textContent = convertToArabicNumerals(totalCustomers.toString());
    document.getElementById('suppliersIndicator').textContent = convertToArabicNumerals(totalSuppliers.toString());
}

// تبديل الثيم
function toggleTheme() {
    const body = document.body;
    const themeToggle = document.getElementById('themeToggle');

    if (body.classList.contains('dark-theme')) {
        body.classList.remove('dark-theme');
        themeToggle.textContent = '🌙';
        localStorage.setItem('theme', 'light');
    } else {
        body.classList.add('dark-theme');
        themeToggle.textContent = '☀️';
        localStorage.setItem('theme', 'dark');
    }
}

// تحميل الثيم المحفوظ
function loadTheme() {
    const savedTheme = localStorage.getItem('theme');
    const body = document.body;
    const themeToggle = document.getElementById('themeToggle');

    if (savedTheme === 'dark') {
        body.classList.add('dark-theme');
        themeToggle.textContent = '☀️';
    } else {
        body.classList.remove('dark-theme');
        themeToggle.textContent = '🌙';
    }
}

// عرض نافذة حول البرنامج
function showAboutDialog() {
    document.getElementById('aboutModal').classList.remove('hidden');
}

// إغلاق نافذة حول البرنامج
function closeAboutModal() {
    document.getElementById('aboutModal').classList.add('hidden');
}

// إنشاء العميل الافتراضي "ضيف"
function createDefaultGuestCustomer() {
    const guestExists = customers.find(c => c.id === 'guest');
    if (!guestExists) {
        const guestCustomer = {
            id: 'guest',
            name: 'ضيف',
            phone: '',
            email: '',
            address: '',
            currentBalance: 0,
            totalPurchases: 0,
            lastTransactionDate: null,
            isGuest: true,
            createdAt: new Date().toISOString()
        };
        customers.unshift(guestCustomer); // إضافة في البداية
        saveData();
    }
}

// فتح صفحة الفيسبوك (للشركة)
function openFacebookPage() {
    const facebookUrl = 'https://web.facebook.com/profile.php?id=61578888731370';

    // محاولة فتح الرابط في نافذة جديدة
    try {
        window.open(facebookUrl, '_blank');
        showNotification('تم فتح صفحة الفيسبوك في نافذة جديدة', 'success');
    } catch (error) {
        // في حالة فشل فتح النافذة، نسخ الرابط للحافظة
        navigator.clipboard.writeText(facebookUrl).then(() => {
            showNotification('تم نسخ رابط صفحة الفيسبوك للحافظة', 'info');
        }).catch(() => {
            // في حالة فشل النسخ، عرض الرابط في تنبيه
            alert('رابط صفحة الفيسبوك:\n' + facebookUrl);
        });
    }
}

// فتح الواتساب (للمطور)
function openWhatsApp() {
    const phoneNumber = '01225396729';
    const message = 'مرحباً، أحتاج مساعدة في StoreMaster';
    const whatsappUrl = `https://wa.me/2${phoneNumber}?text=${encodeURIComponent(message)}`;

    try {
        window.open(whatsappUrl, '_blank');
        showNotification('تم فتح الواتساب', 'success');
    } catch (error) {
        // في حالة فشل فتح الرابط، عرض رقم الهاتف
        alert(`رقم الواتساب: ${phoneNumber}\nالرسالة: ${message}`);
    }
}

// فتح فيسبوك المطور
function openFacebookDeveloper() {
    const facebookUrl = 'https://web.facebook.com/profile.php?id=61578888731370';

    try {
        window.open(facebookUrl, '_blank');
        showNotification('تم فتح صفحة المطور على الفيسبوك', 'success');
    } catch (error) {
        navigator.clipboard.writeText(facebookUrl).then(() => {
            showNotification('تم نسخ رابط صفحة المطور للحافظة', 'info');
        }).catch(() => {
            alert('رابط صفحة المطور:\n' + facebookUrl);
        });
    }
}

// دالة مساعدة لمعالجة القيم المالية وتجنب NaN
function safeNumber(value, defaultValue = 0) {
    const num = parseFloat(value);
    return isNaN(num) || !isFinite(num) ? defaultValue : num;
}

// دالة لتنسيق العملة مع تجنب NaN
function formatCurrency(value, showSymbol = true) {
    const num = safeNumber(value, 0);
    const formatted = num.toFixed(2);
    return showSymbol ? `${formatted} ${settings.currencySymbol}` : formatted;
}

// دالة للحصول على سعر التكلفة الآمن
function getSafeCostPrice(product) {
    if (!product) return 0;
    return safeNumber(product.costPrice, 0);
}

// دالة للحصول على سعر البيع الآمن
function getSafeSellingPrice(product) {
    if (!product) return 0;
    return safeNumber(product.sellingPrice, 0);
}

// دالة للحصول على الكمية الآمنة
function getSafeQuantity(product) {
    if (!product) return 0;
    return safeNumber(product.stockQuantity, 0);
}

// تحديث مخزون المنتج
function updateProductStock(productId, quantity, operation) {
    const product = products.find(p => p.id === productId);
    if (!product) {
        console.error('المنتج غير موجود:', productId);
        return false;
    }

    const safeQty = safeNumber(quantity, 0);
    const currentStock = getSafeQuantity(product);

    if (operation === 'add') {
        product.stockQuantity = currentStock + safeQty;
    } else if (operation === 'subtract') {
        if (currentStock < safeQty) {
            console.error('الكمية المتوفرة غير كافية:', product.name);
            return false;
        }
        product.stockQuantity = currentStock - safeQty;
    }

    // التأكد من عدم وجود كميات سالبة
    if (product.stockQuantity < 0) {
        product.stockQuantity = 0;
    }

    return true;
}
