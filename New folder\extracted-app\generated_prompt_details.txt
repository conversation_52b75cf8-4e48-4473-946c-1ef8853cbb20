===============================================
    تفاصيل قوالب الفواتير - نظام POS
===============================================

هذا الملف يحتوي على تفاصيل قوالب الفواتير المستخدمة في النظام
وطرق تخصيصها وتعديلها حسب احتياجات المتجر.

===============================================
    القالب الافتراضي للفاتورة
===============================================

الفاتورة تتضمن العناصر التالية:

1. رأس الفاتورة:
   - عنوان "فاتورة بيع"
   - رقم الفاتورة (تلقائي)
   - تاريخ ووقت الإصدار

2. جدول المنتجات:
   - اسم المنتج
   - الكمية
   - سعر الوحدة
   - المجموع الفرعي
   - الربح لكل منتج

3. الإجماليات:
   - المجموع الفرعي
   - الضريبة (15%)
   - المجموع النهائي
   - إجمالي الربح

4. ذيل الفاتورة:
   - رسالة شكر
   - معلومات إضافية (اختيارية)

===============================================
    تخصيص الفاتورة
===============================================

يمكن تخصيص الفاتورة من خلال تعديل الكود في ملف app.js
في دالة generateInvoice():

1. تغيير رأس الفاتورة:
   - أضف اسم المتجر
   - أضف الشعار
   - أضف معلومات الاتصال

مثال:
```html
<div class="invoice-header">
    <h1>متجر الأحلام للقرطاسية</h1>
    <h2>فاتورة بيع</h2>
    <div>هاتف: 0501234567 | العنوان: الرياض</div>
    <div>رقم الفاتورة: ${sale.invoiceNumber}</div>
    <div>التاريخ: ${formatDate(sale.date)}</div>
</div>
```

2. تخصيص الألوان والخطوط:
   - عدّل ملف CSS في دالة printInvoice()
   - غيّر الألوان والخطوط حسب هوية المتجر

3. إضافة معلومات إضافية:
   - رقم السجل التجاري
   - الرقم الضريبي
   - شروط وأحكام البيع

===============================================
    أنواع الفواتير المدعومة
===============================================

1. فاتورة البيع العادية:
   - تتضمن جميع تفاصيل المنتجات
   - حساب الضريبة
   - عرض الأرباح

2. فاتورة مبسطة:
   - تتضمن المنتجات والأسعار فقط
   - بدون تفاصيل الأرباح

3. فاتورة ضريبية:
   - تتضمن تفاصيل الضريبة
   - رقم ضريبي للمتجر
   - تفاصيل إضافية للامتثال الضريبي

===============================================
    إعدادات الطباعة
===============================================

الفاتورة مُحسّنة للطباعة على:
- ورق A4 عادي
- ورق حراري 80mm (للطابعات الحرارية)
- ورق حراري 58mm (للطابعات المحمولة)

إعدادات CSS للطباعة:
```css
@media print {
    body { margin: 0; font-size: 12px; }
    .invoice-header { page-break-after: avoid; }
    .invoice-table { page-break-inside: avoid; }
}
```

===============================================
    تخصيص حسب نوع المتجر
===============================================

1. متجر قرطاسية:
   - إضافة معلومات الضمان
   - سياسة الإرجاع والاستبدال
   - عروض خاصة

2. متجر مواد غذائية:
   - تاريخ الانتهاء
   - معلومات التخزين
   - تحذيرات صحية

3. متجر ملابس:
   - معلومات المقاسات
   - تعليمات الغسيل
   - سياسة الإرجاع

===============================================
    إضافة الشعار والهوية البصرية
===============================================

لإضافة شعار المتجر:

1. احفظ الشعار كصورة (PNG/JPG)
2. أضف الكود التالي في رأس الفاتورة:

```html
<div class="store-logo">
    <img src="logo.png" alt="شعار المتجر" style="max-width: 100px; height: auto;">
</div>
```

3. تأكد من وضع ملف الشعار في نفس مجلد النظام

===============================================
    معلومات الاتصال والعنوان
===============================================

أضف معلومات المتجر في رأس الفاتورة:

```html
<div class="store-info">
    <h2>اسم المتجر</h2>
    <div>العنوان: شارع الملك فهد، الرياض</div>
    <div>هاتف: 011-1234567 | جوال: 0501234567</div>
    <div>البريد الإلكتروني: <EMAIL></div>
    <div>الموقع الإلكتروني: www.store.com</div>
</div>
```

===============================================
    الرقم الضريبي والسجل التجاري
===============================================

للامتثال للأنظمة الضريبية:

```html
<div class="tax-info">
    <div>الرقم الضريبي: 123456789012345</div>
    <div>السجل التجاري: 1234567890</div>
    <div>رقم الترخيص: TR-2024-001</div>
</div>
```

===============================================
    رسائل مخصصة
===============================================

يمكن إضافة رسائل مخصصة في نهاية الفاتورة:

1. رسالة شكر:
```html
<div class="thank-you-message">
    شكراً لتعاملكم معنا
    نتطلع لخدمتكم مرة أخرى
</div>
```

2. معلومات الضمان:
```html
<div class="warranty-info">
    ضمان المنتجات: 30 يوم من تاريخ الشراء
    للاستفسارات: 0501234567
</div>
```

3. عروض خاصة:
```html
<div class="special-offers">
    عرض خاص: خصم 10% على المشتريات التالية
    صالح حتى نهاية الشهر
</div>
```

===============================================
    تنسيق الأرقام والعملة
===============================================

النظام يدعم تنسيق العملة تلقائياً:
- الرمز: قابل للتخصيص من الإعدادات
- الافتراضي: ريال سعودي (ر.س)
- التنسيق: 123.45 ر.س

لتغيير تنسيق العملة، عدّل دالة formatCurrency في main.js

===============================================
    طباعة متعددة النسخ
===============================================

لطباعة نسخ متعددة من الفاتورة:

1. نسخة للعميل
2. نسخة للمحاسبة
3. نسخة للأرشيف

يمكن إضافة هذا في دالة printInvoice():

```javascript
// طباعة 3 نسخ
for(let i = 0; i < 3; i++) {
    printWindow.print();
}
```

===============================================
    حفظ الفواتير كملفات PDF
===============================================

لحفظ الفواتير كملفات PDF، يمكن استخدام:

1. خيار "طباعة إلى PDF" في المتصفح
2. مكتبات JavaScript مثل jsPDF
3. خدمات تحويل HTML إلى PDF

===============================================
    نصائح للتخصيص
===============================================

1. احتفظ بنسخة احتياطية قبل التعديل
2. اختبر الطباعة على ورق فعلي
3. تأكد من وضوح النص والأرقام
4. استخدم خطوط واضحة ومقروءة
5. تجنب الألوان الداكنة لتوفير الحبر

===============================================
    استكشاف أخطاء الطباعة
===============================================

مشاكل شائعة وحلولها:

1. النص مقطوع:
   - تحقق من هوامش الصفحة
   - قلل حجم الخط

2. الجدول لا يظهر بشكل صحيح:
   - تحقق من عرض الأعمدة
   - استخدم CSS للطباعة

3. الشعار لا يظهر:
   - تحقق من مسار الصورة
   - استخدم صور بحجم مناسب

===============================================
