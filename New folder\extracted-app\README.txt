===============================================
    نظام إدارة نقاط البيع (POS System)
===============================================

نظام متكامل لإدارة المبيعات والمخزون والمصروفات والعملاء
يدعم اللغة العربية بالكامل ويوفر واجهة مستخدم بديهية

===============================================
    متطلبات التشغيل
===============================================

- متصفح ويب حديث (Chrome, Firefox, Edge, Safari)
- لا يتطلب اتصال بالإنترنت (يعمل محلياً)
- مساحة تخزين محلية في المتصفح

===============================================
    طريقة التشغيل
===============================================

1. افتح ملف index.html في متصفح الويب
2. أدخل كلمة المرور الافتراضية: admin123
3. ابدأ في استخدام النظام

===============================================
    المميزات الرئيسية
===============================================

✓ إدارة المنتجات:
  - إضافة/تعديل/حذف المنتجات
  - تتبع المخزون والكميات
  - تنبيهات للمخزون المنخفض
  - تصنيف المنتجات

✓ نظام المبيعات:
  - سلة مشتريات ديناميكية
  - حساب الضرائب تلقائياً (15%)
  - إنشاء وطباعة الفواتير
  - تتبع الأرباح لكل عملية بيع

✓ إدارة العملاء:
  - سجل العملاء مع معلومات الاتصال
  - إضافة/تعديل/حذف العملاء

✓ إدارة المصروفات:
  - تسجيل المصروفات بتواريخها
  - تصنيف المصروفات
  - تضمين المصروفات في حساب الأرباح

✓ التقارير المالية:
  - تقارير يومية وأسبوعية وشهرية
  - تقارير مخصصة لفترات محددة
  - حساب صافي الأرباح (المبيعات - المصروفات)
  - إحصائيات مفصلة

✓ الإعدادات:
  - تغيير العملة
  - تغيير كلمة المرور
  - إعادة ضبط البيانات
  - نسخ احتياطية

===============================================
    دليل الاستخدام السريع
===============================================

1. تسجيل الدخول:
   - كلمة المرور الافتراضية: admin123

2. إضافة منتجات:
   - انتقل لتبويب "المنتجات"
   - اضغط "إضافة منتج جديد"
   - أدخل البيانات المطلوبة
   - احرص على أن يكون سعر البيع أكبر من سعر الشراء

3. عملية البيع:
   - في تبويب "المبيعات"
   - اضغط على المنتجات لإضافتها للسلة
   - استخدم أزرار + و - لتعديل الكميات
   - اضغط "إنهاء عملية البيع" لطباعة الفاتورة

4. إدارة المصروفات:
   - انتقل لتبويب "المصروفات"
   - سجل جميع مصروفاتك لحساب صافي الربح

5. مراجعة التقارير:
   - تبويب "التقارير" يعرض الأداء المالي
   - اختر الفترة الزمنية المطلوبة

===============================================
    نصائح مهمة
===============================================

⚠️ النسخ الاحتياطية:
- أنشئ نسخة احتياطية بانتظام من الإعدادات
- البيانات محفوظة محلياً في المتصفح
- مسح بيانات المتصفح سيحذف جميع البيانات

⚠️ المخزون:
- تنبيهات تظهر عند انخفاض المخزون لأقل من 5 قطع
- تحقق من المخزون بانتظام
- المنتجات منخفضة المخزون تظهر بلون مختلف

⚠️ الأرباح:
- تأكد من إدخال أسعار الشراء والبيع بدقة
- سجل جميع المصروفات لحساب صافي الربح
- راجع التقارير بانتظام

===============================================
    استكشاف الأخطاء
===============================================

❌ مشكلة: لا تظهر البيانات
✅ الحل: تأكد من تمكين JavaScript في المتصفح

❌ مشكلة: لا تعمل الطباعة
✅ الحل: تأكد من السماح للنوافذ المنبثقة

❌ مشكلة: فقدان البيانات
✅ الحل: استعد النسخة الاحتياطية من الإعدادات

❌ مشكلة: بطء في الأداء
✅ الحل: امسح بيانات المتصفح القديمة

===============================================
    الأمان
===============================================

🔒 كلمة المرور:
- غيّر كلمة المرور الافتراضية
- استخدم كلمة مرور قوية
- لا تشارك كلمة المرور مع الآخرين

🔒 البيانات:
- البيانات محفوظة محلياً فقط
- لا ترسل أي بيانات عبر الإنترنت
- أنشئ نسخ احتياطية منتظمة

===============================================
    الدعم الفني
===============================================

للمساعدة أو الاستفسارات:
- راجع هذا الملف أولاً
- تحقق من إعدادات المتصفح
- جرب إعادة تحميل الصفحة

===============================================
    معلومات النسخة
===============================================

الإصدار: 1.0
التاريخ: 2024
اللغة: العربية
التقنيات: HTML5, CSS3, JavaScript
التخزين: localStorage (محلي)

===============================================
    حقوق الطبع والنشر
===============================================

هذا النظام مطور للاستخدام التجاري والشخصي
جميع الحقوق محفوظة

===============================================
