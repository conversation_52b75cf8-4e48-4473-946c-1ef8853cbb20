// ===== إدارة الموردين =====

// عرض قائمة الموردين
function displaySuppliersList() {
    const list = document.getElementById('suppliersList');
    list.innerHTML = '';
    
    if (suppliers.length === 0) {
        list.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">لا توجد موردين مضافين</div>';
        return;
    }
    
    // إضافة رأس الجدول
    const header = document.createElement('div');
    header.className = 'supplier-item';
    header.style.fontWeight = 'bold';
    header.style.background = '#d0d5dc';
    header.innerHTML = `
        <div>اسم التاجر</div>
        <div>الرصيد المستحق</div>
        <div>البيان</div>
        <div>تاريخ الإضافة</div>
        <div>الإجراءات</div>
    `;
    list.appendChild(header);
    
    // إضافة الموردين
    suppliers.forEach(supplier => {
        const supplierItem = document.createElement('div');
        supplierItem.className = 'supplier-item';
        supplierItem.innerHTML = `
            <div>${supplier.name}</div>
            <div class="${supplier.balance > 0 ? 'debt-amount' : ''}">${formatCurrency(supplier.balance)}</div>
            <div>${supplier.notes || '-'}</div>
            <div>${formatDateShort(supplier.createdAt)}</div>
            <div class="actions">
                <button onclick="editSupplier('${supplier.id}')" class="btn-small">تعديل</button>
                <button onclick="deleteSupplier('${supplier.id}')" class="btn-small btn-danger">حذف</button>
                <button onclick="viewSupplierTransactions('${supplier.id}')" class="btn-small btn-info">المعاملات</button>
            </div>
        `;
        list.appendChild(supplierItem);
    });
}

// إظهار نموذج إضافة مورد
function showAddSupplierForm() {
    document.getElementById('addSupplierForm').classList.remove('hidden');
    document.getElementById('supplierName').focus();
}

// إخفاء نموذج إضافة مورد
function hideAddSupplierForm() {
    document.getElementById('addSupplierForm').classList.add('hidden');
    clearSupplierForm();
}

// مسح نموذج المورد
function clearSupplierForm() {
    document.getElementById('supplierName').value = '';
    document.getElementById('supplierNotes').value = '';
}

// إضافة مورد جديد
function addSupplier() {
    const name = document.getElementById('supplierName').value.trim();
    const notes = document.getElementById('supplierNotes').value.trim();
    
    // التحقق من صحة البيانات
    if (!validateInput(name, 'text')) {
        showNotification('يرجى إدخال اسم التاجر', 'error');
        return;
    }
    
    // التحقق من عدم تكرار الاسم
    if (suppliers.some(s => s.name.toLowerCase() === name.toLowerCase())) {
        showNotification('اسم التاجر موجود مسبقاً', 'error');
        return;
    }
    
    try {
        // إنشاء المورد الجديد
        const supplier = createSupplier(name, notes);
        
        suppliers.push(supplier);
        saveData();
        displaySuppliersList();
        hideAddSupplierForm();
        showNotification('تم إضافة المورد بنجاح', 'success');
        
    } catch (error) {
        console.error('خطأ في إضافة المورد:', error);
        showNotification('حدث خطأ أثناء إضافة المورد', 'error');
    }
}

// تعديل مورد
function editSupplier(id) {
    const supplier = suppliers.find(s => s.id === id);
    if (!supplier) return;
    
    // ملء النموذج بالبيانات الحالية
    document.getElementById('supplierName').value = supplier.name;
    document.getElementById('supplierNotes').value = supplier.notes;
    
    // إظهار النموذج
    showAddSupplierForm();
    
    // تغيير زر الإضافة إلى تحديث
    const addButton = document.querySelector('#addSupplierForm .form-actions .neumorphic-button.primary');
    addButton.textContent = 'تحديث المورد';
    addButton.onclick = () => updateSupplier(id);
}

// تحديث مورد
function updateSupplier(id) {
    const name = document.getElementById('supplierName').value.trim();
    const notes = document.getElementById('supplierNotes').value.trim();
    
    // التحقق من صحة البيانات
    if (!validateInput(name, 'text')) {
        showNotification('يرجى إدخال اسم التاجر', 'error');
        return;
    }
    
    // التحقق من عدم تكرار الاسم (باستثناء المورد الحالي)
    if (suppliers.some(s => s.id !== id && s.name.toLowerCase() === name.toLowerCase())) {
        showNotification('اسم التاجر موجود مسبقاً', 'error');
        return;
    }
    
    // تحديث المورد
    const supplierIndex = suppliers.findIndex(s => s.id === id);
    if (supplierIndex !== -1) {
        suppliers[supplierIndex] = {
            ...suppliers[supplierIndex],
            name,
            notes,
            updatedAt: new Date().toISOString()
        };
        
        saveData();
        displaySuppliersList();
        hideAddSupplierForm();
        
        // إعادة تعيين زر الإضافة
        const addButton = document.querySelector('#addSupplierForm .form-actions .neumorphic-button.primary');
        addButton.textContent = 'إضافة المورد';
        addButton.onclick = addSupplier;
        
        showNotification('تم تحديث المورد بنجاح', 'success');
    }
}

// حذف مورد
function deleteSupplier(id) {
    const supplier = suppliers.find(s => s.id === id);
    if (!supplier) return;
    
    // التحقق من وجود معاملات للمورد
    const supplierPurchases = purchases.filter(p => p.supplierId === id);
    if (supplierPurchases.length > 0) {
        showNotification('لا يمكن حذف المورد لوجود معاملات مرتبطة به', 'error');
        return;
    }
    
    if (confirm(`هل أنت متأكد من حذف المورد "${supplier.name}"؟`)) {
        suppliers = suppliers.filter(s => s.id !== id);
        saveData();
        displaySuppliersList();
        showNotification('تم حذف المورد بنجاح', 'success');
    }
}

// عرض معاملات المورد
function viewSupplierTransactions(supplierId) {
    const supplier = suppliers.find(s => s.id === supplierId);
    if (!supplier) return;
    
    const supplierPurchases = purchases.filter(p => p.supplierId === supplierId);
    
    let content = `
        <div class="supplier-transactions">
            <h4>معاملات المورد: ${supplier.name}</h4>
            <div class="supplier-summary">
                <div class="summary-item">
                    <span>الرصيد الحالي:</span>
                    <span class="${supplier.balance > 0 ? 'debt-amount' : ''}">${formatCurrency(supplier.balance)}</span>
                </div>
                <div class="summary-item">
                    <span>عدد الفواتير:</span>
                    <span>${supplierPurchases.length}</span>
                </div>
                <div class="summary-item">
                    <span>إجمالي المشتريات:</span>
                    <span>${formatCurrency(supplierPurchases.reduce((sum, p) => sum + p.totalAmount, 0))}</span>
                </div>
            </div>
    `;
    
    if (supplierPurchases.length > 0) {
        content += `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>التاريخ</th>
                        <th>رقم فاتورة المورد</th>
                        <th>الإجمالي</th>
                        <th>المدفوع</th>
                        <th>المتبقي</th>
                        <th>طريقة الدفع</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        supplierPurchases.forEach(purchase => {
            content += `
                <tr>
                    <td>${purchase.id}</td>
                    <td>${formatDateShort(purchase.date)}</td>
                    <td>${purchase.supplierInvoiceNumber || '-'}</td>
                    <td>${formatCurrency(purchase.totalAmount)}</td>
                    <td>${formatCurrency(purchase.paidAmount)}</td>
                    <td>${formatCurrency(purchase.remainingAmount)}</td>
                    <td>
                        <span class="tag ${purchase.paymentMethod === 'cash' ? 'tag-cash' : 'tag-credit'}">
                            ${purchase.paymentMethod === 'cash' ? 'نقداً' : 'على الحساب'}
                        </span>
                    </td>
                </tr>
            `;
        });
        
        content += '</tbody></table>';
    } else {
        content += '<div style="text-align: center; color: #666; padding: 20px;">لا توجد معاملات لهذا المورد</div>';
    }
    
    content += '</div>';
    
    // عرض النافذة المنبثقة
    showModal(content, `معاملات المورد: ${supplier.name}`);
}

// عرض نافذة منبثقة
function showModal(content, title) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3>${title}</h3>
                <button onclick="this.closest('.modal').remove()" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            <div class="modal-actions">
                <button onclick="this.closest('.modal').remove()" class="neumorphic-button secondary">إغلاق</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// البحث في الموردين
function searchSuppliers(searchTerm) {
    const filteredSuppliers = searchArray(suppliers, searchTerm, ['name', 'notes']);
    
    const list = document.getElementById('suppliersList');
    list.innerHTML = '';
    
    if (filteredSuppliers.length === 0) {
        list.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">لا توجد موردين تطابق البحث</div>';
        return;
    }
    
    // عرض النتائج المفلترة
    // ... (نفس كود displaySuppliersList مع استخدام filteredSuppliers)
}
