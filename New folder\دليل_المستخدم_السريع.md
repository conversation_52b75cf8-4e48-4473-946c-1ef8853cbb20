# 🚀 دليل المستخدم السريع - StoreMaster v2.0

## 🔐 تسجيل الدخول

عند تشغيل التطبيق لأول مرة:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

---

## 📋 الخطوات الأولى للبدء

### 1️⃣ إعداد الموظفين
1. انتقل إلى تبويب **"الموظفين"**
2. أضف موظفين جدد مع تحديد الصلاحيات
3. تأكد من تفعيل الموظفين النشطين

### 2️⃣ إضافة المنتجات
1. انتقل إلى تبويب **"الأصناف"**
2. أضف المنتجات مع تحديد:
   - اسم المنتج
   - سعر التكلفة
   - سعر البيع
   - الكمية الأولية
   - الحد الأدنى للتنبيه

### 3️⃣ إعداد الموردين والعملاء
1. أضف الموردين في تبويب **"الموردون"**
2. أضف العملاء في تبويب **"العملاء"**

---

## 🏭 استخدام نظام التصنيع

### إنشاء وصفة تصنيعية:
1. انتقل إلى **"التصنيع"**
2. أدخل اسم المنتج النهائي والكمية
3. أضف الخامات المطلوبة مع كمياتها
4. احفظ الوصفة

### بدء عملية تصنيع:
1. اختر الوصفة المحفوظة
2. حدد الكمية المراد إنتاجها
3. اختر الموظف المسئول
4. تأكد من توفر الخامات
5. ابدأ التصنيع - سيتم خصم الخامات وإضافة المنتج النهائي تلقائياً

---

## 🗑️ إدارة الهالك

1. انتقل إلى تبويب **"الهالك"**
2. اختر الصنف التالف
3. أدخل الكمية التالفة
4. حدد سبب الهالك (كسر، صلاحية، تلف، إلخ)
5. اختر الموظف المسئول
6. احفظ - سيتم خصم الكمية من المخزون تلقائياً

---

## ⏰ تسجيل الحضور والانصراف

### تسجيل سريع:
1. انتقل إلى **"الحضور والانصراف"**
2. اختر الموظف
3. اضغط **"تسجيل حضور سريع"** أو **"تسجيل انصراف سريع"**

### تسجيل يدوي:
1. اختر الموظف والتاريخ
2. أدخل أوقات الحضور والانصراف
3. احفظ السجل

---

## 🛒 المبيعات والمرتجعات

### إجراء عملية بيع:
1. انتقل إلى **"المبيعات"**
2. أضف المنتجات للسلة
3. اختر العميل
4. أكمل عملية البيع

### إرجاع منتجات:
1. في تقرير المبيعات، اضغط **"مرتجع"** بجانب الفاتورة
2. اختر المنتجات والكميات المراد إرجاعها
3. حدد سبب المرتجع
4. أكد العملية - سيتم إضافة الكميات للمخزون تلقائياً

---

## 💰 تقرير صافي الربح

1. انتقل إلى **"صافي الربح"**
2. اختر الفترة الزمنية (يوم، أسبوع، شهر، سنة، أو مخصصة)
3. اضغط **"إنشاء التقرير"**
4. راجع التفاصيل والنسب

### المعادلة المستخدمة:
```
صافي الربح = المبيعات - (المشتريات + تكلفة التصنيع + المصروفات + المرتبات + قيمة الهالك)
```

---

## ⚙️ الإعدادات المهمة

### إعدادات الشركة:
- اسم الشركة
- العنوان ومعلومات الاتصال
- رمز العملة

### إعدادات التنبيهات:
- تفعيل تنبيهات نقص المخزون
- تحديد الحد الأدنى للتنبيه

---

## 🔗 أزرار التواصل

### في صفحة الإعدادات:
- **متابعة صفحتنا على الفيسبوك**: للتحديثات والأخبار

### في صفحة "حول البرنامج":
- **واتساب**: للدعم الفني المباشر (01225396729)
- **فيسبوك**: لمتابعة المطور

---

## ⚠️ نصائح مهمة

### الأمان:
- غيّر كلمة المرور الافتراضية
- حدد صلاحيات الموظفين بعناية
- اعمل نسخة احتياطية من البيانات بانتظام

### الاستخدام الأمثل:
- راجع التقارير يومياً
- تابع مستويات المخزون
- سجل جميع العمليات فور حدوثها
- درب الموظفين على النظام

### حل المشاكل:
- في حالة وجود مشكلة، تواصل عبر الواتساب
- تأكد من صحة البيانات المدخلة
- راجع الصلاحيات في حالة عدم ظهور بعض الخيارات

---

## 📱 معلومات التواصل

**للدعم الفني:**
- **واتساب**: 01225396729
- **فيسبوك**: https://web.facebook.com/profile.php?id=61578888731370

**المطور:**
- Eng / Hossam Osama
- H-TECH

---

*StoreMaster v2.0 - نظام إدارة المخازن المتطور*
*تطوير: H-TECH*
