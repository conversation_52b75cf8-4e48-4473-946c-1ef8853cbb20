// ===== إدارة المشتريات =====

let currentPurchaseItems = []; // الأصناف المؤقتة للفاتورة الحالية
let editingPurchaseItemIndex = -1; // فهرس الصنف قيد التعديل

// عرض صفحة المشتريات
function showPurchasesPage() {
    const content = `
        <div class="purchases-container">
            <div class="section-header">
                <h3>إضافة فاتورة مشتريات</h3>
                <div class="operation-number">
                    رقم العملية: <span id="purchaseNumber">${generatePurchaseNumber()}</span>
                </div>
            </div>
            
            <!-- بيانات الفاتورة الأساسية -->
            <div class="purchase-header">
                <div class="form-row">
                    <div class="form-group">
                        <label>التاريخ:</label>
                        <input type="date" id="purchaseDate" class="neumorphic-input" value="${new Date().toISOString().split('T')[0]}">
                    </div>
                    <div class="form-group">
                        <label>طريقة الدفع:</label>
                        <div class="payment-method">
                            <label><input type="radio" name="purchasePayment" value="cash" checked> نقداً</label>
                            <label><input type="radio" name="purchasePayment" value="credit"> على الحساب</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- قسم بيانات الصنف -->
            <div class="item-section">
                <h4>بيانات الصنف</h4>
                <div class="form-grid">
                    <div class="form-group">
                        <label>الصنف:</label>
                        <select id="purchaseProduct" class="neumorphic-input" onchange="onProductSelect()">
                            <option value="">اختر الصنف</option>
                            ${products.map(p => `<option value="${p.id}">${p.name} (${p.code})</option>`).join('')}
                        </select>
                    </div>
                    <div class="form-group">
                        <label>الوحدة:</label>
                        <input type="text" id="purchaseUnit" class="neumorphic-input" readonly>
                    </div>
                    <div class="form-group">
                        <label>الكمية:</label>
                        <input type="number" id="purchaseQuantity" class="neumorphic-input" step="0.01" oninput="calculateItemValue()">
                    </div>
                    <div class="form-group">
                        <label>سعر شراء الوحدة:</label>
                        <input type="number" id="purchaseCostPrice" class="neumorphic-input" step="0.01" oninput="calculateItemValue()">
                    </div>
                    <div class="form-group">
                        <label>سعر بيع الوحدة:</label>
                        <input type="number" id="purchaseSellPrice" class="neumorphic-input" step="0.01">
                    </div>
                    <div class="form-group">
                        <label>القيمة:</label>
                        <input type="number" id="purchaseItemValue" class="neumorphic-input" readonly>
                    </div>
                </div>
                <div class="item-actions">
                    <button onclick="addPurchaseItem()" class="neumorphic-button primary">إضافة وتعديل</button>
                    <button onclick="deletePurchaseItem()" class="neumorphic-button danger">حذف المحدد</button>
                </div>
            </div>
            
            <!-- جدول الأصناف المضافة -->
            <div class="items-table-section">
                <h4>مشتريات الفترة</h4>
                <div id="purchaseItemsTable" class="items-table"></div>
            </div>
            
            <!-- بيانات التاجر والملخص -->
            <div class="supplier-section">
                <div class="form-row">
                    <div class="form-group">
                        <label>اسم التاجر:</label>
                        <select id="purchaseSupplier" class="neumorphic-input">
                            <option value="">اختر التاجر</option>
                            ${suppliers.map(s => `<option value="${s.id}">${s.name}</option>`).join('')}
                        </select>
                    </div>
                    <div class="form-group">
                        <label>رقم الفاتورة:</label>
                        <input type="text" id="supplierInvoiceNumber" class="neumorphic-input">
                    </div>
                    <div class="form-group">
                        <label>بيان:</label>
                        <input type="text" id="purchaseNotes" class="neumorphic-input">
                    </div>
                </div>
                
                <div class="purchase-summary">
                    <div class="summary-row">
                        <span>إجمالي الفاتورة:</span>
                        <span id="purchaseTotalAmount">0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>المبلغ المدفوع:</span>
                        <input type="number" id="purchasePaidAmount" class="neumorphic-input" step="0.01" oninput="calculatePurchaseRemaining()">
                    </div>
                    <div class="summary-row">
                        <span>المبلغ المتبقي:</span>
                        <span id="purchaseRemainingAmount">0.00</span>
                    </div>
                </div>
            </div>
            
            <!-- أزرار العمليات -->
            <div class="purchase-actions">
                <button onclick="savePurchase()" class="neumorphic-button primary">حفظ العملية</button>
                <button onclick="cancelPurchase()" class="neumorphic-button secondary">إلغاء العملية</button>
            </div>
        </div>
    `;
    
    return content;
}

// إنشاء رقم عملية جديد
function generatePurchaseNumber() {
    return 'PUR' + Date.now().toString().slice(-6);
}

// عند اختيار منتج
function onProductSelect() {
    const productId = document.getElementById('purchaseProduct').value;
    if (productId) {
        const product = products.find(p => p.id === productId);
        if (product) {
            document.getElementById('purchaseUnit').value = product.unit;
            document.getElementById('purchaseCostPrice').value = product.costPrice;
            document.getElementById('purchaseSellPrice').value = product.sellPrice;
        }
    } else {
        document.getElementById('purchaseUnit').value = '';
        document.getElementById('purchaseCostPrice').value = '';
        document.getElementById('purchaseSellPrice').value = '';
    }
    calculateItemValue();
}

// حساب قيمة الصنف
function calculateItemValue() {
    const quantity = parseFloat(document.getElementById('purchaseQuantity').value) || 0;
    const costPrice = parseFloat(document.getElementById('purchaseCostPrice').value) || 0;
    const value = quantity * costPrice;
    document.getElementById('purchaseItemValue').value = value.toFixed(2);
}

// إضافة صنف للفاتورة
function addPurchaseItem() {
    const productId = document.getElementById('purchaseProduct').value;
    const unit = document.getElementById('purchaseUnit').value;
    const quantity = parseFloat(document.getElementById('purchaseQuantity').value);
    const costPrice = parseFloat(document.getElementById('purchaseCostPrice').value);
    const sellPrice = parseFloat(document.getElementById('purchaseSellPrice').value);
    const value = parseFloat(document.getElementById('purchaseItemValue').value);
    
    // التحقق من صحة البيانات
    if (!productId || !quantity || !costPrice || !sellPrice) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    const product = products.find(p => p.id === productId);
    if (!product) {
        showNotification('المنتج غير موجود', 'error');
        return;
    }
    
    const item = {
        productId: productId,
        productName: product.name,
        productCode: product.code,
        unit: unit,
        quantity: quantity,
        costPrice: costPrice,
        sellPrice: sellPrice,
        value: value
    };
    
    if (editingPurchaseItemIndex >= 0) {
        // تعديل صنف موجود
        currentPurchaseItems[editingPurchaseItemIndex] = item;
        editingPurchaseItemIndex = -1;
    } else {
        // إضافة صنف جديد
        currentPurchaseItems.push(item);
    }
    
    updatePurchaseItemsTable();
    clearPurchaseItemForm();
    calculatePurchaseTotal();
    showNotification('تم إضافة الصنف بنجاح', 'success');
}

// تحديث جدول الأصناف
function updatePurchaseItemsTable() {
    const table = document.getElementById('purchaseItemsTable');
    
    if (currentPurchaseItems.length === 0) {
        table.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">لا توجد أصناف مضافة</div>';
        return;
    }
    
    let html = `
        <table class="data-table">
            <thead>
                <tr>
                    <th>رقم</th>
                    <th>الصنف</th>
                    <th>الوحدة</th>
                    <th>الكمية</th>
                    <th>سعر الشراء</th>
                    <th>سعر البيع</th>
                    <th>القيمة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
    `;
    
    currentPurchaseItems.forEach((item, index) => {
        html += `
            <tr onclick="selectPurchaseItem(${index}, this)" class="table-row">
                <td>${index + 1}</td>
                <td>${item.productName}</td>
                <td>${item.unit}</td>
                <td>${item.quantity}</td>
                <td>${formatCurrency(item.costPrice)}</td>
                <td>${formatCurrency(item.sellPrice)}</td>
                <td>${formatCurrency(item.value)}</td>
                <td>
                    <button onclick="editPurchaseItem(${index})" class="btn-small">تعديل</button>
                    <button onclick="removePurchaseItem(${index})" class="btn-small btn-danger">حذف</button>
                </td>
            </tr>
        `;
    });
    
    html += '</tbody></table>';
    table.innerHTML = html;
}

// تحديد صنف في الجدول
function selectPurchaseItem(index, element) {
    // إزالة التحديد السابق
    document.querySelectorAll('.table-row').forEach(row => row.classList.remove('selected'));

    // تحديد الصف الجديد
    if (element) {
        element.closest('.table-row').classList.add('selected');
    }
}

// تعديل صنف
function editPurchaseItem(index) {
    const item = currentPurchaseItems[index];
    
    document.getElementById('purchaseProduct').value = item.productId;
    document.getElementById('purchaseUnit').value = item.unit;
    document.getElementById('purchaseQuantity').value = item.quantity;
    document.getElementById('purchaseCostPrice').value = item.costPrice;
    document.getElementById('purchaseSellPrice').value = item.sellPrice;
    document.getElementById('purchaseItemValue').value = item.value;
    
    editingPurchaseItemIndex = index;
}

// حذف صنف
function removePurchaseItem(index) {
    if (confirm('هل أنت متأكد من حذف هذا الصنف؟')) {
        currentPurchaseItems.splice(index, 1);
        updatePurchaseItemsTable();
        calculatePurchaseTotal();
        showNotification('تم حذف الصنف', 'info');
    }
}

// حذف الصنف المحدد
function deletePurchaseItem() {
    const selectedRow = document.querySelector('.table-row.selected');
    if (selectedRow) {
        const index = Array.from(selectedRow.parentNode.children).indexOf(selectedRow);
        removePurchaseItem(index);
    } else {
        showNotification('يرجى تحديد صنف للحذف', 'error');
    }
}

// مسح نموذج الصنف
function clearPurchaseItemForm() {
    document.getElementById('purchaseProduct').value = '';
    document.getElementById('purchaseUnit').value = '';
    document.getElementById('purchaseQuantity').value = '';
    document.getElementById('purchaseCostPrice').value = '';
    document.getElementById('purchaseSellPrice').value = '';
    document.getElementById('purchaseItemValue').value = '';
}

// حساب إجمالي الفاتورة
function calculatePurchaseTotal() {
    const total = currentPurchaseItems.reduce((sum, item) => sum + item.value, 0);
    document.getElementById('purchaseTotalAmount').textContent = formatCurrency(total);
    calculatePurchaseRemaining();
}

// حساب المبلغ المتبقي
function calculatePurchaseRemaining() {
    const total = currentPurchaseItems.reduce((sum, item) => sum + item.value, 0);
    const paid = parseFloat(document.getElementById('purchasePaidAmount').value) || 0;
    const remaining = total - paid;
    document.getElementById('purchaseRemainingAmount').textContent = formatCurrency(remaining);
}

// حفظ فاتورة المشتريات
function savePurchase() {
    // التحقق من وجود أصناف
    if (currentPurchaseItems.length === 0) {
        showNotification('يرجى إضافة أصناف للفاتورة', 'error');
        return;
    }

    // التحقق من بيانات التاجر
    const supplierId = document.getElementById('purchaseSupplier').value;
    if (!supplierId) {
        showNotification('يرجى اختيار التاجر', 'error');
        return;
    }

    const date = document.getElementById('purchaseDate').value;
    const paymentMethod = document.querySelector('input[name="purchasePayment"]:checked').value;
    const supplierInvoiceNumber = document.getElementById('supplierInvoiceNumber').value;
    const notes = document.getElementById('purchaseNotes').value;
    const totalAmount = currentPurchaseItems.reduce((sum, item) => sum + item.value, 0);
    const paidAmount = parseFloat(document.getElementById('purchasePaidAmount').value) || 0;
    const remainingAmount = totalAmount - paidAmount;

    try {
        // إنشاء فاتورة المشتريات
        const purchase = createPurchase(
            date,
            paymentMethod,
            supplierId,
            supplierInvoiceNumber,
            totalAmount,
            paidAmount,
            remainingAmount
        );

        purchases.push(purchase);

        // إضافة أصناف المشتريات
        currentPurchaseItems.forEach(item => {
            const purchaseItem = createPurchaseItem(
                purchase.id,
                item.productId,
                item.unit,
                item.quantity,
                item.costPrice,
                item.sellPrice,
                item.value
            );
            purchaseItems.push(purchaseItem);

            // تحديث المخزون وأسعار المنتج
            updateProductStock(item.productId, item.quantity, 'add');
            updateProductPrices(item.productId, item.costPrice, item.sellPrice);
        });

        // تحديث رصيد المورد
        if (paymentMethod === 'credit') {
            updateSupplierBalance(supplierId, remainingAmount, 'add');
        }

        // حفظ البيانات
        saveData();
        if (typeof onDataChange === 'function') { try { onDataChange(); } catch(e) { console.error(e); } }

        // إنشاء سند الاستلام
        generatePurchaseReceipt(purchase);

        // مسح النموذج
        resetPurchaseForm();

        showNotification('تم حفظ فاتورة المشتريات بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في حفظ فاتورة المشتريات:', error);
        showNotification('حدث خطأ أثناء حفظ الفاتورة', 'error');
    }
}

// إلغاء فاتورة المشتريات
function cancelPurchase() {
    if (currentPurchaseItems.length > 0) {
        if (confirm('هل أنت متأكد من إلغاء العملية؟ سيتم فقدان جميع البيانات المدخلة.')) {
            resetPurchaseForm();
            showNotification('تم إلغاء العملية', 'info');
        }
    } else {
        resetPurchaseForm();
    }
}

// إعادة تعيين نموذج المشتريات
function resetPurchaseForm() {
    currentPurchaseItems = [];
    editingPurchaseItemIndex = -1;

    document.getElementById('purchaseDate').value = new Date().toISOString().split('T')[0];
    document.querySelector('input[name="purchasePayment"][value="cash"]').checked = true;
    document.getElementById('purchaseSupplier').value = '';
    document.getElementById('supplierInvoiceNumber').value = '';
    document.getElementById('purchaseNotes').value = '';
    document.getElementById('purchasePaidAmount').value = '';

    clearPurchaseItemForm();
    updatePurchaseItemsTable();
    calculatePurchaseTotal();

    // تحديث رقم العملية
    document.getElementById('purchaseNumber').textContent = generatePurchaseNumber();
}

// إنشاء سند استلام المشتريات
function generatePurchaseReceipt(purchase) {
    const supplier = suppliers.find(s => s.id === purchase.supplierId);
    const items = getPurchaseItems(purchase.id);

    let companyInfo = '';
    if (settings.showCompanyInInvoice) {
        companyInfo = `
            <div class="company-info" style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 15px;">
                <h2 style="margin: 0; color: #333;">${settings.companyName || 'اسم الشركة'}</h2>
                <div style="margin: 5px 0;">${settings.activity || 'النشاط'}</div>
                ${settings.commercialRegister ? `<div style="margin: 5px 0;">س.ت: ${settings.commercialRegister}</div>` : ''}
                ${settings.phone ? `<div style="margin: 5px 0;">هاتف: ${settings.phone}</div>` : ''}
                ${settings.address ? `<div style="margin: 5px 0;">العنوان: ${settings.address}</div>` : ''}
                ${settings.email ? `<div style="margin: 5px 0;">البريد: ${settings.email}</div>` : ''}
            </div>
        `;
    }

    const receiptContent = `
        ${companyInfo}
        <div class="receipt-header">
            <h2>سند استلام مشتريات</h2>
            <div class="receipt-info">
                <div>رقم العملية: ${purchase.id}</div>
                <div>التاريخ: ${formatDateShort(purchase.date)}</div>
                <div>التاجر: ${supplier ? supplier.name : 'غير محدد'}</div>
            </div>
        </div>

        <div class="receipt-body">
            <p>استلمت أنا / المقر أدناه من السيد/ة <strong>${supplier ? supplier.name : '___________'}</strong></p>
            <p>الأصناف التالية:</p>

            <table class="receipt-table">
                <thead>
                    <tr>
                        <th>الصنف</th>
                        <th>الوحدة</th>
                        <th>الكمية</th>
                        <th>سعر الوحدة</th>
                        <th>القيمة</th>
                    </tr>
                </thead>
                <tbody>
                    ${items.map(item => {
                        const product = products.find(p => p.id === item.productId);
                        return `
                            <tr>
                                <td>${product ? product.name : 'غير محدد'}</td>
                                <td>${item.unit}</td>
                                <td>${item.quantity}</td>
                                <td>${formatCurrency(item.costPrice)}</td>
                                <td>${formatCurrency(item.totalValue)}</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>

            <div class="receipt-summary">
                <div class="summary-row">
                    <span>الإجمالي:</span>
                    <span>${formatCurrency(purchase.totalAmount)}</span>
                </div>
                <div class="summary-row">
                    <span>المدفوع:</span>
                    <span>${formatCurrency(purchase.paidAmount)}</span>
                </div>
                <div class="summary-row">
                    <span>المتبقي:</span>
                    <span>${formatCurrency(purchase.remainingAmount)}</span>
                </div>
            </div>

            <div class="receipt-footer">
                <div class="signature-section">
                    <div>المستلم: _______________</div>
                    <div>التوقيع: _______________</div>
                    <div>التاريخ: ${formatDateShort(new Date())}</div>
                </div>
            </div>
        </div>
    `;

    // عرض سند الاستلام في نافذة منبثقة
    showReceiptModal(receiptContent, 'سند استلام مشتريات');
}

// عرض نافذة سند الاستلام
function showReceiptModal(content, title) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content receipt-modal">
            <div class="modal-header">
                <h3>${title}</h3>
                <button onclick="this.closest('.modal').remove()" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            <div class="modal-actions">
                <button onclick="printReceipt()" class="neumorphic-button primary">طباعة</button>
                <button onclick="this.closest('.modal').remove()" class="neumorphic-button secondary">إغلاق</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// طباعة السند
function printReceipt() {
    const receiptContent = document.querySelector('.receipt-modal .modal-body').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>سند استلام</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    .receipt-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    .receipt-table th, .receipt-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                    .receipt-table th { background-color: #f5f5f5; }
                    .receipt-summary { margin: 20px 0; }
                    .summary-row { display: flex; justify-content: space-between; margin: 5px 0; }
                    .signature-section { margin-top: 40px; }
                    .signature-section div { margin: 10px 0; }
                </style>
            </head>
            <body>
                ${receiptContent}
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}
