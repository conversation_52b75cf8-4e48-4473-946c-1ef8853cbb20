@echo off
echo بناء نظام إدارة نقاط البيع كملف exe...
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت على النظام
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

REM تثبيت التبعيات إذا لم تكن موجودة
if not exist "node_modules" (
    echo تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo خطأ في تثبيت التبعيات
        pause
        exit /b 1
    )
)

REM بناء التطبيق لنظام Windows
echo بناء التطبيق لنظام Windows...
npm run build-win

if %errorlevel% equ 0 (
    echo.
    echo ✅ تم بناء التطبيق بنجاح!
    echo ملف exe موجود في مجلد: dist
    echo.
    
    REM فتح مجلد النتائج
    if exist "dist" (
        echo فتح مجلد النتائج...
        explorer dist
    )
) else (
    echo.
    echo ❌ فشل في بناء التطبيق
    echo يرجى التحقق من الأخطاء أعلاه
)

echo.
pause
