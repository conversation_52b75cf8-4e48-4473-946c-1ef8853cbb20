<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StoreMaster - H-TECH</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <h1>StoreMaster</h1>
            <p class="login-subtitle">نظام إدارة المخازن المتطور</p>
            <div class="login-form">
                <input type="text" id="usernameInput" placeholder="اسم المستخدم" class="neumorphic-input">
                <input type="password" id="passwordInput" placeholder="كلمة المرور" class="neumorphic-input">
                <button onclick="login()" class="neumorphic-button">دخول</button>
            </div>
            <div id="loginError" class="error-message"></div>
            <div class="login-footer">
                <p>تطوير: <strong>H-TECH</strong></p>
            </div>
        </div>
    </div>

    <!-- الواجهة الرئيسية -->
    <div id="mainApp" class="main-app hidden">
        <!-- قائمة جانبية منسدلة -->
        <aside id="sidebar">
            <div class="sidebar-header">
                <div class="nav-brand">StoreMaster</div>
                <div class="sidebar-actions">
                    <button id="themeToggle" onclick="toggleTheme()" class="theme-toggle">🌙</button>
                    <button class="logout-btn" onclick="logout()">⎋</button>
                </div>
            </div>
            <div class="menu">
                <div class="menu-item" onclick="showTab('dashboard')">
                    <span>لوحة المعلومات</span>
                </div>
                <div class="menu-item has-sub">
                    <span>المبيعات</span>
                    <div class="submenu">
                        <button onclick="showTab('sales')">نقطة البيع</button>
                        <button onclick="showReturnsSection()">مرتجعات</button>
                    </div>
                </div>
                <div class="menu-item" onclick="showTab('purchases')"><span>المشتريات</span></div>
                <div class="menu-item" onclick="showTab('products')"><span>الأصناف</span></div>
                <div class="menu-item has-sub">
                    <span>الشحن</span>
                    <div class="submenu">
                        <button onclick="showTab('seaShipping')">الشحن البحري</button>
                        <button onclick="showTab('domesticShipping')">الشحن الداخلي</button>
                        <button onclick="showTab('customs')">التخليص الجمركي</button>
                    </div>
                </div>
                <div class="menu-item has-sub">
                    <span>المالية</span>
                    <div class="submenu">
                        <button onclick="showTab('treasury')">الخزينة</button>
                        <button onclick="showTab('banks')">البنوك</button>
                        <button onclick="showTab('expenses')">المصروفات</button>
                        <button onclick="showTab('profit')">صافي الربح</button>
                    </div>
                </div>
                <div class="menu-item has-sub">
                    <span>العلاقات</span>
                    <div class="submenu">
                        <button onclick="showTab('suppliers')">الموردون</button>
                        <button onclick="showTab('customers')">العملاء</button>
                        <button onclick="showTab('debts')">الديون</button>
                    </div>
                </div>
                <div class="menu-item has-sub">
                    <span>الموارد البشرية</span>
                    <div class="submenu">
                        <button onclick="showTab('employees')">الموظفون</button>
                        <button onclick="showTab('attendance')">الحضور والانصراف</button>
                    </div>
                </div>
                <div class="menu-item" onclick="showTab('wastage')"><span>الهالك</span></div>
                <div class="menu-item" onclick="showTab('reports')"><span>التقارير</span></div>
                <div class="menu-item" onclick="showTab('settings')"><span>الإعدادات</span></div>
            </div>
        </aside>
        
        <!-- إخفاء الشريط العلوي القديم -->
        <nav class="navbar" style="display:none;"></nav>

        <!-- علامة تبويب لوحة المعلومات -->
        <div id="dashboardTab" class="tab-content active">
            <div class="dashboard-container">
                <h3>لوحة المعلومات - التقارير الرئيسية</h3>

                <!-- مؤشرات سريعة -->
                <div id="quickIndicators" class="quick-indicators">
                    <div class="indicator-card">
                        <div class="indicator-icon">📦</div>
                        <div class="indicator-info">
                            <div class="indicator-label">إجمالي المنتجات</div>
                            <div class="indicator-value" id="totalProductsIndicator">0</div>
                        </div>
                    </div>
                    <div class="indicator-card warning">
                        <div class="indicator-icon">⚠️</div>
                        <div class="indicator-info">
                            <div class="indicator-label">منخفضة المخزون</div>
                            <div class="indicator-value" id="lowStockIndicator">0</div>
                        </div>
                    </div>
                    <div class="indicator-card">
                        <div class="indicator-icon">👥</div>
                        <div class="indicator-info">
                            <div class="indicator-label">العملاء</div>
                            <div class="indicator-value" id="customersIndicator">0</div>
                        </div>
                    </div>
                    <div class="indicator-card">
                        <div class="indicator-icon">🏪</div>
                        <div class="indicator-info">
                            <div class="indicator-label">الموردين</div>
                            <div class="indicator-value" id="suppliersIndicator">0</div>
                        </div>
                    </div>
                </div>

                <!-- قسم المشتريات -->
                <div class="dashboard-section">
                    <h4>المشتريات</h4>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-label">مشتريات اليوم</div>
                            <div class="stat-value" id="purchasesToday">0.00</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">مشتريات الأسبوع</div>
                            <div class="stat-value" id="purchasesWeek">0.00</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">مشتريات الشهر</div>
                            <div class="stat-value" id="purchasesMonth">0.00</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">إجمالي المشتريات</div>
                            <div class="stat-value" id="purchasesTotal">0.00</div>
                        </div>
                    </div>
                </div>

                <!-- قسم المبيعات -->
                <div class="dashboard-section">
                    <h4>المبيعات</h4>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-label">مبيعات اليوم</div>
                            <div class="stat-value" id="salesToday">0.00</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">مبيعات الأسبوع</div>
                            <div class="stat-value" id="salesWeek">0.00</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">مبيعات الشهر</div>
                            <div class="stat-value" id="salesMonth">0.00</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">إجمالي المبيعات</div>
                            <div class="stat-value" id="salesTotal">0.00</div>
                        </div>
                    </div>
                </div>

                <!-- قسم المصروفات -->
                <div class="dashboard-section">
                    <h4>المصروفات</h4>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-label">مصروفات اليوم</div>
                            <div class="stat-value" id="expensesToday">0.00</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">مصروفات الأسبوع</div>
                            <div class="stat-value" id="expensesWeek">0.00</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">مصروفات الشهر</div>
                            <div class="stat-value" id="expensesMonth">0.00</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">إجمالي المصروفات</div>
                            <div class="stat-value" id="expensesTotal">0.00</div>
                        </div>
                    </div>
                </div>

                <!-- قسم حركة الخزنة -->
                <div class="dashboard-section">
                    <h4>حركة الخزنة - المقبوضات</h4>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-label">مقبوضات اليوم</div>
                            <div class="stat-value" id="receiptsToday">0.00</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">مقبوضات الأسبوع</div>
                            <div class="stat-value" id="receiptsWeek">0.00</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">مقبوضات الشهر</div>
                            <div class="stat-value" id="receiptsMonth">0.00</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">مقبوضات العام</div>
                            <div class="stat-value" id="receiptsYear">0.00</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">إجمالي المقبوضات</div>
                            <div class="stat-value" id="receiptsTotal">0.00</div>
                        </div>
                    </div>
                </div>

                <!-- قوائم Top 10 -->
                <div class="dashboard-lists">
                    <div class="top-list">
                        <h4>أكثر 10 أصناف مبيعاً من حيث الكمية</h4>
                        <div id="topProductsList" class="list-container"></div>
                    </div>
                    <div class="top-list">
                        <h4>أكثر 10 مديونين عليهم مبالغ</h4>
                        <div id="topDebtorsList" class="list-container"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- علامة تبويب المشتريات -->
        <div id="purchasesTab" class="tab-content">
            <div id="purchasesContent"></div>
        </div>

        <!-- علامة تبويب المبيعات -->
        <div id="salesTab" class="tab-content">
            <div class="sales-container">
                <!-- قائمة المنتجات -->
                <div class="products-grid">
                    <h3>المنتجات المتاحة</h3>
                    <div class="low-stock-alert" id="lowStockAlert"></div>
                    <div id="productsGrid" class="grid"></div>
                </div>

                <!-- سلة المشتريات -->
                <div class="cart-section">
                    <h3>سلة المشتريات</h3>
                    <div id="cartItems" class="cart-items"></div>
                    <div class="cart-total">
                        <div class="total-row">
                            <span>المجموع الفرعي:</span>
                            <span id="subtotal">0.00</span>
                        </div>
                        <div class="total-row">
                            <span id="taxLabel">الضريبة:</span>
                            <span id="tax">0.00</span>
                        </div>
                        <div class="total-row total-final">
                            <span>المجموع النهائي:</span>
                            <span id="total">0.00</span>
                        </div>
                    </div>

                    <!-- طريقة الدفع -->
                    <div class="payment-method-section">
                        <h4>طريقة الدفع:</h4>
                        <div class="payment-options">
                            <label class="payment-option">
                                <input type="radio" name="salePayment" value="cash" checked onchange="toggleCustomerSelection()">
                                <span class="tag tag-cash">نقداً</span>
                            </label>
                            <label class="payment-option">
                                <input type="radio" name="salePayment" value="credit" onchange="toggleCustomerSelection()">
                                <span class="tag tag-credit">على الحساب</span>
                            </label>
                        </div>
                    </div>

                    <!-- اختيار العميل للمبيعات الآجلة -->
                    <div id="customerSelection" class="customer-selection hidden">
                        <h4>اختيار العميل:</h4>
                        <select id="saleCustomer" class="neumorphic-input">
                            <option value="">اختر العميل</option>
                        </select>
                    </div>

                    <div class="cart-actions">
                        <button onclick="clearCart()" class="neumorphic-button secondary">مسح السلة</button>
                        <button onclick="showReturnsSection()" class="neumorphic-button warning">🔁 المرتجعات</button>
                        <button onclick="completeSale()" class="neumorphic-button primary">إنهاء عملية البيع</button>
                    </div>
                </div>
            </div>

            <!-- قسم المرتجعات -->
            <div id="returnsSection" class="returns-section hidden">
                <h3>🔁 مرتجعات المبيعات <button onclick="cancelReturn()" class="neumorphic-button secondary" style="float: left;">غلق</button></h3>

                <!-- البحث عن الفاتورة -->
                <div class="return-search">
                    <h4>البحث عن الفاتورة:</h4>
                    <div class="form-grid">
                        <input type="text" id="returnInvoiceNumber" placeholder="رقم الفاتورة" class="neumorphic-input">
                        <input type="date" id="returnInvoiceDate" class="neumorphic-input">
                        <button onclick="searchInvoice()" class="neumorphic-button primary">بحث</button>
                    </div>
                </div>

                <!-- تفاصيل الفاتورة المختارة -->
                <div id="invoiceDetails" class="invoice-details hidden">
                    <h4>تفاصيل الفاتورة:</h4>
                    <div id="invoiceInfo" class="invoice-info"></div>
                    <div id="invoiceItems" class="invoice-items"></div>
                </div>

                <!-- عناصر المرتجع -->
                <div id="returnItems" class="return-items hidden">
                    <h4>عناصر المرتجع:</h4>
                    <div id="returnItemsList" class="return-items-list"></div>

                    <div class="return-summary">
                        <div class="return-total">
                            <strong>إجمالي المرتجع: <span id="returnTotal">0.00</span> ج.م</strong>
                        </div>
                    </div>

                    <div class="return-actions">
                        <button onclick="cancelReturn()" class="neumorphic-button secondary">إلغاء</button>
                        <button onclick="processReturn()" class="neumorphic-button danger">تنفيذ المرتجع</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- علامة تبويب الأصناف -->
        <div id="productsTab" class="tab-content">
            <div class="products-management">
                <div class="section-header">
                    <h3>إدارة الأصناف</h3>
                    <button onclick="showAddProductForm()" class="neumorphic-button">إضافة صنف جديد</button>
                </div>

                <!-- نموذج إضافة صنف -->
                <div id="addProductForm" class="form-container hidden">
                    <h4>إضافة صنف جديد</h4>
                    <div class="form-grid">
                        <input type="text" id="productName" placeholder="اسم الصنف" class="neumorphic-input">
                        <input type="text" id="productCode" placeholder="كود الصنف" class="neumorphic-input">
                        <input type="text" id="productUnit" placeholder="الوحدة (كيلو، قطعة)" class="neumorphic-input">
                        <input type="number" id="productStockQuantity" placeholder="رصيد المخزون" class="neumorphic-input" step="0.01">
                        <input type="number" id="productCostPrice" placeholder="سعر الشراء" class="neumorphic-input" step="0.01">
                        <input type="number" id="productSellPrice" placeholder="سعر البيع" class="neumorphic-input" step="0.01">
                    </div>
                    <div class="form-actions">
                        <button onclick="hideAddProductForm()" class="neumorphic-button secondary">إلغاء</button>
                        <button onclick="addProduct()" class="neumorphic-button primary">حفظ العملية</button>
                    </div>
                </div>

                <!-- قائمة الأصناف -->
                <div id="productsList" class="products-list"></div>

                <!-- Excel لإدارة المخزون -->
                <div class="manufacturing-section" style="margin-top: 25px;">
                    <h4>استيراد/تصدير Excel - المخزون</h4>
                    <div class="excel-import-section">
                        <div class="import-controls">
                            <input type="file" id="excelFileInput" accept=".xlsx,.xls,.csv" class="file-input">
                            <button onclick="importExcelFile()" class="neumorphic-button">استيراد ملف Excel</button>
                            <button onclick="exportToExcel()" class="neumorphic-button secondary">تصدير إلى Excel</button>
                            <button onclick="clearExcelData()" class="neumorphic-button danger">مسح البيانات</button>
                        </div>

                        <div id="excelDataContainer" class="excel-data-container">
                            <div id="excelSheetTabs" class="excel-sheet-tabs"></div>
                            <div id="excelSheetInfo" class="excel-sheet-info">
                                <span id="currentSheetName">لا توجد أوراق</span>
                                <span id="sheetDimensions"></span>
                            </div>
                            <div class="excel-table-wrapper" id="excelTableWrapper">
                                <table id="excelTable" class="excel-table">
                                    <thead id="excelTableHead"></thead>
                                    <tbody id="excelTableBody"></tbody>
                                </table>
                            </div>
                            <div class="excel-actions">
                                <button onclick="addExcelRow()" class="neumorphic-button">إضافة صف</button>
                                <button onclick="addExcelColumn()" class="neumorphic-button">إضافة عمود</button>
                                <button onclick="saveExcelChanges()" class="neumorphic-button primary">حفظ التغييرات</button>
                                <button onclick="exportCurrentSheet()" class="neumorphic-button secondary">تصدير الورقة الحالية</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- علامة تبويب الموردون -->
        <div id="suppliersTab" class="tab-content">
            <div class="suppliers-management">
                <div class="section-header">
                    <h3>إدارة الموردين</h3>
                    <button onclick="showAddSupplierForm()" class="neumorphic-button">إضافة مورد جديد</button>
                </div>

                <!-- نموذج إضافة مورد -->
                <div id="addSupplierForm" class="form-container hidden">
                    <h4>إضافة مورد جديد</h4>
                    <div class="form-grid">
                        <input type="text" id="supplierName" placeholder="اسم التاجر" class="neumorphic-input">
                        <textarea id="supplierNotes" placeholder="بيان (ملاحظات)" class="neumorphic-input"></textarea>
                    </div>
                    <div class="form-actions">
                        <button onclick="hideAddSupplierForm()" class="neumorphic-button secondary">إلغاء</button>
                        <button onclick="addSupplier()" class="neumorphic-button primary">إضافة المورد</button>
                    </div>
                </div>

                <!-- قائمة الموردين -->
                <div id="suppliersList" class="suppliers-list"></div>
            </div>
        </div>

        <!-- علامة تبويب العملاء -->
        <div id="customersTab" class="tab-content">
            <div class="customers-management">
                <div class="section-header">
                    <h3>إدارة العملاء</h3>
                    <button onclick="showAddCustomerForm()" class="neumorphic-button">إضافة عميل جديد</button>
                </div>
                
                <!-- نموذج إضافة عميل -->
                <div id="addCustomerForm" class="form-container hidden">
                    <h4>إضافة عميل جديد</h4>
                    <div class="form-grid">
                        <input type="text" id="customerName" placeholder="اسم العميل" class="neumorphic-input">
                        <input type="tel" id="customerPhone" placeholder="رقم الهاتف" class="neumorphic-input">
                        <input type="email" id="customerEmail" placeholder="البريد الإلكتروني" class="neumorphic-input">
                        <textarea id="customerAddress" placeholder="العنوان" class="neumorphic-input"></textarea>
                    </div>
                    <div class="form-actions">
                        <button onclick="hideAddCustomerForm()" class="neumorphic-button secondary">إلغاء</button>
                        <button onclick="addCustomer()" class="neumorphic-button primary">إضافة العميل</button>
                    </div>
                </div>

                <!-- قائمة العملاء -->
                <div id="customersList" class="customers-list"></div>
            </div>
        </div>

        <!-- علامة تبويب الديون -->
        <div id="debtsTab" class="tab-content">
            <div class="debts-management">
                <div class="section-header">
                    <h3>إدارة الديون والمدفوعات</h3>
                    <button onclick="refreshDebtsData()" class="neumorphic-button">تحديث البيانات</button>
                </div>

                <!-- ملخص الديون -->
                <div class="debts-summary">
                    <div class="summary-card debt-card">
                        <div class="summary-icon">💰</div>
                        <div class="summary-info">
                            <div class="summary-label">إجمالي الديون</div>
                            <div class="summary-value" id="totalDebts">0.00</div>
                        </div>
                    </div>
                    <div class="summary-card customers-card">
                        <div class="summary-icon">👥</div>
                        <div class="summary-info">
                            <div class="summary-label">العملاء المدينين</div>
                            <div class="summary-value" id="debtorsCount">0</div>
                        </div>
                    </div>
                    <div class="summary-card payments-card">
                        <div class="summary-icon">📊</div>
                        <div class="summary-info">
                            <div class="summary-label">المدفوعات اليوم</div>
                            <div class="summary-value" id="todayPayments">0.00</div>
                        </div>
                    </div>
                </div>

                <!-- فلاتر وأدوات -->
                <div class="debts-filters">
                    <div class="filter-group">
                        <label>ترتيب حسب:</label>
                        <select id="debtsSortBy" class="neumorphic-input" onchange="sortDebtsList()">
                            <option value="balance">قيمة الدين</option>
                            <option value="name">اسم العميل</option>
                            <option value="lastTransaction">آخر معاملة</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>عرض:</label>
                        <select id="debtsFilter" class="neumorphic-input" onchange="filterDebtsList()">
                            <option value="all">جميع العملاء</option>
                            <option value="debtors">المدينين فقط</option>
                            <option value="creditors">الدائنين فقط</option>
                        </select>
                    </div>
                </div>

                <!-- قائمة الديون -->
                <div class="debts-list">
                    <h4>قائمة أرصدة العملاء</h4>
                    <div id="debtsList"></div>
                </div>

                <!-- المدفوعات الأخيرة -->
                <div class="recent-payments">
                    <h4>المدفوعات الأخيرة (آخر 10)</h4>
                    <div id="recentPaymentsList"></div>
                </div>
            </div>
        </div>

        <!-- علامة تبويب المصروفات -->
        <div id="expensesTab" class="tab-content">
            <div class="expenses-management">
                <div class="section-header">
                    <h3>إدارة المصروفات</h3>
                    <button onclick="showAddExpenseForm()" class="neumorphic-button">إضافة مصروف جديد</button>
                </div>
                
                <!-- نموذج إضافة مصروف -->
                <div id="addExpenseForm" class="form-container hidden">
                    <h4>إضافة مصروف جديد</h4>
                    <div class="form-grid">
                        <input type="text" id="expenseDescription" placeholder="وصف المصروف" class="neumorphic-input">
                        <input type="number" id="expenseAmount" placeholder="المبلغ" class="neumorphic-input" step="0.01">
                        <input type="date" id="expenseDate" class="neumorphic-input">
                        <select id="expenseCategory" class="neumorphic-input">
                            <option value="">اختر الفئة</option>
                            <option value="rent">إيجار</option>
                            <option value="utilities">مرافق</option>
                            <option value="supplies">مستلزمات</option>
                            <option value="maintenance">صيانة</option>
                            <option value="other">أخرى</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button onclick="hideAddExpenseForm()" class="neumorphic-button secondary">إلغاء</button>
                        <button onclick="addExpense()" class="neumorphic-button primary">إضافة المصروف</button>
                    </div>
                </div>

                <!-- قائمة المصروفات -->
                <div id="expensesList" class="expenses-list"></div>
            </div>
        </div>

        <!-- علامة تبويب التقارير -->
        <div id="reportsTab" class="tab-content">
            <div class="reports-container">
                <h3>التقارير المالية</h3>
                <div class="report-filters">
                    <select id="reportPeriod" class="neumorphic-input" onchange="generateReport()">
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month">هذا الشهر</option>
                        <option value="custom">فترة مخصصة</option>
                    </select>
                    <div id="customDateRange" class="hidden">
                        <input type="date" id="startDate" class="neumorphic-input">
                        <input type="date" id="endDate" class="neumorphic-input">
                        <button onclick="generateReport()" class="neumorphic-button">إنشاء التقرير</button>
                    </div>
                </div>
                <div id="reportContent" class="report-content"></div>
            </div>
        </div>

        

        <!-- علامة تبويب الشحن البحري -->
        <div id="seaShippingTab" class="tab-content">
            <div class="section-header">
                <h3>إدارة الشحن البحري</h3>
                <div class="actions">
                    <button onclick="seaShipping.showCreateForm()" class="neumorphic-button">إضافة شحنة بحرية</button>
                    <button onclick="seaShipping.showRunningReport()" class="neumorphic-button secondary">تقرير الشحنات الجارية</button>
                    <button onclick="seaShipping.showCompletedReport()" class="neumorphic-button secondary">تقرير الشحنات المكتملة</button>
                    <button onclick="seaShipping.showCostsReport()" class="neumorphic-button secondary">تقرير تكاليف الشحن</button>
                </div>
            </div>
            <div id="seaShippingContent"></div>
        </div>

        <!-- علامة تبويب الشحن الداخلي -->
        <div id="domesticShippingTab" class="tab-content">
            <div class="section-header">
                <h3>إدارة الشحن الداخلي</h3>
                <div class="actions">
                    <button onclick="domesticShipping.showCreateForm()" class="neumorphic-button">إضافة نقل داخلي</button>
                    <button onclick="domesticShipping.showCostsReport()" class="neumorphic-button secondary">تقرير تكاليف النقل</button>
                    <button onclick="domesticShipping.showClientsReport()" class="neumorphic-button secondary">تقرير الشحنات لكل عميل</button>
                    <button onclick="domesticShipping.showEtaVsActual()" class="neumorphic-button secondary">تتبع زمن التسليم</button>
                </div>
            </div>
            <div id="domesticShippingContent"></div>
        </div>

        <!-- علامة تبويب التخليص الجمركي -->
        <div id="customsTab" class="tab-content">
            <div class="section-header">
                <h3>إدارة التخليص الجمركي</h3>
                <div class="actions">
                    <button onclick="customs.showCreateForm()" class="neumorphic-button">إضافة بيان جمركي</button>
                    <button onclick="customs.showCostsReport()" class="neumorphic-button secondary">تقرير تكاليف التخليص</button>
                    <button onclick="customs.showDurationReport()" class="neumorphic-button secondary">تقرير مدة الإفراج</button>
                    <button onclick="customs.showHeldRejectedReport()" class="neumorphic-button secondary">الشحنات المحتجزة/المرفوضة</button>
                </div>
            </div>
            <div id="customsContent"></div>
        </div>

        <!-- علامة تبويب الخزينة -->
        <div id="treasuryTab" class="tab-content">
            <div class="section-header">
                <h3>الخزينة</h3>
                <div class="actions">
                    <button onclick="treasury.showAddCashbox()" class="neumorphic-button">إضافة خزينة</button>
                    <button onclick="treasury.showCashIn()" class="neumorphic-button secondary">تسجيل إيراد</button>
                    <button onclick="treasury.showCashOut()" class="neumorphic-button secondary">تسجيل مصروف</button>
                    <button onclick="treasury.showReports()" class="neumorphic-button secondary">تقارير الخزينة</button>
                </div>
            </div>
            <div id="treasuryContent"></div>
        </div>

        <!-- علامة تبويب البنوك -->
        <div id="banksTab" class="tab-content">
            <div class="section-header">
                <h3>الحسابات البنكية</h3>
                <div class="actions">
                    <button onclick="banks.showAddAccount()" class="neumorphic-button">إضافة حساب بنكي</button>
                    <button onclick="banks.showDeposit()" class="neumorphic-button secondary">إيداع</button>
                    <button onclick="banks.showWithdraw()" class="neumorphic-button secondary">سحب</button>
                    <button onclick="banks.showInternalTransfer()" class="neumorphic-button secondary">تحويل داخلي</button>
                    <button onclick="banks.showWireTransfer()" class="neumorphic-button secondary">تحويل خارجي</button>
                    <button onclick="banks.showReports()" class="neumorphic-button secondary">تقارير البنك</button>
                </div>
            </div>
            <div id="banksContent"></div>
        </div>

        <!-- علامة تبويب الهالك -->
        <div id="wastageTab" class="tab-content">
            <div class="wastage-container">
                <h3>إدارة الهالك والأصناف التالفة</h3>

                <!-- قسم تسجيل هالك جديد -->
                <div class="wastage-section">
                    <h4>تسجيل صنف تالف</h4>
                    <div class="wastage-form">
                        <div class="form-grid">
                            <select id="wastageProduct" class="neumorphic-input">
                                <option value="">اختر الصنف</option>
                            </select>
                            <input type="number" id="wastageQuantity" placeholder="الكمية التالفة" class="neumorphic-input" min="0.01" step="0.01">
                            <select id="wastageReason" class="neumorphic-input">
                                <option value="">سبب الهالك</option>
                                <option value="كسر">كسر</option>
                                <option value="صلاحية">انتهاء الصلاحية</option>
                                <option value="تلف">تلف</option>
                                <option value="فقدان">فقدان</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                            <input type="date" id="wastageDate" class="neumorphic-input">
                            <select id="wastageEmployee" class="neumorphic-input">
                                <option value="">الموظف المسئول</option>
                            </select>
                            <textarea id="wastageNotes" placeholder="ملاحظات إضافية" class="neumorphic-input" rows="2"></textarea>
                        </div>

                        <div class="form-actions">
                            <button onclick="saveWastage()" class="neumorphic-button">حفظ الهالك</button>
                            <button onclick="clearWastageForm()" class="neumorphic-button secondary">مسح النموذج</button>
                        </div>
                    </div>
                </div>

                <!-- قسم سجل الهالك -->
                <div class="wastage-section">
                    <h4>سجل الهالك</h4>
                    <div class="filter-section">
                        <div class="form-grid">
                            <input type="date" id="wastageFilterStart" class="neumorphic-input" placeholder="من تاريخ">
                            <input type="date" id="wastageFilterEnd" class="neumorphic-input" placeholder="إلى تاريخ">
                            <select id="wastageFilterReason" class="neumorphic-input">
                                <option value="">جميع الأسباب</option>
                                <option value="كسر">كسر</option>
                                <option value="صلاحية">انتهاء الصلاحية</option>
                                <option value="تلف">تلف</option>
                                <option value="فقدان">فقدان</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                            <button onclick="filterWastage()" class="neumorphic-button">فلترة</button>
                        </div>
                    </div>
                    <div id="wastageList" class="wastage-list"></div>
                </div>

                <!-- قسم تقرير الهالك -->
                <div class="wastage-section">
                    <h4>تقرير الهالك</h4>
                    <div id="wastageReport" class="wastage-report"></div>
                </div>
            </div>
        </div>

        <!-- علامة تبويب الموظفين -->
        <div id="employeesTab" class="tab-content">
            <div class="employees-container">
                <h3>إدارة الموظفين</h3>

                <!-- قسم إضافة موظف جديد -->
                <div class="employees-section">
                    <h4>إضافة موظف جديد</h4>
                    <div class="employee-form">
                        <div class="form-grid">
                            <input type="text" id="employeeName" placeholder="اسم الموظف" class="neumorphic-input">
                            <input type="text" id="employeePhone" placeholder="رقم الهاتف" class="neumorphic-input">
                            <input type="text" id="employeePosition" placeholder="الوظيفة" class="neumorphic-input">
                            <input type="number" id="employeeSalary" placeholder="المرتب" class="neumorphic-input" min="0" step="0.01">
                            <input type="date" id="employeeHireDate" placeholder="تاريخ التعيين" class="neumorphic-input">
                            <select id="employeeStatus" class="neumorphic-input">
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                            <textarea id="employeeNotes" placeholder="ملاحظات" class="neumorphic-input" rows="2"></textarea>
                        </div>

                        <!-- صلاحيات الموظف -->
                        <div class="employee-permissions">
                            <h5>صلاحيات الموظف:</h5>
                            <div class="permissions-grid">
                                <label><input type="checkbox" id="permSales"> المبيعات</label>
                                <label><input type="checkbox" id="permPurchases"> المشتريات</label>
                                <label><input type="checkbox" id="permProducts"> إدارة الأصناف</label>
                                <label><input type="checkbox" id="permManufacturing"> التصنيع</label>
                                <label><input type="checkbox" id="permWastage"> الهالك</label>
                                <label><input type="checkbox" id="permReports"> التقارير</label>
                                <label><input type="checkbox" id="permSettings"> الإعدادات</label>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button onclick="saveEmployee()" class="neumorphic-button">حفظ الموظف</button>
                            <button onclick="clearEmployeeForm()" class="neumorphic-button secondary">مسح النموذج</button>
                        </div>
                    </div>
                </div>

                <!-- قسم قائمة الموظفين -->
                <div class="employees-section">
                    <h4>قائمة الموظفين</h4>
                    <div id="employeesList" class="employees-list"></div>
                </div>
            </div>
        </div>

        <!-- علامة تبويب الحضور والانصراف -->
        <div id="attendanceTab" class="tab-content">
            <div class="attendance-container">
                <h3>إدارة الحضور والانصراف</h3>

                <!-- قسم تسجيل الحضور -->
                <div class="attendance-section">
                    <h4>تسجيل الحضور والانصراف</h4>
                    <div class="attendance-form">
                        <div class="form-grid">
                            <select id="attendanceEmployee" class="neumorphic-input">
                                <option value="">اختر الموظف</option>
                            </select>
                            <input type="date" id="attendanceDate" class="neumorphic-input">
                            <input type="time" id="checkInTime" placeholder="وقت الحضور" class="neumorphic-input">
                            <input type="time" id="checkOutTime" placeholder="وقت الانصراف" class="neumorphic-input">
                            <textarea id="attendanceNotes" placeholder="ملاحظات" class="neumorphic-input" rows="2"></textarea>
                        </div>

                        <div class="quick-actions">
                            <button onclick="quickCheckIn()" class="neumorphic-button">تسجيل حضور سريع</button>
                            <button onclick="quickCheckOut()" class="neumorphic-button">تسجيل انصراف سريع</button>
                        </div>

                        <div class="form-actions">
                            <button onclick="saveAttendance()" class="neumorphic-button">حفظ السجل</button>
                            <button onclick="clearAttendanceForm()" class="neumorphic-button secondary">مسح النموذج</button>
                        </div>
                    </div>
                </div>

                <!-- قسم سجل الحضور -->
                <div class="attendance-section">
                    <h4>سجل الحضور والانصراف</h4>
                    <div class="filter-section">
                        <div class="form-grid">
                            <select id="attendanceFilterEmployee" class="neumorphic-input">
                                <option value="">جميع الموظفين</option>
                            </select>
                            <input type="date" id="attendanceFilterStart" class="neumorphic-input" placeholder="من تاريخ">
                            <input type="date" id="attendanceFilterEnd" class="neumorphic-input" placeholder="إلى تاريخ">
                            <button onclick="filterAttendance()" class="neumorphic-button">فلترة</button>
                        </div>
                    </div>
                    <div id="attendanceList" class="attendance-list"></div>
                </div>

                <!-- قسم تقرير ساعات العمل -->
                <div class="attendance-section">
                    <h4>تقرير ساعات العمل</h4>
                    <div id="workHoursReport" class="work-hours-report"></div>
                </div>
            </div>
        </div>

        <!-- علامة تبويب صافي الربح -->
        <div id="profitTab" class="tab-content">
            <div class="profit-container">
                <h3>تقرير صافي الربح</h3>

                <!-- قسم فلترة التقرير -->
                <div class="profit-section">
                    <h4>فترة التقرير</h4>
                    <div class="filter-section">
                        <div class="form-grid">
                            <input type="date" id="profitStartDate" class="neumorphic-input" placeholder="من تاريخ">
                            <input type="date" id="profitEndDate" class="neumorphic-input" placeholder="إلى تاريخ">
                            <select id="profitPeriod" class="neumorphic-input">
                                <option value="custom">فترة مخصصة</option>
                                <option value="today">اليوم</option>
                                <option value="week">هذا الأسبوع</option>
                                <option value="month">هذا الشهر</option>
                                <option value="year">هذا العام</option>
                            </select>
                            <button onclick="generateProfitReport()" class="neumorphic-button">إنشاء التقرير</button>
                        </div>
                    </div>
                </div>

                <!-- قسم ملخص الأرباح -->
                <div class="profit-section">
                    <h4>ملخص الأرباح والخسائر</h4>
                    <div id="profitSummary" class="profit-summary">
                        <div class="profit-card">
                            <h5>إجمالي المبيعات</h5>
                            <div class="profit-amount" id="totalSales">0.00 ر.س</div>
                        </div>
                        <div class="profit-card">
                            <h5>تكلفة المشتريات</h5>
                            <div class="profit-amount" id="totalPurchases">0.00 ر.س</div>
                        </div>
                        <div class="profit-card">
                            <h5>تكلفة التصنيع</h5>
                            <div class="profit-amount" id="totalManufacturing">0.00 ر.س</div>
                        </div>
                        <div class="profit-card">
                            <h5>المصروفات</h5>
                            <div class="profit-amount" id="totalExpenses">0.00 ر.س</div>
                        </div>
                        <div class="profit-card">
                            <h5>مرتبات الموظفين</h5>
                            <div class="profit-amount" id="totalSalaries">0.00 ر.س</div>
                        </div>
                        <div class="profit-card">
                            <h5>قيمة الهالك</h5>
                            <div class="profit-amount" id="totalWastage">0.00 ر.س</div>
                        </div>
                        <div class="profit-card net-profit">
                            <h5>صافي الربح</h5>
                            <div class="profit-amount" id="netProfit">0.00 ر.س</div>
                        </div>
                    </div>
                </div>

                <!-- قسم التفاصيل -->
                <div class="profit-section">
                    <h4>تفاصيل التقرير</h4>
                    <div id="profitDetails" class="profit-details"></div>
                </div>
            </div>
        </div>

        <!-- علامة تبويب الإعدادات -->
        <div id="settingsTab" class="tab-content">
            <div class="settings-container">
                <h3>الإعدادات</h3>

                <!-- إعدادات الشركة -->
                <div class="settings-section">
                    <h4>بيانات الشركة/المحل</h4>
                    <div class="form-grid">
                        <input type="text" id="companyName" placeholder="اسم الشركة/المحل" class="neumorphic-input">
                        <input type="text" id="activity" placeholder="النشاط" class="neumorphic-input">
                        <input type="text" id="commercialRegister" placeholder="السجل التجاري" class="neumorphic-input">
                        <input type="text" id="companyPhone" placeholder="رقم الهاتف" class="neumorphic-input">
                        <input type="text" id="companyAddress" placeholder="العنوان" class="neumorphic-input">
                        <input type="email" id="companyEmail" placeholder="البريد الإلكتروني" class="neumorphic-input">
                        <input type="text" id="customField1" placeholder="المالك / المدير" class="neumorphic-input">
                        <input type="text" id="customField2" placeholder="الرقم الضريبي" class="neumorphic-input">
                    </div>
                </div>

                <!-- إعدادات العملة -->
                <div class="settings-section">
                    <h4>إعدادات العملة</h4>
                    <div class="form-grid">
                        <input type="text" id="mainCurrency" placeholder="العملة الرئيسية" class="neumorphic-input">
                        <input type="text" id="subCurrency" placeholder="العملة الفرعية" class="neumorphic-input">
                        <input type="text" id="currencySymbol" placeholder="رمز العملة" class="neumorphic-input">
                    </div>
                </div>

                <!-- إعدادات الضريبة -->
                <div class="settings-section">
                    <h4>إعدادات الضريبة</h4>
                    <div class="form-grid">
                        <label class="checkbox-label">
                            <input type="checkbox" id="isTaxable"> هل مسؤول بالضريبة
                        </label>
                        <input type="number" id="taxRate" placeholder="نسبة الضريبة %" class="neumorphic-input" step="0.01">
                        <input type="text" id="companyTaxNumber" placeholder="الرقم الضريبي للشركة" class="neumorphic-input">
                    </div>
                </div>

                <!-- إعدادات الفواتير -->
                <div class="settings-section">
                    <h4>إعدادات الفواتير</h4>
                    <div class="form-grid">
                        <label class="checkbox-label">
                            <input type="checkbox" id="showCompanyInInvoice"> عرض بيانات الشركة في الفواتير
                        </label>
                    </div>
                </div>

                <!-- تغيير كلمة المرور -->
                <div class="settings-section">
                    <h4>تغيير كلمة المرور</h4>
                    <div class="form-grid">
                        <input type="password" id="currentPassword" placeholder="كلمة المرور الحالية" class="neumorphic-input">
                        <input type="password" id="newPassword" placeholder="كلمة المرور الجديدة" class="neumorphic-input">
                        <input type="password" id="confirmPassword" placeholder="تأكيد كلمة المرور" class="neumorphic-input">
                    </div>
                    <button onclick="changePassword()" class="neumorphic-button">تغيير كلمة المرور</button>
                </div>

                <!-- حفظ الإعدادات -->
                <div class="settings-section">
                    <button onclick="saveAllSettings()" class="neumorphic-button primary">حفظ جميع الإعدادات</button>
                    <button onclick="showAboutDialog()" class="neumorphic-button" style="background: #74b9ff; color: white; margin-right: 10px;">حول البرنامج</button>
                    <button onclick="openFacebookPage()" class="neumorphic-button" style="background: #1877f2; color: white; margin-right: 10px;">متابعة صفحتنا على الفيسبوك</button>
                </div>

                <!-- إعادة ضبط البيانات -->
                <div class="settings-section">
                    <h4>إعادة ضبط البيانات</h4>
                    <p class="warning-text">تحذير: هذا الإجراء سيحذف جميع البيانات نهائياً</p>
                    <button onclick="resetAllData()" class="neumorphic-button danger">إعادة ضبط جميع البيانات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة الفاتورة -->
    <div id="invoiceModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>فاتورة البيع</h3>
                <button onclick="closeInvoiceModal()" class="close-btn">&times;</button>
            </div>
            <div id="invoiceContent" class="invoice-content"></div>
            <div class="modal-actions">
                <button onclick="printInvoice()" class="neumorphic-button primary">طباعة</button>
                <button onclick="closeInvoiceModal()" class="neumorphic-button secondary">إغلاق</button>
            </div>
        </div>
    </div>

    <!-- نافذة تفاصيل العميل -->
    <div id="customerDetailsModal" class="modal hidden">
        <div class="modal-content customer-details-modal">
            <div class="modal-header">
                <h3>تفاصيل العميل</h3>
                <button onclick="closeCustomerDetailsModal()" class="close-btn">&times;</button>
            </div>
            <div id="customerDetailsContent" class="customer-details-content"></div>
            <div class="modal-actions">
                <button onclick="showPaymentForm()" class="neumorphic-button" style="background: #00b894; color: white;">تسجيل دفعة</button>
                <button onclick="closeCustomerDetailsModal()" class="neumorphic-button secondary">إغلاق</button>
            </div>
        </div>
    </div>

    <!-- نافذة تسجيل الدفعة -->
    <div id="paymentModal" class="modal hidden">
        <div class="modal-content payment-modal">
            <div class="modal-header">
                <h3>تسجيل دفعة</h3>
                <button onclick="closePaymentModal()" class="close-btn">&times;</button>
            </div>
            <div class="payment-form">
                <div class="form-grid">
                    <input type="hidden" id="paymentCustomerId">
                    <input type="number" id="paymentAmount" placeholder="المبلغ المدفوع" class="neumorphic-input" step="0.01">
                    <textarea id="paymentNotes" placeholder="ملاحظات (اختياري)" class="neumorphic-input"></textarea>
                </div>
            </div>
            <div class="modal-actions">
                <button onclick="recordPayment()" class="neumorphic-button primary">تسجيل الدفعة</button>
                <button onclick="closePaymentModal()" class="neumorphic-button secondary">إلغاء</button>
            </div>
        </div>
    </div>

    <!-- نافذة حول البرنامج -->
    <div id="aboutModal" class="modal hidden">
        <div class="modal-content about-modal">
            <div class="modal-header">
                <h3>حول البرنامج</h3>
                <button onclick="closeAboutModal()" class="close-btn">&times;</button>
            </div>
            <div class="about-content">
                <div class="about-logo">🏪</div>
                <h2>StoreMaster v2.0</h2>
                <p>نظام إدارة المخازن المتطور والشامل</p>

                <div class="about-features">
                    <h4>المميزات الرئيسية:</h4>
                    <ul>
                        <li>إدارة المنتجات والمخزون المتقدمة</li>
                        <li>نظام التصنيع والإنتاج</li>
                        <li>إدارة الموظفين والحضور والانصراف</li>
                        <li>نظام المرتجعات والهالك</li>
                        <li>تقارير صافي الربح التفصيلية</li>
                        <li>نظام المبيعات والمشتريات</li>
                        <li>إدارة العملاء والموردين</li>
                        <li>الثيم الداكن والمضيء</li>
                    </ul>
                </div>

                <div class="about-developer">
                    <p><strong>تم تطويره بواسطة:</strong></p>
                    <p class="developer-name">Eng / Hossam Osama</p>

                    <div class="contact-buttons">
                        <button onclick="openWhatsApp()" class="contact-btn whatsapp-btn">
                            📱 واتساب
                        </button>
                        <button onclick="openFacebookDeveloper()" class="contact-btn facebook-btn">
                            📘 فيسبوك
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-actions">
                <button onclick="closeAboutModal()" class="neumorphic-button primary">إغلاق</button>
            </div>
        </div>

        <!-- حقوق الطبع -->
        <div class="footer">
            تم تطوير هذا البرنامج بواسطة <a href="https://www.youtube.com/@Techno_flash" target="_blank">تكنوفلاش</a>
        </div>
    </div>

    <!-- مكتبة XLSX لقراءة وكتابة ملفات Excel -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <script src="main.js"></script>
    <script src="database.js"></script>
    <script src="reports.js"></script>
    <script src="purchases.js"></script>
    <script src="suppliers.js"></script>
    <script src="dashboard.js"></script>
    <script src="app.js"></script>
    <script src="reset.js"></script>

    <!-- ملفات JavaScript للميزات الجديدة -->
    <script src="manufacturing.js"></script>
    <script src="wastage.js"></script>
    <script src="employees.js"></script>
    <script src="attendance.js"></script>
    <script src="profit.js"></script>
    <script src="returns.js"></script>
    <script src="sea_shipping.js"></script>
    <script src="domestic_shipping.js"></script>
    <script src="customs.js"></script>
    <script src="treasury.js"></script>
    <script src="banks.js"></script>
</body>
</html>
