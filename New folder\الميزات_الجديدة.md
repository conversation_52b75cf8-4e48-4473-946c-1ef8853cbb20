# StoreMaster v2.0 - الميزات الجديدة

## 📋 نظرة عامة
تم تطوير **StoreMaster** ليصبح نظام إدارة المخازن الأكثر تطوراً وشمولية، مع إضافة مجموعة واسعة من الميزات الجديدة لتحسين كفاءة العمل وإدارة العمليات بشكل احترافي.

## 🔐 نظام تسجيل الدخول الجديد
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- واجهة تسجيل دخول محدثة ومحسنة

---

## 🏭 صفحة التصنيع (الإنتاج)

### الميزات الرئيسية:
- **إدارة الوصفات التصنيعية**: إنشاء وحفظ وصفات للمنتجات النهائية
- **قائمة الخامات**: تحديد الخامات المطلوبة والكميات لكل وصفة
- **عمليات التصنيع**: تسجيل عمليات إنتاج جديدة مع خصم تلقائي للخامات
- **إضافة تلقائية للمنتج النهائي**: زيادة مخزون المنتج النهائي تلقائياً
- **تنبيهات نقص الخامات**: تحذيرات عند عدم توفر كميات كافية
- **سجل عمليات التصنيع**: تتبع جميع عمليات الإنتاج مع التفاصيل

### كيفية الاستخدام:
1. انتقل إلى تبويب "التصنيع"
2. أنشئ وصفة جديدة بإدخال اسم المنتج النهائي والكمية المنتجة
3. أضف الخامات المطلوبة مع كمياتها
4. احفظ الوصفة للاستخدام المتكرر
5. لبدء عملية تصنيع، اختر الوصفة والكمية المطلوبة
6. سيتم خصم الخامات وإضافة المنتج النهائي تلقائياً

---

## 🗑️ صفحة الهالك

### الميزات الرئيسية:
- **تسجيل الأصناف التالفة**: تسجيل المنتجات التالفة مع السبب
- **أسباب متنوعة للهالك**: كسر، انتهاء صلاحية، تلف، فقدان، أخرى
- **خصم تلقائي من المخزون**: تحديث المخزون فوراً عند تسجيل الهالك
- **تقارير الهالك**: إحصائيات شاملة حسب السبب والمنتج
- **فلترة البيانات**: البحث حسب التاريخ والسبب
- **حساب قيمة الهالك**: تقدير الخسائر المالية

### كيفية الاستخدام:
1. انتقل إلى تبويب "الهالك"
2. اختر الصنف التالف والكمية
3. حدد سبب الهالك والموظف المسئول
4. أضف ملاحظات إضافية إذا لزم الأمر
5. احفظ السجل - سيتم خصم الكمية من المخزون تلقائياً

---

## 👥 صفحة الموظفين

### الميزات الرئيسية:
- **إدارة شاملة للموظفين**: إضافة، تعديل، حذف الموظفين
- **معلومات تفصيلية**: الاسم، الهاتف، البريد، الوظيفة، المرتب، تاريخ التعيين
- **نظام الصلاحيات**: تحديد صلاحيات كل موظف في النظام
- **تفعيل/إيقاف الموظفين**: إدارة حالة الموظفين دون حذف البيانات
- **ربط مع العمليات**: ربط الموظفين بعمليات التصنيع والهالك والحضور

### الصلاحيات المتاحة:
- المبيعات
- المشتريات
- إدارة الأصناف
- التصنيع
- الهالك
- التقارير
- الإعدادات

---

## ⏰ صفحة الحضور والانصراف

### الميزات الرئيسية:
- **تسجيل الحضور والانصراف**: تسجيل يدوي أو سريع
- **حساب ساعات العمل**: حساب تلقائي لساعات العمل الفعلية
- **تتبع التأخيرات**: رصد التأخير والانصراف المبكر
- **تقارير شهرية ويومية**: إحصائيات مفصلة لكل موظف
- **فلترة البيانات**: البحث حسب الموظف والتاريخ

### كيفية الاستخدام:
1. انتقل إلى تبويب "الحضور والانصراف"
2. اختر الموظف والتاريخ
3. استخدم "تسجيل حضور سريع" أو أدخل الأوقات يدوياً
4. احفظ السجل لحساب ساعات العمل تلقائياً

---

## 💰 صفحة صافي الربح

### الميزات الرئيسية:
- **تقرير شامل للأرباح**: حساب صافي الربح بدقة
- **معادلة متكاملة**: المبيعات - (المشتريات + التصنيع + المصروفات + المرتبات + الهالك)
- **فترات زمنية متنوعة**: يوم، أسبوع، شهر، سنة، أو فترة مخصصة
- **تفاصيل مفصلة**: تحليل كل عنصر من عناصر الحساب
- **نسب الأرباح**: حساب هامش الربح ونسبة التكاليف

### المعادلة المستخدمة:
```
صافي الربح = المبيعات - (المشتريات + تكلفة التصنيع + المصروفات + المرتبات + قيمة الهالك)
```

---

## 🔄 تحديثات صفحة المبيعات - المرتجعات

### الميزات الجديدة:
- **زر مرتجع لكل فاتورة**: إمكانية إرجاع أي فاتورة مبيعات
- **اختيار الأصناف المرتجعة**: تحديد المنتجات والكميات المراد إرجاعها
- **أسباب المرتجع**: تسجيل سبب الإرجاع والموظف المسئول
- **إضافة تلقائية للمخزون**: زيادة الكميات في المخزون فوراً
- **سجل المرتجعات**: تتبع جميع عمليات الإرجاع

### كيفية الاستخدام:
1. انتقل إلى تقرير المبيعات
2. اضغط على زر "مرتجع" بجانب الفاتورة المطلوبة
3. اختر الأصناف والكميات المراد إرجاعها
4. حدد سبب المرتجع والموظف المسئول
5. أكد العملية - سيتم تحديث المخزون تلقائياً

---

## ⚙️ تحديثات الإعدادات

### إضافات جديدة:
- **رابط صفحة الفيسبوك**: زر للوصول المباشر لصفحة الشركة على فيسبوك
- **فتح في نافذة جديدة**: الرابط يفتح في متصفح منفصل

---

## 🔗 التكامل التلقائي بين الصفحات

| العملية | التأثير على المخزون | التأثير على النظام |
|---------|-------------------|-------------------|
| بيع منتج | خصم من المخزون | إضافة في تقرير المبيعات |
| مرتجع منتج | إضافة للمخزون | إضافة في سجل المرتجعات |
| تصنيع منتج | خصم خامات + إضافة منتج نهائي | إضافة في سجل التصنيع |
| تسجيل هالك | خصم من المخزون | إضافة في تقرير الهالك |
| حضور موظف | لا تغيير | إضافة في سجل الدوام |
| صرف مرتب | لا تغيير | إضافة في المصروفات |
| حساب الربح | لا تغيير مباشر | حساب تلقائي من جميع البيانات |

---

## 📱 التوافق والاستجابة

- **تصميم متجاوب**: يعمل على جميع أحجام الشاشات
- **واجهة سهلة الاستخدام**: تصميم بديهي ومألوف
- **أداء محسن**: تحميل سريع وتفاعل سلس
- **حفظ تلقائي**: جميع البيانات تُحفظ فوراً

---

## 🚀 كيفية البدء

1. **افتح التطبيق**: شغل ملف "Warehouse management system.exe"
2. **استكشف الصفحات الجديدة**: انتقل بين التبويبات الجديدة
3. **أضف الموظفين**: ابدأ بإضافة موظفين في صفحة الموظفين
4. **أنشئ وصفات التصنيع**: حدد المنتجات والخامات المطلوبة
5. **ابدأ التسجيل**: سجل عمليات التصنيع والحضور والهالك
6. **راجع التقارير**: تابع صافي الربح والإحصائيات

---

## 👨‍💻 معلومات المطور

**تم تطوير StoreMaster بواسطة:**
- **المطور**: Eng / Hossam Osama
- **الشركة**: H-TECH

## 📞 الدعم الفني والتواصل

للحصول على المساعدة أو الإبلاغ عن مشاكل:

### 📱 الواتساب (الدعم المباشر)
- **رقم الهاتف**: 01225396729
- **متاح للدعم الفني والاستفسارات**

### 📘 صفحة الفيسبوك
- **رابط الصفحة**: https://web.facebook.com/profile.php?id=61578888731370
- **للمتابعة والتحديثات**

### 🔗 الوصول السريع
- استخدم أزرار التواصل في صفحة "حول البرنامج"
- أو الأزرار الموجودة في صفحة الإعدادات

---

## 📝 ملاحظات مهمة

- **النسخ الاحتياطي**: احرص على عمل نسخة احتياطية من البيانات بانتظام
- **التدريب**: تأكد من تدريب الموظفين على الميزات الجديدة
- **الصلاحيات**: راجع صلاحيات الموظفين بعناية
- **المراجعة**: راجع التقارير بانتظام لضمان دقة البيانات

---

## 🏷️ معلومات الإصدار

- **اسم التطبيق**: StoreMaster
- **الإصدار**: v2.0
- **المطور**: Eng / Hossam Osama
- **الشركة**: H-TECH
- **تاريخ الإصدار**: 2024

---

*تم تطوير StoreMaster v2.0 بواسطة H-TECH لتوفير حل شامل ومتطور لإدارة المخازن والعمليات التجارية.*
