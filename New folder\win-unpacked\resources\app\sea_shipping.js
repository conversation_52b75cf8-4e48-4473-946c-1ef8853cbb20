// ===== الشحن البحري =====

const seaShipping = (() => {
    function showCreateForm() {
        const container = document.getElementById('seaShippingContent');
        container.innerHTML = `
            <div class="form-container">
                <h4>إضافة شحنة بحرية</h4>
                <div class="form-grid">
                    <input id="ss_shipmentNumber" class="neumorphic-input" placeholder="رقم الشحنة">
                    <input id="ss_shipper" class="neumorphic-input" placeholder="المرسل">
                    <input id="ss_consignee" class="neumorphic-input" placeholder="المستلم">
                    <input id="ss_pol" class="neumorphic-input" placeholder="ميناء الشحن">
                    <input id="ss_pod" class="neumorphic-input" placeholder="ميناء الوصول">
                    <input id="ss_line" class="neumorphic-input" placeholder="خط الملاحة">
                    <input id="ss_containerNo" class="neumorphic-input" placeholder="رقم الحاوية">
                    <input id="ss_bl" class="neumorphic-input" placeholder="رقم بوليصة الشحن">
                    <input id="ss_containerType" class="neumorphic-input" placeholder="نوع الحاوية (20ft, 40ft, Reefer)">
                    <input id="ss_grossWeight" type="number" step="0.01" class="neumorphic-input" placeholder="الوزن الإجمالي">
                    <input id="ss_netWeight" type="number" step="0.01" class="neumorphic-input" placeholder="الوزن الصافي">
                    <select id="ss_status" class="neumorphic-input">
                        <option value="Booked">حجز</option>
                        <option value="Loading">قيد التحميل</option>
                        <option value="In Transit">في البحر</option>
                        <option value="Arrived">وصلت الميناء</option>
                        <option value="Delivered">تم التسليم</option>
                    </select>
                </div>
                <div class="form-grid">
                    <input id="ss_oceanFreight" type="number" step="0.01" class="neumorphic-input" placeholder="رسوم الشحن البحري">
                    <input id="ss_portCharges" type="number" step="0.01" class="neumorphic-input" placeholder="رسوم تفريغ الميناء">
                    <input id="ss_demurrage" type="number" step="0.01" class="neumorphic-input" placeholder="Demurrage & Detention">
                    <input id="ss_otherCharges" type="number" step="0.01" class="neumorphic-input" placeholder="مصاريف إضافية (وقود/مناولة)">
                </div>
                <div class="form-actions">
                    <button onclick="seaShipping.save()" class="neumorphic-button primary">حفظ</button>
                </div>
            </div>
        `;
    }

    function save() {
        const record = {
            id: generateId(),
            shipmentNumber: document.getElementById('ss_shipmentNumber').value.trim(),
            shipper: document.getElementById('ss_shipper').value.trim(),
            consignee: document.getElementById('ss_consignee').value.trim(),
            pol: document.getElementById('ss_pol').value.trim(),
            pod: document.getElementById('ss_pod').value.trim(),
            shippingLine: document.getElementById('ss_line').value.trim(),
            containerNumber: document.getElementById('ss_containerNo').value.trim(),
            billOfLading: document.getElementById('ss_bl').value.trim(),
            containerType: document.getElementById('ss_containerType').value.trim(),
            grossWeight: safeNumber(parseFloat(document.getElementById('ss_grossWeight').value), 0),
            netWeight: safeNumber(parseFloat(document.getElementById('ss_netWeight').value), 0),
            status: document.getElementById('ss_status').value,
            costs: {
                oceanFreight: safeNumber(parseFloat(document.getElementById('ss_oceanFreight').value), 0),
                portCharges: safeNumber(parseFloat(document.getElementById('ss_portCharges').value), 0),
                demurrage: safeNumber(parseFloat(document.getElementById('ss_demurrage').value), 0),
                otherCharges: safeNumber(parseFloat(document.getElementById('ss_otherCharges').value), 0)
            },
            createdAt: new Date().toISOString()
        };

        if (!window.seaShipments) window.seaShipments = [];
        window.seaShipments.push(record);
        saveData();
        showNotification('تم حفظ الشحنة البحرية', 'success');
        list();
    }

    function list() {
        const container = document.getElementById('seaShippingContent');
        if (!window.seaShipments || window.seaShipments.length === 0) {
            container.innerHTML = '<div style="text-align:center;color:#666;padding:20px;">لا توجد شحنات</div>';
            return;
        }
        container.innerHTML = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>رقم الشحنة</th><th>POL</th><th>POD</th><th>الحاوية</th><th>الحالة</th><th>التكلفة</th>
                    </tr>
                </thead>
                <tbody>
                    ${window.seaShipments.map(s => `
                        <tr>
                            <td>${s.shipmentNumber}</td>
                            <td>${s.pol}</td>
                            <td>${s.pod}</td>
                            <td>${s.containerNumber}</td>
                            <td>${s.status}</td>
                            <td>${formatCurrency((s.costs.oceanFreight||0)+(s.costs.portCharges||0)+(s.costs.demurrage||0)+(s.costs.otherCharges||0))}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    function showRunningReport() {
        const container = document.getElementById('seaShippingContent');
        const data = (window.seaShipments||[]).filter(s => ['Booked','Loading','In Transit','Arrived'].includes(s.status));
        container.innerHTML = generateReportTable(data);
    }

    function showCompletedReport() {
        const container = document.getElementById('seaShippingContent');
        const data = (window.seaShipments||[]).filter(s => s.status === 'Delivered');
        container.innerHTML = generateReportTable(data);
    }

    function showCostsReport() {
        const container = document.getElementById('seaShippingContent');
        const data = (window.seaShipments||[]);
        const total = data.reduce((sum, s) => sum + (s.costs.oceanFreight||0)+(s.costs.portCharges||0)+(s.costs.demurrage||0)+(s.costs.otherCharges||0), 0);
        container.innerHTML = `<div class="report-summary">إجمالي التكاليف: ${formatCurrency(total)}</div>` + generateReportTable(data);
    }

    function generateReportTable(rows){
        if (!rows || rows.length===0) return '<div style="text-align:center;color:#666;padding:20px;">لا توجد بيانات</div>';
        return `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>رقم الشحنة</th><th>المرسل</th><th>المستلم</th><th>POL</th><th>POD</th><th>الحاوية</th><th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    ${rows.map(s => `
                        <tr>
                            <td>${s.shipmentNumber}</td>
                            <td>${s.shipper}</td>
                            <td>${s.consignee}</td>
                            <td>${s.pol}</td>
                            <td>${s.pod}</td>
                            <td>${s.containerNumber} (${s.containerType})</td>
                            <td>${s.status}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    return { showCreateForm, save, showRunningReport, showCompletedReport, showCostsReport };
})();


