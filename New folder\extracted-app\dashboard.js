// ===== لوحة المعلومات =====

// تحديث لوحة المعلومات
function updateDashboard() {
    const report = getDashboardReport();
    
    // تحديث إحصائيات المشتريات
    document.getElementById('purchasesToday').textContent = formatCurrency(report.purchases.today);
    document.getElementById('purchasesWeek').textContent = formatCurrency(report.purchases.week);
    document.getElementById('purchasesMonth').textContent = formatCurrency(report.purchases.month);
    document.getElementById('purchasesTotal').textContent = formatCurrency(report.purchases.total);
    
    // تحديث إحصائيات المبيعات
    document.getElementById('salesToday').textContent = formatCurrency(report.sales.today);
    document.getElementById('salesWeek').textContent = formatCurrency(report.sales.week);
    document.getElementById('salesMonth').textContent = formatCurrency(report.sales.month);
    document.getElementById('salesTotal').textContent = formatCurrency(report.sales.total);
    
    // تحديث إحصائيات المصروفات
    document.getElementById('expensesToday').textContent = formatCurrency(report.expenses.today);
    document.getElementById('expensesWeek').textContent = formatCurrency(report.expenses.week);
    document.getElementById('expensesMonth').textContent = formatCurrency(report.expenses.month);
    document.getElementById('expensesTotal').textContent = formatCurrency(report.expenses.total);
    
    // تحديث إحصائيات المقبوضات
    document.getElementById('receiptsToday').textContent = formatCurrency(report.cashReceipts.today);
    document.getElementById('receiptsWeek').textContent = formatCurrency(report.cashReceipts.week);
    document.getElementById('receiptsMonth').textContent = formatCurrency(report.cashReceipts.month);
    document.getElementById('receiptsYear').textContent = formatCurrency(report.cashReceipts.year);
    document.getElementById('receiptsTotal').textContent = formatCurrency(report.cashReceipts.total);
    
    // تحديث قائمة أفضل المنتجات
    updateTopProductsList(report.topProducts);
    
    // تحديث قائمة أكثر المديونين
    updateTopDebtorsList(report.topDebtors);
}

// تحديث قائمة أفضل المنتجات
function updateTopProductsList(topProducts) {
    const container = document.getElementById('topProductsList');
    
    if (topProducts.length === 0) {
        container.innerHTML = '<div class="no-data">لا توجد بيانات مبيعات</div>';
        return;
    }
    
    let html = '<div class="top-list-items">';
    
    topProducts.forEach((item, index) => {
        html += `
            <div class="list-item">
                <div class="item-rank">${index + 1}</div>
                <div class="item-details">
                    <div class="item-name">${item.product ? item.product.name : 'منتج محذوف'}</div>
                    <div class="item-code">${item.product ? item.product.code : '-'}</div>
                </div>
                <div class="item-value">${item.quantity} ${item.product ? item.product.unit : ''}</div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// تحديث قائمة أكثر المديونين
function updateTopDebtorsList(topDebtors) {
    const container = document.getElementById('topDebtorsList');
    
    if (topDebtors.length === 0) {
        container.innerHTML = '<div class="no-data">لا توجد ديون مستحقة</div>';
        return;
    }
    
    let html = '<div class="top-list-items">';
    
    topDebtors.forEach((debtor, index) => {
        html += `
            <div class="list-item">
                <div class="item-rank">${index + 1}</div>
                <div class="item-details">
                    <div class="item-name">${debtor.name}</div>
                </div>
                <div class="item-value debt-amount">${formatCurrency(debtor.balance)}</div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// تحديث الإحصائيات في الوقت الفعلي
function refreshDashboardStats() {
    // حساب الإحصائيات العامة
    const totalSales = sales.reduce((sum, s) => sum + (typeof s.total === 'number' ? s.total : (s.totalAmount || 0)), 0);
    const totalPurchases = purchases.reduce((sum, p) => sum + (p.totalAmount || 0), 0);
    const totalExpenses = expenses.reduce((sum, e) => sum + e.amount, 0);

    // إضافة معلومات إضافية للوحة المعلومات
    addDashboardSummary();
}

// إضافة ملخص إضافي للوحة المعلومات
function addDashboardSummary() {
    const dashboardContainer = document.querySelector('.dashboard-container');

    // التحقق من وجود قسم الملخص
    let summarySection = document.getElementById('dashboardSummary');
    if (!summarySection) {
        summarySection = document.createElement('div');
        summarySection.id = 'dashboardSummary';
        summarySection.className = 'dashboard-section';

        // إدراج قسم الملخص في بداية لوحة المعلومات
        dashboardContainer.insertBefore(summarySection, dashboardContainer.firstChild.nextSibling);
    }

    summarySection.innerHTML = `
        <h4>الملخص العام</h4>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-label">عدد المنتجات</div>
                <div class="stat-value">${products.length}</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">عدد العملاء</div>
                <div class="stat-value">${customers.length}</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">عدد الموردين</div>
                <div class="stat-value">${suppliers.length}</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">المنتجات منخفضة المخزون</div>
                <div class="stat-value warning">${getLowStockProducts().length}</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">إجمالي الديون المستحقة</div>
                <div class="stat-value debt-amount">${formatCurrency(customers.reduce((sum, c) => sum + c.currentBalance, 0))}</div>
            </div>
        </div>
    `;
}

// تحديث لوحة المعلومات عند تغيير البيانات
function onDataChange() {
    updateDashboard();
    refreshDashboardStats();
}

// إضافة مستمعات الأحداث لتحديث لوحة المعلومات
function initializeDashboard() {
    // تحديث لوحة المعلومات عند تحميل الصفحة
    updateDashboard();
    refreshDashboardStats();
    
    // تحديث لوحة المعلومات كل دقيقة
    setInterval(() => {
        if (document.getElementById('dashboardTab').classList.contains('active')) {
            updateDashboard();
            refreshDashboardStats();
        }
    }, 60000); // كل دقيقة
}

// تصدير تقرير لوحة المعلومات
function exportDashboardReport() {
    const report = getDashboardReport();
    const currentDate = new Date().toISOString().split('T')[0];
    
    const reportData = {
        title: 'تقرير لوحة المعلومات',
        date: currentDate,
        companyName: settings.companyName,
        ...report,
        summary: {
            productsCount: products.length,
            customersCount: customers.length,
            suppliersCount: suppliers.length,
            lowStockCount: getLowStockProducts().length,
            totalDebt: customers.reduce((sum, c) => sum + c.currentBalance, 0)
        }
    };
    
    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `dashboard_report_${currentDate}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    showNotification('تم تصدير تقرير لوحة المعلومات بنجاح', 'success');
}

// طباعة تقرير لوحة المعلومات
function printDashboardReport() {
    const report = getDashboardReport();
    const currentDate = formatDateShort(new Date());
    
    const printContent = `
        <div style="font-family: Arial, sans-serif; direction: rtl; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1>${settings.companyName}</h1>
                <h2>تقرير لوحة المعلومات</h2>
                <p>التاريخ: ${currentDate}</p>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h3>المشتريات</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr><td style="border: 1px solid #ddd; padding: 8px;">اليوم</td><td style="border: 1px solid #ddd; padding: 8px;">${formatCurrency(report.purchases.today)}</td></tr>
                    <tr><td style="border: 1px solid #ddd; padding: 8px;">الأسبوع</td><td style="border: 1px solid #ddd; padding: 8px;">${formatCurrency(report.purchases.week)}</td></tr>
                    <tr><td style="border: 1px solid #ddd; padding: 8px;">الشهر</td><td style="border: 1px solid #ddd; padding: 8px;">${formatCurrency(report.purchases.month)}</td></tr>
                    <tr><td style="border: 1px solid #ddd; padding: 8px;">الإجمالي</td><td style="border: 1px solid #ddd; padding: 8px;">${formatCurrency(report.purchases.total)}</td></tr>
                </table>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h3>المبيعات</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr><td style="border: 1px solid #ddd; padding: 8px;">اليوم</td><td style="border: 1px solid #ddd; padding: 8px;">${formatCurrency(report.sales.today)}</td></tr>
                    <tr><td style="border: 1px solid #ddd; padding: 8px;">الأسبوع</td><td style="border: 1px solid #ddd; padding: 8px;">${formatCurrency(report.sales.week)}</td></tr>
                    <tr><td style="border: 1px solid #ddd; padding: 8px;">الشهر</td><td style="border: 1px solid #ddd; padding: 8px;">${formatCurrency(report.sales.month)}</td></tr>
                    <tr><td style="border: 1px solid #ddd; padding: 8px;">الإجمالي</td><td style="border: 1px solid #ddd; padding: 8px;">${formatCurrency(report.sales.total)}</td></tr>
                </table>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h3>أفضل 10 منتجات مبيعاً</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr style="background-color: #f5f5f5;">
                        <th style="border: 1px solid #ddd; padding: 8px;">الترتيب</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">اسم المنتج</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">الكمية المباعة</th>
                    </tr>
                    ${report.topProducts.map((item, index) => `
                        <tr>
                            <td style="border: 1px solid #ddd; padding: 8px;">${index + 1}</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${item.product ? item.product.name : 'منتج محذوف'}</td>
                            <td style="border: 1px solid #ddd; padding: 8px;">${item.quantity}</td>
                        </tr>
                    `).join('')}
                </table>
            </div>
        </div>
    `;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>تقرير لوحة المعلومات</title>
            </head>
            <body>
                ${printContent}
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}
