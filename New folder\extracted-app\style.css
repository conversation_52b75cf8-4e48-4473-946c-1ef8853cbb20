/* متغيرات الثيم */
:root {
    --bg-primary: #f1f5f9;
    --bg-secondary: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --shadow-light: #ffffff;
    --shadow-dark: #cbd5e1;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    /* ألوان إضافية للحيوية */
    --primary-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    --success-gradient: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --purple-gradient: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    --pink-gradient: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
    --orange-gradient: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
}

body.dark-theme {
    --bg-primary: #2c3e50;
    --bg-secondary: #34495e;
    --text-primary: #ecf0f1;
    --text-secondary: #bdc3c7;
    --shadow-light: #34495e;
    --shadow-dark: #1a252f;
    --accent-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    --gradient-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    min-height: 100vh;
    direction: rtl;
    color: var(--text-primary);
    transition: all 0.4s ease;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
    25% { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 50%, #43e97b 100%); }
    50% { background: linear-gradient(135deg, #fa709a 0%, #fee140 50%, #667eea 100%); }
    75% { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 50%, #764ba2 100%); }
    100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
}

/* انتقالات سلسة لجميع العناصر */
* {
    transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* فئة الإخفاء */
.hidden {
    display: none !important;
}

/* نمط Neumorphism */
.neumorphic-input, .neumorphic-button {
    background: var(--bg-primary);
    border: none;
    border-radius: 15px;
    box-shadow:
        9px 9px 16px var(--shadow-dark),
        -9px -9px 16px var(--shadow-light);
    padding: 12px 20px;
    font-size: 14px;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.neumorphic-input {
    width: 100%;
    margin-bottom: 15px;
}

.neumorphic-input:focus {
    outline: none;
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

.neumorphic-button {
    cursor: pointer;
    font-weight: 600;
    text-align: center;
    min-width: 120px;
    margin: 5px;
}

.neumorphic-button:hover {
    box-shadow:
        6px 6px 12px var(--shadow-dark),
        -6px -6px 12px var(--shadow-light);
    transform: translateY(-2px);
}

.neumorphic-button:active {
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
    transform: translateY(0);
}

.neumorphic-button.primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow:
        6px 6px 16px rgba(59, 130, 246, 0.3),
        -6px -6px 16px var(--shadow-light);
}

.neumorphic-button.primary:hover {
    box-shadow:
        8px 8px 20px rgba(59, 130, 246, 0.4),
        -8px -8px 20px var(--shadow-light);
}

.neumorphic-button.secondary {
    background: var(--purple-gradient);
    color: white;
    box-shadow:
        6px 6px 16px rgba(139, 92, 246, 0.3),
        -6px -6px 16px var(--shadow-light);
}

.neumorphic-button.secondary:hover {
    box-shadow:
        8px 8px 20px rgba(139, 92, 246, 0.4),
        -8px -8px 20px var(--shadow-light);
}

.neumorphic-button.success {
    background: var(--success-gradient);
    color: white;
    box-shadow:
        6px 6px 16px rgba(16, 185, 129, 0.3),
        -6px -6px 16px var(--shadow-light);
}

.neumorphic-button.success:hover {
    box-shadow:
        8px 8px 20px rgba(16, 185, 129, 0.4),
        -8px -8px 20px var(--shadow-light);
}

.neumorphic-button.warning {
    background: var(--warning-gradient);
    color: white;
    box-shadow:
        6px 6px 16px rgba(245, 158, 11, 0.3),
        -6px -6px 16px var(--shadow-light);
}

.neumorphic-button.warning:hover {
    box-shadow:
        8px 8px 20px rgba(245, 158, 11, 0.4),
        -8px -8px 20px var(--shadow-light);
}

.neumorphic-button.danger {
    background: var(--danger-gradient);
    color: white;
    box-shadow:
        6px 6px 16px rgba(239, 68, 68, 0.3),
        -6px -6px 16px var(--shadow-light);
}

.neumorphic-button.danger:hover {
    box-shadow:
        8px 8px 20px rgba(239, 68, 68, 0.4),
        -8px -8px 20px var(--shadow-light);
}

/* شاشة تسجيل الدخول */
.login-screen {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-container {
    background: #e0e5ec;
    padding: 40px;
    border-radius: 25px;
    box-shadow: 
        20px 20px 40px #a3b1c6,
        -20px -20px 40px #ffffff;
    text-align: center;
    min-width: 400px;
}

.login-container h1 {
    margin-bottom: 30px;
    color: #333;
    font-size: 28px;
}

.login-form {
    margin-bottom: 20px;
}

.error-message {
    color: #ff6b6b;
    font-size: 14px;
    margin-top: 10px;
}

/* الواجهة الرئيسية */
.main-app {
    background: var(--bg-primary);
    min-height: 100vh;
}

.hidden {
    display: none !important;
}

/* شريط التنقل */
.navbar {
    background: var(--bg-primary);
    padding: 15px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow:
        0 4px 8px var(--shadow-dark),
        0 -4px 8px var(--shadow-light);
    margin-bottom: 20px;
}

.nav-brand {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
}

.nav-tabs {
    display: flex;
    gap: 10px;
}

.nav-tab {
    background: rgba(255, 255, 255, 0.95);
    border: none;
    padding: 14px 24px;
    border-radius: 16px;
    cursor: pointer;
    font-size: 15px;
    font-weight: 700;
    color: var(--text-primary);
    box-shadow:
        6px 6px 20px rgba(0, 0, 0, 0.1),
        -6px -6px 20px rgba(255, 255, 255, 0.8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 2px solid transparent;
}

.nav-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.6s;
}

.nav-tab:hover::before {
    left: 100%;
}

.nav-tab:hover {
    color: var(--text-primary);
    transform: translateY(-3px);
    box-shadow:
        8px 8px 25px rgba(0, 0, 0, 0.15),
        -8px -8px 25px rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(59, 130, 246, 0.3);
}

.nav-tab.active {
    background: var(--primary-gradient);
    color: white;
    box-shadow:
        8px 8px 25px rgba(59, 130, 246, 0.4),
        -8px -8px 25px rgba(255, 255, 255, 0.9);
    transform: translateY(-4px);
    border: 2px solid rgba(59, 130, 246, 0.6);
}

.logout-btn {
    background: linear-gradient(145deg, #ff6b6b, #ee5a52);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 500;
    box-shadow: 
        4px 4px 8px #a3b1c6,
        -4px -4px 8px #ffffff;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    transform: translateY(-1px);
}

/* محتوى التبويبات */
.tab-content {
    display: none;
    padding: 20px 30px;
    animation: fadeIn 0.3s ease;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تبويب المبيعات */
.sales-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    height: calc(100vh - 140px);
}

.products-grid {
    background: #e0e5ec;
    padding: 20px;
    border-radius: 20px;
    box-shadow: 
        inset 8px 8px 16px #a3b1c6,
        inset -8px -8px 16px #ffffff;
    overflow-y: auto;
}

.products-grid h3 {
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

.grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.product-card {
    background: var(--bg-primary);
    padding: 15px;
    border-radius: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-primary);
    box-shadow:
        6px 6px 12px var(--shadow-dark),
        -6px -6px 12px var(--shadow-light);
}

.product-card:hover {
    transform: translateY(-3px);
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

.product-card.low-stock {
    border: 2px solid #ff6b6b;
    background: linear-gradient(145deg, #ffe0e0, #ffcccc);
}

.product-name {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
}

.product-price {
    color: #667eea;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.product-stock {
    color: #666;
    font-size: 12px;
}

/* سلة المشتريات */
.cart-section {
    background: #e0e5ec;
    padding: 20px;
    border-radius: 20px;
    box-shadow: 
        inset 8px 8px 16px #a3b1c6,
        inset -8px -8px 16px #ffffff;
    display: flex;
    flex-direction: column;
}

.cart-section h3 {
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

.cart-items {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 20px;
}

.cart-item {
    background: var(--bg-primary);
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 12px;
    color: var(--text-primary);
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.cart-item-price {
    color: #667eea;
    font-size: 14px;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.quantity-btn {
    background: #e0e5ec;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    box-shadow: 
        3px 3px 6px #a3b1c6,
        -3px -3px 6px #ffffff;
}

.quantity-btn:hover {
    box-shadow: 
        2px 2px 4px #a3b1c6,
        -2px -2px 4px #ffffff;
}

.quantity-display {
    min-width: 30px;
    text-align: center;
    font-weight: bold;
}

.remove-btn {
    background: linear-gradient(145deg, #ff6b6b, #ee5a52);
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: bold;
    box-shadow: 
        3px 3px 6px #a3b1c6,
        -3px -3px 6px #ffffff;
}

.cart-total {
    background: #e0e5ec;
    padding: 15px;
    border-radius: 12px;
    box-shadow: 
        inset 4px 4px 8px #a3b1c6,
        inset -4px -4px 8px #ffffff;
    margin-bottom: 20px;
}

.total-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.total-final {
    font-weight: bold;
    font-size: 18px;
    color: #333;
    border-top: 2px solid #a3b1c6;
    padding-top: 8px;
    margin-top: 8px;
}

.cart-actions {
    display: flex;
    gap: 10px;
}

/* تنبيه المخزون المنخفض */
.low-stock-alert {
    background: linear-gradient(145deg, #ffe0e0, #ffcccc);
    color: #d63031;
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 15px;
    text-align: center;
    font-weight: bold;
    box-shadow: 
        4px 4px 8px #a3b1c6,
        -4px -4px 8px #ffffff;
}

/* إدارة المنتجات */
.products-management {
    max-width: 1200px;
    margin: 0 auto;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.section-header h3 {
    color: #333;
    font-size: 24px;
}

.form-container {
    background: #e0e5ec;
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 30px;
    box-shadow: 
        inset 8px 8px 16px #a3b1c6,
        inset -8px -8px 16px #ffffff;
}

.form-container h4 {
    margin-bottom: 20px;
    color: #333;
    text-align: center;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.form-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* قائمة المنتجات */
.products-list {
    background: #e0e5ec;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 
        inset 8px 8px 16px #a3b1c6,
        inset -8px -8px 16px #ffffff;
}

.product-item {
    background: #e0e5ec;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 15px;
    box-shadow: 
        6px 6px 12px #a3b1c6,
        -6px -6px 12px #ffffff;
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr auto;
    gap: 15px;
    align-items: center;
}

.product-item.low-stock {
    border: 2px solid #ff6b6b;
}

.product-actions {
    display: flex;
    gap: 5px;
}

.edit-btn, .delete-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 
        3px 3px 6px #a3b1c6,
        -3px -3px 6px #ffffff;
}

.edit-btn {
    background: linear-gradient(145deg, #74b9ff, #0984e3);
    color: white;
}

.delete-btn {
    background: linear-gradient(145deg, #ff6b6b, #ee5a52);
    color: white;
}

/* النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: #e0e5ec;
    padding: 30px;
    border-radius: 20px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 
        20px 20px 40px #a3b1c6,
        -20px -20px 40px #ffffff;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #a3b1c6;
    padding-bottom: 15px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: #ff6b6b;
    color: white;
}

.modal-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    border-top: 2px solid #a3b1c6;
    padding-top: 15px;
}

/* الفاتورة */
.invoice-content {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin: 20px 0;
    color: #333;
    direction: rtl;
}

.invoice-header {
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #333;
    padding-bottom: 15px;
}

.invoice-details {
    margin-bottom: 20px;
}

.invoice-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.invoice-table th,
.invoice-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
}

.invoice-table th {
    background: #f5f5f5;
    font-weight: bold;
}

.invoice-total {
    text-align: left;
    font-size: 18px;
    font-weight: bold;
}

/* التقارير */
.reports-container {
    max-width: 1000px;
    margin: 0 auto;
}

.report-filters {
    background: #e0e5ec;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow: 
        inset 6px 6px 12px #a3b1c6,
        inset -6px -6px 12px #ffffff;
    display: flex;
    gap: 15px;
    align-items: center;
}

.report-content {
    background: #e0e5ec;
    padding: 25px;
    border-radius: 20px;
    box-shadow: 
        inset 8px 8px 16px #a3b1c6,
        inset -8px -8px 16px #ffffff;
}

.report-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: #e0e5ec;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 
        6px 6px 12px #a3b1c6,
        -6px -6px 12px #ffffff;
}

.summary-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.summary-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.summary-value.positive {
    color: #00b894;
}

.summary-value.negative {
    color: #e17055;
}

/* الإعدادات */
.settings-container {
    max-width: 800px;
    margin: 0 auto;
}

.settings-section {
    background: #e0e5ec;
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 25px;
    box-shadow: 
        inset 8px 8px 16px #a3b1c6,
        inset -8px -8px 16px #ffffff;
}

.settings-section h4 {
    margin-bottom: 20px;
    color: #333;
    border-bottom: 2px solid #a3b1c6;
    padding-bottom: 10px;
}

.warning-text {
    color: #e17055;
    font-weight: bold;
    margin-bottom: 15px;
    text-align: center;
}

/* أنماط لوحة المعلومات */
.dashboard-container {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

.dashboard-section {
    margin-bottom: 30px;
    background: #e0e5ec;
    padding: 20px;
    border-radius: 15px;
    box-shadow:
        8px 8px 16px #a3b1c6,
        -8px -8px 16px #ffffff;
}

.dashboard-section h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 18px;
    font-weight: bold;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-card {
    background: #e0e5ec;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    box-shadow:
        inset 5px 5px 10px #a3b1c6,
        inset -5px -5px 10px #ffffff;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-card.profit {
    border-left: 4px solid #00b894;
}

.stat-card.loss {
    border-left: 4px solid #e17055;
}

.stat-card.warning {
    border-left: 4px solid #fdcb6e;
}

.stat-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.stat-value.warning {
    color: #e17055;
}

.debt-amount {
    color: #e17055;
    font-weight: bold;
}

.dashboard-lists {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.top-list {
    background: #e0e5ec;
    padding: 20px;
    border-radius: 15px;
    box-shadow:
        8px 8px 16px #a3b1c6,
        -8px -8px 16px #ffffff;
}

.top-list h4 {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
}

.list-container {
    max-height: 300px;
    overflow-y: auto;
}

.top-list-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.list-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: #e0e5ec;
    border-radius: 8px;
    box-shadow:
        inset 3px 3px 6px #a3b1c6,
        inset -3px -3px 6px #ffffff;
}

.item-rank {
    width: 30px;
    height: 30px;
    background: #74b9ff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    margin-left: 10px;
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: bold;
    color: #333;
}

.item-code {
    font-size: 12px;
    color: #666;
}

.item-value {
    font-weight: bold;
    color: #333;
}

.no-data {
    text-align: center;
    color: #666;
    padding: 20px;
    font-style: italic;
}

/* أنماط المشتريات */
.purchases-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.operation-number {
    background: #e0e5ec;
    padding: 10px 20px;
    border-radius: 10px;
    box-shadow:
        inset 4px 4px 8px #a3b1c6,
        inset -4px -4px 8px #ffffff;
    font-weight: bold;
    color: #333;
}

.purchase-header {
    background: #e0e5ec;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow:
        8px 8px 16px #a3b1c6,
        -8px -8px 16px #ffffff;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    align-items: center;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.payment-method {
    display: flex;
    gap: 20px;
    align-items: center;
}

.payment-method label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

.item-section {
    background: #e0e5ec;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow:
        8px 8px 16px #a3b1c6,
        -8px -8px 16px #ffffff;
}

.item-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 15px;
}

.items-table-section {
    background: #e0e5ec;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow:
        8px 8px 16px #a3b1c6,
        -8px -8px 16px #ffffff;
}

.items-table {
    width: 100%;
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    overflow: hidden;
    box-shadow:
        8px 8px 25px rgba(0, 0, 0, 0.1),
        -8px -8px 25px rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.data-table th,
.data-table td {
    padding: 16px;
    text-align: center;
    border-bottom: 1px solid rgba(203, 213, 225, 0.3);
    transition: all 0.3s ease;
}

.data-table th {
    background: var(--primary-gradient);
    font-weight: 700;
    color: white;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table tr:hover {
    background: rgba(59, 130, 246, 0.05);
    transform: scale(1.01);
}

.data-table tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.5);
}

.table-row.selected {
    background: #74b9ff !important;
    color: white;
}

.btn-small {
    padding: 5px 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    margin: 0 2px;
    box-shadow:
        2px 2px 4px #a3b1c6,
        -2px -2px 4px #ffffff;
}

.btn-small:hover {
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(145deg, #ff6b6b, #ee5a52);
    color: white;
}

.btn-info {
    background: linear-gradient(145deg, #74b9ff, #0984e3);
    color: white;
}

.supplier-section {
    background: #e0e5ec;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
    box-shadow:
        8px 8px 16px #a3b1c6,
        -8px -8px 16px #ffffff;
}

.purchase-summary {
    background: #e0e5ec;
    padding: 15px;
    border-radius: 10px;
    margin-top: 15px;
    box-shadow:
        inset 4px 4px 8px #a3b1c6,
        inset -4px -4px 8px #ffffff;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-weight: bold;
}

.purchase-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

/* أنماط الموردين */
.suppliers-management {
    max-width: 1200px;
    margin: 0 auto;
}

.suppliers-list {
    background: #e0e5ec;
    border-radius: 20px;
    padding: 20px;
    box-shadow:
        inset 8px 8px 16px #a3b1c6,
        inset -8px -8px 16px #ffffff;
}

.supplier-item {
    background: #e0e5ec;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 15px;
    box-shadow:
        6px 6px 12px #a3b1c6,
        -6px -6px 12px #ffffff;
    display: grid;
    grid-template-columns: 2fr 1fr 2fr 1fr auto;
    gap: 15px;
    align-items: center;
}

.actions {
    display: flex;
    gap: 5px;
}

/* أنماط النوافذ المنبثقة الكبيرة */
.large-modal {
    max-width: 900px;
    width: 95%;
}

.supplier-transactions {
    padding: 20px;
}

.supplier-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* أنماط سندات الاستلام */
.receipt-modal {
    max-width: 800px;
    width: 95%;
}

.receipt-header {
    text-align: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #333;
    padding-bottom: 15px;
}

.receipt-info {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    font-size: 14px;
}

.receipt-body {
    line-height: 1.6;
}

.receipt-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

.receipt-table th,
.receipt-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
}

.receipt-table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

.receipt-summary {
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.receipt-footer {
    margin-top: 40px;
    border-top: 1px solid #ddd;
    padding-top: 20px;
}

.signature-section {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    text-align: center;
}

.signature-section div {
    padding: 10px;
    border-bottom: 1px solid #333;
}

/* أنماط checkbox */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: bold;
}

.checkbox-label input[type="checkbox"] {
    width: 20px;
    height: 20px;
    cursor: pointer;
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .sales-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .navbar {
        flex-direction: column;
        gap: 15px;
    }

    .nav-tabs {
        flex-wrap: wrap;
        justify-content: center;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .product-item {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .report-filters {
        flex-direction: column;
    }

    .dashboard-lists {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .supplier-item {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .purchase-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* أنماط المؤشرات السريعة */
.quick-indicators {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.indicator-card {
    background: var(--bg-primary);
    padding: 20px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
    transition: all 0.3s ease;
}

.indicator-card:hover {
    transform: translateY(-2px);
}

.indicator-card.warning {
    border-left: 4px solid var(--warning-color);
}

.indicator-icon {
    font-size: 24px;
    width: 40px;
    text-align: center;
}

.indicator-info {
    flex: 1;
}

.indicator-label {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.indicator-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
}

/* أنماط السوم (Tags) */
.tag {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    margin: 2px;
}

.tag-success {
    background: var(--success-color);
    color: white;
}

.tag-warning {
    background: var(--warning-color);
    color: white;
}

.tag-danger {
    background: var(--danger-color);
    color: white;
}

.tag-info {
    background: var(--info-color);
    color: white;
}

.tag-cash {
    background: var(--info-color);
    color: white;
}

.tag-credit {
    background: #f39c12;
    color: white;
}

.tag-secondary {
    background: #6c757d;
    color: white;
}

/* أنماط خيارات الدفع */
.payment-method-section {
    margin: 15px 0;
    padding: 15px;
    background: var(--bg-secondary);
    border-radius: 10px;
}

.payment-method-section h4 {
    margin-bottom: 10px;
    color: var(--text-primary);
    font-size: 14px;
}

.payment-options {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.payment-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 5px 0;
}

.payment-option input[type="radio"] {
    display: none;
}

.payment-option .tag {
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.6;
}

.payment-option input[type="radio"]:checked + .tag {
    opacity: 1;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.customer-selection {
    margin: 15px 0;
    padding: 15px;
    background: var(--bg-secondary);
    border-radius: 10px;
}

.customer-selection h4 {
    margin-bottom: 10px;
    color: var(--text-primary);
    font-size: 14px;
}

.customer-selection select {
    width: 100%;
}

/* أنماط نافذة تفاصيل العميل */
.customer-details-modal {
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.customer-details-content {
    padding: 20px;
}

.customer-info-header {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: 10px;
}

.customer-balance {
    text-align: center;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.customer-balance.debt {
    background: #ffe0e0;
    color: #e17055;
}

.customer-balance.credit {
    background: #e0f7e0;
    color: #00b894;
}

.customer-balance.neutral {
    background: var(--bg-secondary);
    color: var(--text-secondary);
}

.transactions-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--shadow-dark);
    border-radius: 10px;
    padding: 10px;
}

.transaction-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 10px;
    padding: 10px;
    margin-bottom: 10px;
    background: var(--bg-primary);
    border-radius: 8px;
    box-shadow: 2px 2px 4px var(--shadow-dark);
    align-items: center;
}

.transaction-type {
    font-weight: bold;
}

.transaction-type.sale {
    color: #74b9ff;
}

.transaction-type.payment {
    color: #00b894;
}

/* أنماط نافذة الدفعة */
.payment-modal {
    max-width: 400px;
}

.payment-form {
    padding: 20px;
}

/* أزرار العملاء */
.view-btn, .edit-btn, .delete-btn {
    padding: 5px 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    margin: 2px;
    transition: all 0.3s ease;
}

.view-btn {
    background: #74b9ff;
    color: white;
}

.edit-btn {
    background: #fdcb6e;
    color: white;
}

.delete-btn {
    background: #e17055;
    color: white;
}

.view-btn:hover, .edit-btn:hover, .delete-btn:hover {
    transform: translateY(-1px);
    opacity: 0.9;
}

/* أنماط صفحة الديون */
.debts-management {
    padding: 20px;
}

.debts-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: var(--bg-primary);
    padding: 20px;
    border-radius: 15px;
    box-shadow: 6px 6px 12px var(--shadow-dark), -6px -6px 12px var(--shadow-light);
    display: flex;
    align-items: center;
    gap: 15px;
}

.summary-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: var(--bg-secondary);
}

.debt-card .summary-icon {
    background: #ffe0e0;
}

.customers-card .summary-icon {
    background: #e0f0ff;
}

.payments-card .summary-icon {
    background: #e0f7e0;
}

.summary-info {
    flex: 1;
}

.summary-label {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.summary-value {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
}

.debts-filters {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: var(--bg-secondary);
    border-radius: 10px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: bold;
    color: var(--text-primary);
    white-space: nowrap;
}

.filter-group select {
    min-width: 150px;
}

.debts-list, .recent-payments {
    margin-bottom: 30px;
}

.debts-list h4, .recent-payments h4 {
    margin-bottom: 15px;
    color: var(--text-primary);
    border-bottom: 2px solid var(--accent-color);
    padding-bottom: 10px;
}

.debt-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: 15px;
    padding: 15px;
    margin-bottom: 10px;
    background: var(--bg-primary);
    border-radius: 10px;
    box-shadow: 4px 4px 8px var(--shadow-dark), -4px -4px 8px var(--shadow-light);
    align-items: center;
}

.debt-item.high-debt {
    border-right: 4px solid #e17055;
}

.debt-item.credit {
    border-right: 4px solid #00b894;
}

.debt-customer-info {
    display: flex;
    flex-direction: column;
}

.debt-customer-name {
    font-weight: bold;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.debt-customer-contact {
    font-size: 12px;
    color: var(--text-secondary);
}

.debt-balance {
    font-weight: bold;
    font-size: 16px;
}

.debt-balance.positive {
    color: #e17055;
}

.debt-balance.negative {
    color: #00b894;
}

.debt-last-transaction {
    font-size: 12px;
    color: var(--text-secondary);
}

.payment-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 15px;
    padding: 10px;
    margin-bottom: 8px;
    background: var(--bg-secondary);
    border-radius: 8px;
    align-items: center;
    font-size: 14px;
}

.payment-customer {
    font-weight: bold;
    color: var(--text-primary);
}

.payment-amount {
    font-weight: bold;
    color: #00b894;
}

.payment-date {
    color: var(--text-secondary);
    font-size: 12px;
}

.payment-notes {
    color: var(--text-secondary);
    font-size: 12px;
    font-style: italic;
}

/* أنماط زر تبديل الثيم */
.navbar-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.theme-toggle {
    background: var(--bg-primary);
    border: none;
    padding: 10px 14px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 18px;
    color: var(--text-primary);
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
    transition: all 0.3s ease;
    min-width: 45px;
    min-height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    transform: scale(1.1);
    box-shadow:
        6px 6px 12px var(--shadow-dark),
        -6px -6px 12px var(--shadow-light);
}

.theme-toggle:active {
    transform: scale(0.95);
    box-shadow:
        inset 2px 2px 4px var(--shadow-dark),
        inset -2px -2px 4px var(--shadow-light);
}

/* تحديث الأنماط للثيم الداكن */
body.dark-theme .neumorphic-input,
body.dark-theme .neumorphic-button {
    background: var(--bg-primary);
    color: var(--text-primary);
    box-shadow:
        9px 9px 16px var(--shadow-dark),
        -9px -9px 16px var(--shadow-light);
}

body.dark-theme .neumorphic-input:focus {
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

body.dark-theme .neumorphic-input::placeholder {
    color: var(--text-secondary);
    opacity: 0.8;
}

body.dark-theme .neumorphic-button:hover {
    box-shadow:
        6px 6px 12px var(--shadow-dark),
        -6px -6px 12px var(--shadow-light);
}

body.dark-theme .login-container,
body.dark-theme .main-app,
body.dark-theme .navbar,
body.dark-theme .products-grid,
body.dark-theme .cart-section,
body.dark-theme .form-container,
body.dark-theme .products-list {
    background: var(--bg-primary);
    color: var(--text-primary);
}

body.dark-theme .product-card,
body.dark-theme .cart-item,
body.dark-theme .product-item {
    background: var(--bg-primary);
    color: var(--text-primary);
    box-shadow:
        6px 6px 12px var(--shadow-dark),
        -6px -6px 12px var(--shadow-light);
}

/* نافذة حول البرنامج */
.about-modal {
    max-width: 500px;
    text-align: center;
}

.about-content {
    padding: 20px;
}

.about-logo {
    font-size: 48px;
    margin-bottom: 20px;
}

.about-content h2 {
    color: var(--accent-color);
    margin-bottom: 10px;
}

.about-content p {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

.about-features {
    text-align: right;
    margin: 20px 0;
    padding: 15px;
    background: var(--bg-secondary);
    border-radius: 10px;
}

.about-features h4 {
    color: var(--text-primary);
    margin-bottom: 10px;
}

.about-features ul {
    list-style: none;
    padding: 0;
}

.about-features li {
    padding: 5px 0;
    color: var(--text-secondary);
}

.about-features li:before {
    content: "✓ ";
    color: var(--success-color);
    font-weight: bold;
    margin-left: 5px;
}

.about-developer {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--shadow-dark);
}

.developer-link {
    display: inline-block;
    padding: 10px 20px;
    background: var(--accent-color);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.developer-link:hover {
    background: var(--info-color);
    transform: translateY(-2px);
}

/* حقوق الطبع */
.footer {
    text-align: center;
    padding: 15px;
    margin-top: 30px;
    color: var(--text-secondary);
    font-size: 12px;
    opacity: 0.7;
}

.footer a {
    color: var(--accent-color);
    text-decoration: none;
    font-size: 11px;
}

.footer a:hover {
    text-decoration: underline;
}

/* أنماط صفحة تسجيل الدخول المحدثة */
.login-subtitle {
    color: var(--text-secondary);
    font-size: 16px;
    margin-bottom: 25px;
    text-align: center;
}

.login-footer {
    margin-top: 20px;
    text-align: center;
    color: var(--text-secondary);
    font-size: 14px;
}

.login-footer strong {
    color: var(--accent-color);
    font-weight: bold;
}

/* أنماط صفحة حول البرنامج المحدثة */
.developer-name {
    font-size: 18px;
    font-weight: bold;
    color: var(--accent-color);
    margin: 15px 0;
    text-align: center;
}

.contact-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.contact-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 25px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
}

.contact-btn:hover {
    transform: translateY(-2px);
    box-shadow:
        6px 6px 12px var(--shadow-dark),
        -6px -6px 12px var(--shadow-light);
}

.whatsapp-btn {
    background: linear-gradient(135deg, #25D366, #128C7E);
    color: white;
}

.whatsapp-btn:hover {
    background: linear-gradient(135deg, #128C7E, #25D366);
}

.facebook-btn {
    background: linear-gradient(135deg, #1877f2, #42a5f5);
    color: white;
}

.facebook-btn:hover {
    background: linear-gradient(135deg, #42a5f5, #1877f2);
}

/* أنماط استيراد Excel */
.excel-import-section {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 15px;
}

.import-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.file-input {
    padding: 10px;
    border: 2px dashed var(--accent-color);
    border-radius: 10px;
    background: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-input:hover {
    border-color: var(--success-color);
    background: var(--bg-secondary);
}

.excel-data-container {
    display: none;
    margin-top: 20px;
}

/* تبويبات أوراق العمل */
.excel-sheet-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
    border-bottom: 2px solid var(--shadow-dark);
    padding-bottom: 10px;
    overflow-x: auto;
    white-space: nowrap;
}

.sheet-tab {
    background: var(--bg-secondary);
    border: 1px solid var(--shadow-dark);
    border-bottom: none;
    border-radius: 8px 8px 0 0;
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    text-align: center;
    position: relative;
    color: var(--text-primary);
}

.sheet-tab:hover {
    background: var(--accent-color);
    color: white;
    transform: translateY(-2px);
}

.sheet-tab.active {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.sheet-tab .sheet-name {
    display: block;
    font-weight: bold;
    font-size: 14px;
}

.sheet-tab .sheet-size {
    display: block;
    font-size: 11px;
    opacity: 0.8;
    margin-top: 2px;
}

/* معلومات الورقة */
.excel-sheet-info {
    background: var(--bg-secondary);
    padding: 10px 15px;
    border-radius: 6px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 4px solid var(--success-color);
    color: var(--text-primary);
}

.excel-sheet-info span {
    font-size: 14px;
}

#currentSheetName {
    font-weight: bold;
    color: var(--success-color);
}

.excel-table-wrapper {
    max-height: 70vh;
    overflow: auto;
    border: 1px solid var(--shadow-dark);
    border-radius: 10px;
    background: var(--bg-primary);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.excel-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
}

.excel-table th,
.excel-table td {
    border: 1px solid var(--shadow-dark);
    padding: 8px 12px;
    text-align: right;
    min-width: 120px;
    position: relative;
}

.excel-table th {
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 2px solid var(--success-color);
}

.excel-table td {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.excel-table .row-number {
    background: var(--bg-secondary) !important;
    text-align: center;
    font-weight: bold;
    color: var(--text-secondary);
    width: 50px;
    min-width: 50px;
    position: sticky;
    right: 0;
    z-index: 5;
}

.excel-table td[contenteditable="true"]:hover {
    background: var(--bg-secondary);
    outline: 2px solid var(--accent-color);
}

.excel-table td[contenteditable="true"]:focus {
    background: var(--bg-secondary);
    outline: 2px solid var(--success-color);
    box-shadow: inset 0 0 5px rgba(76, 175, 80, 0.3);
}

.excel-table tr:hover {
    background: var(--bg-secondary);
}

.excel-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

.delete-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.delete-btn:hover {
    background: #c0392b;
    transform: scale(1.05);
}

.danger {
    background: var(--danger-color);
    color: white;
}

.danger:hover {
    background: #c0392b;
}

/* أنماط متجاوبة لجدول Excel */
@media (max-width: 768px) {
    .import-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .excel-table-wrapper {
        max-height: 300px;
    }

    .excel-table th,
    .excel-table td {
        min-width: 80px;
        padding: 6px 8px;
        font-size: 12px;
    }

    .excel-actions {
        flex-direction: column;
    }
}

/* ===== أنماط إدارة الخامات والمنتجات ===== */

.materials-management,
.products-management {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
}

.materials-table,
.final-products-table {
    margin-top: 20px;
    background: var(--bg-primary);
    border-radius: 10px;
    padding: 15px;
    box-shadow: var(--shadow-neumorphic);
}

.materials-table .data-table,
.final-products-table .data-table {
    margin: 0;
}

.no-data {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: 8px;
    margin: 10px 0;
}

.btn-small.edit {
    background: var(--accent-color);
    color: white;
    margin-left: 5px;
}

.btn-small.edit:hover {
    background: #1976d2;
}

.btn-small.delete {
    background: var(--danger-color);
    color: white;
}

.btn-small.delete:hover {
    background: #c0392b;
}

/* تحسينات للنماذج */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.form-grid textarea {
    grid-column: 1 / -1;
    resize: vertical;
    min-height: 60px;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    flex-wrap: wrap;
}

/* أنماط الوصفات */
.recipe-summary {
    background: var(--bg-secondary);
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    border-left: 4px solid var(--success-color);
}

.total-cost {
    text-align: center;
    font-size: 16px;
    color: var(--success-color);
}

.insufficient-stock {
    background: rgba(231, 76, 60, 0.1) !important;
}

.insufficient-stock td {
    color: var(--danger-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
    font-weight: bold;
}

/* تحسينات للقوائم المنسدلة */
optgroup {
    font-weight: bold;
    color: var(--text-primary);
    background: var(--bg-secondary);
}

optgroup option {
    font-weight: normal;
    padding-left: 20px;
}

/* ===== أنماط المرتجعات ===== */

.returns-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 24px;
    margin-top: 20px;
    box-shadow:
        12px 12px 30px rgba(0, 0, 0, 0.1),
        -12px -12px 30px rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.returns-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--warning-gradient);
    border-radius: 24px 24px 0 0;
}

.returns-section h3 {
    color: transparent;
    background: var(--warning-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    font-size: 24px;
    font-weight: 800;
    text-align: center;
    margin-bottom: 30px;
}

.return-search {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 16px;
    margin-bottom: 25px;
    border-left: 4px solid var(--warning-color);
}

.return-search h4 {
    color: var(--warning-color);
    margin-bottom: 15px;
    font-weight: 700;
}

.invoice-details {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 16px;
    margin-bottom: 25px;
    border-left: 4px solid var(--info-color);
}

.invoice-details h4 {
    color: var(--info-color);
    margin-bottom: 15px;
    font-weight: 700;
}

.invoice-header {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    background: var(--bg-primary);
    padding: 15px;
    border-radius: 12px;
    margin-bottom: 20px;
}

.invoice-header div {
    padding: 8px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    text-align: center;
}

.return-items {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 16px;
    border-left: 4px solid var(--danger-color);
}

.return-items h4 {
    color: var(--danger-color);
    margin-bottom: 15px;
    font-weight: 700;
}

.return-summary {
    background: var(--bg-primary);
    padding: 20px;
    border-radius: 12px;
    margin: 20px 0;
    text-align: center;
    border: 2px solid var(--danger-color);
}

.return-total {
    font-size: 18px;
    color: var(--danger-color);
}

.return-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

/* أنماط متجاوبة للخامات والمنتجات */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .materials-table,
    .final-products-table {
        overflow-x: auto;
    }

    .materials-table .data-table,
    .final-products-table .data-table {
        min-width: 600px;
    }

    .form-actions,
    .return-actions {
        flex-direction: column;
    }

    .recipe-summary {
        margin-top: 10px;
        padding: 10px;
    }

    .invoice-header {
        grid-template-columns: 1fr;
    }

    .returns-section {
        padding: 20px;
    }
}

/* ===== أنماط الصفحات الجديدة ===== */

/* أنماط صفحة التصنيع */
.manufacturing-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.manufacturing-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 24px;
    margin-bottom: 30px;
    box-shadow:
        12px 12px 30px rgba(0, 0, 0, 0.1),
        -12px -12px 30px rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.manufacturing-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--success-gradient);
    border-radius: 24px 24px 0 0;
}

.manufacturing-section h4 {
    margin-bottom: 25px;
    color: transparent;
    background: var(--primary-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    font-size: 20px;
    font-weight: 800;
    border-bottom: 3px solid transparent;
    border-image: var(--primary-gradient) 1;
    padding-bottom: 12px;
    position: relative;
    text-align: center;
}

.manufacturing-section h4::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: 2px;
}

.recipe-form, .manufacturing-form {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 20px;
}

.ingredient-form {
    display: grid;
    grid-template-columns: 2fr 1fr auto;
    gap: 15px;
    align-items: center;
    margin-bottom: 15px;
}

.ingredients-list {
    background: var(--bg-primary);
    padding: 15px;
    border-radius: 10px;
    margin-top: 15px;
    box-shadow:
        inset 4px 4px 8px var(--shadow-dark),
        inset -4px -4px 8px var(--shadow-light);
}

.ingredients-table {
    width: 100%;
}

.ingredients-table .table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 15px;
    padding: 10px;
    background: var(--bg-secondary);
    border-radius: 8px;
    margin-bottom: 10px;
    font-weight: bold;
    color: var(--text-primary);
}

.ingredients-table .table-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 15px;
    padding: 10px;
    background: var(--bg-primary);
    border-radius: 8px;
    margin-bottom: 8px;
    align-items: center;
    box-shadow:
        2px 2px 4px var(--shadow-dark),
        -2px -2px 4px var(--shadow-light);
}

.ingredients-table .table-row.insufficient-stock {
    border: 2px solid var(--danger-color);
    background: #ffe0e0;
}

.recipes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.recipe-card {
    background: var(--bg-primary);
    padding: 20px;
    border-radius: 15px;
    box-shadow:
        6px 6px 12px var(--shadow-dark),
        -6px -6px 12px var(--shadow-light);
    transition: all 0.3s ease;
}

.recipe-card:hover {
    transform: translateY(-2px);
}

.recipe-card h5 {
    color: var(--text-primary);
    margin-bottom: 10px;
    font-size: 18px;
}

.recipe-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.manufacturing-preview {
    background: var(--bg-secondary);
    padding: 15px;
    border-radius: 10px;
    margin: 15px 0;
}

.manufacturing-preview-content h5 {
    color: var(--text-primary);
    margin-bottom: 10px;
}

.ingredient-preview {
    padding: 5px 10px;
    margin: 5px 0;
    border-radius: 5px;
    background: var(--bg-primary);
}

.ingredient-preview.insufficient {
    background: #ffe0e0;
    color: var(--danger-color);
}

.manufacturing-preview .warning {
    background: #fff3cd;
    color: #856404;
    padding: 10px;
    border-radius: 5px;
    margin-top: 10px;
    font-weight: bold;
}

.cost-summary {
    background: var(--bg-primary);
    padding: 15px;
    border-radius: 10px;
    margin-top: 15px;
    border: 2px solid var(--success-color);
}

.cost-summary h6 {
    color: var(--success-color);
    margin-bottom: 10px;
    font-weight: bold;
}

.cost-summary p {
    margin: 5px 0;
    color: var(--text-primary);
}

.cost-summary p strong {
    color: var(--accent-color);
}

.manufacturing-table {
    width: 100%;
    background: var(--bg-primary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
}

.manufacturing-table .table-header {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1fr 1fr auto;
    gap: 10px;
    padding: 15px;
    background: var(--bg-secondary);
    font-weight: bold;
    color: var(--text-primary);
    font-size: 14px;
}

.manufacturing-table .table-row {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1fr 1fr auto;
    gap: 10px;
    padding: 15px;
    border-bottom: 1px solid var(--shadow-dark);
    align-items: center;
    font-size: 14px;
}

.manufacturing-table .table-row:hover {
    background: var(--bg-secondary);
}

/* أنماط صفحة الهالك */
.wastage-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.wastage-section {
    background: var(--bg-primary);
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 25px;
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

.wastage-section h4 {
    margin-bottom: 20px;
    color: var(--text-primary);
    border-bottom: 2px solid var(--danger-color);
    padding-bottom: 10px;
}

.wastage-form {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 15px;
}

.filter-section {
    background: var(--bg-secondary);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.wastage-table {
    width: 100%;
    background: var(--bg-primary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
}

.wastage-table .table-header {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1fr 1fr auto;
    gap: 10px;
    padding: 15px;
    background: var(--bg-secondary);
    font-weight: bold;
    color: var(--text-primary);
    font-size: 14px;
}

.wastage-table .table-row {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1fr 1fr auto;
    gap: 10px;
    padding: 15px;
    border-bottom: 1px solid var(--shadow-dark);
    align-items: center;
    font-size: 14px;
}

.wastage-table .table-row:hover {
    background: var(--bg-secondary);
}

.wastage-report {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 15px;
}

.wastage-report-content {
    color: var(--text-primary);
}

.wastage-report .report-section {
    margin-bottom: 20px;
    padding: 15px;
    background: var(--bg-primary);
    border-radius: 10px;
}

.wastage-report .report-section h5 {
    color: var(--text-primary);
    margin-bottom: 10px;
    border-bottom: 1px solid var(--shadow-dark);
    padding-bottom: 5px;
}

.reason-stats, .product-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border-radius: 5px;
}

/* أنماط صفحة الموظفين */
.employees-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.employees-section {
    background: var(--bg-primary);
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 25px;
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

.employees-section h4 {
    margin-bottom: 20px;
    color: var(--text-primary);
    border-bottom: 2px solid var(--info-color);
    padding-bottom: 10px;
}

.employee-form {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 15px;
}

.employee-permissions {
    margin: 20px 0;
    padding: 15px;
    background: var(--bg-primary);
    border-radius: 10px;
}

.employee-permissions h5 {
    color: var(--text-primary);
    margin-bottom: 15px;
}

.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.permissions-grid label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: var(--text-primary);
}

.permissions-grid input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.employees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.employee-card {
    background: var(--bg-primary);
    padding: 20px;
    border-radius: 15px;
    box-shadow:
        6px 6px 12px var(--shadow-dark),
        -6px -6px 12px var(--shadow-light);
    transition: all 0.3s ease;
}

.employee-card:hover {
    transform: translateY(-2px);
}

.employee-card.inactive {
    opacity: 0.6;
    border: 2px solid var(--warning-color);
}

.employee-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.employee-header h5 {
    color: var(--text-primary);
    margin: 0;
}

.employee-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.employee-status.active {
    background: var(--success-color);
    color: white;
}

.employee-status.inactive {
    background: var(--warning-color);
    color: white;
}

.employee-info {
    margin-bottom: 15px;
}

.employee-info p {
    margin: 5px 0;
    color: var(--text-secondary);
    font-size: 14px;
}

.employee-permissions {
    margin-bottom: 15px;
    font-size: 14px;
    color: var(--text-secondary);
}

.employee-actions {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.toggle-btn {
    background: var(--warning-color);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.toggle-btn:hover {
    transform: translateY(-1px);
    opacity: 0.9;
}

/* أنماط صفحة الحضور والانصراف */
.attendance-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.attendance-section {
    background: var(--bg-primary);
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 25px;
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

.attendance-section h4 {
    margin-bottom: 20px;
    color: var(--text-primary);
    border-bottom: 2px solid var(--success-color);
    padding-bottom: 10px;
}

.attendance-form {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 15px;
}

.quick-actions {
    display: flex;
    gap: 15px;
    margin: 15px 0;
    justify-content: center;
}

.attendance-table {
    width: 100%;
    background: var(--bg-primary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
}

.attendance-table .table-header {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1fr 1fr auto;
    gap: 10px;
    padding: 15px;
    background: var(--bg-secondary);
    font-weight: bold;
    color: var(--text-primary);
    font-size: 14px;
}

.attendance-table .table-row {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr 1fr 1fr 1fr auto;
    gap: 10px;
    padding: 15px;
    border-bottom: 1px solid var(--shadow-dark);
    align-items: center;
    font-size: 14px;
}

.attendance-table .table-row:hover {
    background: var(--bg-secondary);
}

.attendance-table .table-row.late {
    border-right: 4px solid var(--warning-color);
}

.attendance-table .table-row.early {
    border-right: 4px solid var(--info-color);
}

.attendance-table .table-row.late-early {
    border-right: 4px solid var(--danger-color);
}

.work-hours-report {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 15px;
}

.work-hours-report-content h5 {
    color: var(--text-primary);
    margin-bottom: 20px;
    text-align: center;
}

.employees-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.employee-stat-card {
    background: var(--bg-primary);
    padding: 20px;
    border-radius: 15px;
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
}

.employee-stat-card h6 {
    color: var(--text-primary);
    margin-bottom: 15px;
    text-align: center;
    border-bottom: 1px solid var(--shadow-dark);
    padding-bottom: 10px;
}

.stat-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.stat-grid .stat-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border-radius: 5px;
    font-size: 14px;
}

/* أنماط صفحة صافي الربح */
.profit-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.profit-section {
    background: var(--bg-primary);
    padding: 25px;
    border-radius: 20px;
    margin-bottom: 25px;
    box-shadow:
        8px 8px 16px var(--shadow-dark),
        -8px -8px 16px var(--shadow-light);
}

.profit-section h4 {
    margin-bottom: 20px;
    color: var(--text-primary);
    border-bottom: 2px solid var(--accent-color);
    padding-bottom: 10px;
}

.profit-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.profit-card {
    background: var(--bg-primary);
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow:
        6px 6px 12px var(--shadow-dark),
        -6px -6px 12px var(--shadow-light);
    transition: all 0.3s ease;
}

.profit-card:hover {
    transform: translateY(-2px);
}

.profit-card h5 {
    color: var(--text-secondary);
    margin-bottom: 10px;
    font-size: 14px;
}

.profit-amount {
    font-size: 20px;
    font-weight: bold;
    color: var(--text-primary);
}

.profit-card.net-profit {
    border: 3px solid var(--accent-color);
}

.profit-card.profit .profit-amount {
    color: var(--success-color);
}

.profit-card.loss .profit-amount {
    color: var(--danger-color);
}

.profit-details {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 15px;
}

.profit-details-content {
    color: var(--text-primary);
}

.profit-formula {
    background: var(--bg-primary);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
}

.profit-formula h5 {
    color: var(--text-primary);
    margin-bottom: 15px;
}

.formula-text {
    font-size: 16px;
    font-weight: bold;
    color: var(--accent-color);
    margin-bottom: 10px;
}

.formula-calculation {
    font-size: 14px;
    color: var(--text-secondary);
    font-family: monospace;
    background: var(--bg-secondary);
    padding: 10px;
    border-radius: 5px;
}

.detail-section {
    margin-bottom: 20px;
    padding: 15px;
    background: var(--bg-primary);
    border-radius: 10px;
}

.detail-section h6 {
    color: var(--text-primary);
    margin-bottom: 10px;
    border-bottom: 1px solid var(--shadow-dark);
    padding-bottom: 5px;
}

.detail-section p {
    margin: 5px 0;
    color: var(--text-secondary);
}

/* أنماط عامة للجداول */
.table-header {
    font-weight: bold;
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.table-row {
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.table-row:hover {
    background: var(--bg-secondary);
}

/* أنماط الأزرار الصغيرة */
.view-btn, .edit-btn, .delete-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
    margin: 2px;
}

.view-btn {
    background: var(--info-color);
    color: white;
}

.edit-btn {
    background: var(--warning-color);
    color: white;
}

.delete-btn {
    background: var(--danger-color);
    color: white;
}

.view-btn:hover, .edit-btn:hover, .delete-btn:hover {
    transform: translateY(-1px);
    opacity: 0.9;
}

/* أنماط متجاوبة للصفحات الجديدة */
@media (max-width: 768px) {
    .manufacturing-container,
    .wastage-container,
    .employees-container,
    .attendance-container,
    .profit-container {
        padding: 10px;
    }

    .ingredient-form {
        grid-template-columns: 1fr;
    }

    .ingredients-table .table-header,
    .ingredients-table .table-row {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .wastage-table .table-header,
    .wastage-table .table-row {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .attendance-table .table-header,
    .attendance-table .table-row {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .manufacturing-table .table-header,
    .manufacturing-table .table-row {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .recipes-grid,
    .employees-grid {
        grid-template-columns: 1fr;
    }

    .profit-summary {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .employees-stats {
        grid-template-columns: 1fr;
    }

    .stat-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        flex-direction: column;
        align-items: center;
    }

    .permissions-grid {
        grid-template-columns: 1fr;
    }
}

/* أنماط المرتجعات */
.return-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: var(--bg-secondary);
    border-radius: 10px;
    border: 1px solid var(--shadow-dark);
}

.return-item-info {
    flex: 1;
    color: var(--text-primary);
}

.return-item-info strong {
    font-size: 16px;
    margin-bottom: 5px;
    display: block;
}

.return-item-info span {
    font-size: 14px;
    color: var(--text-secondary);
    display: block;
    margin: 2px 0;
}

.return-item-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.return-item-controls label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    color: var(--text-primary);
    font-weight: bold;
}

.return-item-controls input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.return-reason, .return-notes, .return-employee {
    margin: 15px 0;
}

.return-reason label, .return-notes label, .return-employee label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: bold;
}

.sale-info {
    background: var(--bg-secondary);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.sale-info p {
    margin: 5px 0;
    color: var(--text-primary);
}

.return-items-list {
    max-height: 300px;
    overflow-y: auto;
    margin: 15px 0;
    padding: 10px;
    background: var(--bg-primary);
    border-radius: 10px;
    border: 1px solid var(--shadow-dark);
}

.returns-table {
    width: 100%;
    background: var(--bg-primary);
    border-radius: 10px;
    overflow: hidden;
    box-shadow:
        4px 4px 8px var(--shadow-dark),
        -4px -4px 8px var(--shadow-light);
}

.returns-table .table-header {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr 1fr 1fr auto;
    gap: 10px;
    padding: 15px;
    background: var(--bg-secondary);
    font-weight: bold;
    color: var(--text-primary);
    font-size: 14px;
}

.returns-table .table-row {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr 1fr 1fr auto;
    gap: 10px;
    padding: 15px;
    border-bottom: 1px solid var(--shadow-dark);
    align-items: center;
    font-size: 14px;
}

.returns-table .table-row:hover {
    background: var(--bg-secondary);
}

/* أنماط متجاوبة للمرتجعات */
@media (max-width: 768px) {
    .return-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .return-item-controls {
        flex-direction: row;
        justify-content: center;
    }

    .returns-table .table-header,
    .returns-table .table-row {
        grid-template-columns: 1fr;
        text-align: center;
    }
}
