// ===== نظام التقارير والإحصائيات =====

// وظائف مساعدة للتواريخ
function isToday(date) {
    const today = new Date();
    const checkDate = new Date(date);
    return checkDate.toDateString() === today.toDateString();
}

function isThisWeek(date) {
    const today = new Date();
    const checkDate = new Date(date);
    const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
    const weekEnd = new Date(today.setDate(today.getDate() - today.getDay() + 6));
    return checkDate >= weekStart && checkDate <= weekEnd;
}

function isThisMonth(date) {
    const today = new Date();
    const checkDate = new Date(date);
    return checkDate.getMonth() === today.getMonth() && checkDate.getFullYear() === today.getFullYear();
}

function isThisYear(date) {
    const today = new Date();
    const checkDate = new Date(date);
    return checkDate.getFullYear() === today.getFullYear();
}

// ===== تقارير المشتريات =====

function getPurchasesToday() {
    return purchases.filter(p => isToday(p.date))
        .reduce((sum, p) => sum + p.totalAmount, 0);
}

function getPurchasesThisWeek() {
    return purchases.filter(p => isThisWeek(p.date))
        .reduce((sum, p) => sum + p.totalAmount, 0);
}

function getPurchasesThisMonth() {
    return purchases.filter(p => isThisMonth(p.date))
        .reduce((sum, p) => sum + p.totalAmount, 0);
}

function getTotalPurchases() {
    return purchases.reduce((sum, p) => sum + p.totalAmount, 0);
}

// ===== تقارير المبيعات =====

function getSalesToday() {
    return sales.filter(s => isToday(s.date))
        .reduce((sum, s) => sum + s.totalAmount, 0);
}

function getSalesThisWeek() {
    return sales.filter(s => isThisWeek(s.date))
        .reduce((sum, s) => sum + s.totalAmount, 0);
}

function getSalesThisMonth() {
    return sales.filter(s => isThisMonth(s.date))
        .reduce((sum, s) => sum + s.totalAmount, 0);
}

function getTotalSales() {
    return sales.reduce((sum, s) => sum + s.totalAmount, 0);
}

// ===== تقارير المصروفات =====

function getExpensesToday() {
    return expenses.filter(e => isToday(e.date))
        .reduce((sum, e) => sum + e.amount, 0);
}

function getExpensesThisWeek() {
    return expenses.filter(e => isThisWeek(e.date))
        .reduce((sum, e) => sum + e.amount, 0);
}

function getExpensesThisMonth() {
    return expenses.filter(e => isThisMonth(e.date))
        .reduce((sum, e) => sum + e.amount, 0);
}

function getTotalExpenses() {
    return expenses.reduce((sum, e) => sum + e.amount, 0);
}

// ===== تقارير حركة الخزنة =====

function getCashReceiptsToday() {
    return cashVouchers.filter(cv => cv.type === 'receipt' && isToday(cv.date))
        .reduce((sum, cv) => sum + cv.amount, 0);
}

function getCashReceiptsThisWeek() {
    return cashVouchers.filter(cv => cv.type === 'receipt' && isThisWeek(cv.date))
        .reduce((sum, cv) => sum + cv.amount, 0);
}

function getCashReceiptsThisMonth() {
    return cashVouchers.filter(cv => cv.type === 'receipt' && isThisMonth(cv.date))
        .reduce((sum, cv) => sum + cv.amount, 0);
}

function getCashReceiptsThisYear() {
    return cashVouchers.filter(cv => cv.type === 'receipt' && isThisYear(cv.date))
        .reduce((sum, cv) => sum + cv.amount, 0);
}

function getTotalCashReceipts() {
    return cashVouchers.filter(cv => cv.type === 'receipt')
        .reduce((sum, cv) => sum + cv.amount, 0);
}

function getCashPaymentsToday() {
    return cashVouchers.filter(cv => cv.type === 'payment' && isToday(cv.date))
        .reduce((sum, cv) => sum + cv.amount, 0);
}

function getCashPaymentsThisWeek() {
    return cashVouchers.filter(cv => cv.type === 'payment' && isThisWeek(cv.date))
        .reduce((sum, cv) => sum + cv.amount, 0);
}

function getCashPaymentsThisMonth() {
    return cashVouchers.filter(cv => cv.type === 'payment' && isThisMonth(cv.date))
        .reduce((sum, cv) => sum + cv.amount, 0);
}

function getCashPaymentsThisYear() {
    return cashVouchers.filter(cv => cv.type === 'payment' && isThisYear(cv.date))
        .reduce((sum, cv) => sum + cv.amount, 0);
}

function getTotalCashPayments() {
    return cashVouchers.filter(cv => cv.type === 'payment')
        .reduce((sum, cv) => sum + cv.amount, 0);
}

// ===== تقارير أفضل المنتجات =====

function getTop10ProductsBySales() {
    // حساب كمية المبيعات لكل منتج
    const productSales = {};
    
    saleItems.forEach(item => {
        if (productSales[item.productId]) {
            productSales[item.productId] += item.quantity;
        } else {
            productSales[item.productId] = item.quantity;
        }
    });
    
    // ترتيب المنتجات حسب الكمية المباعة
    const sortedProducts = Object.entries(productSales)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([productId, quantity]) => {
            const product = products.find(p => p.id === productId);
            return {
                product: product,
                quantity: quantity
            };
        });
    
    return sortedProducts;
}

// ===== تقارير أكثر المديونين =====

function getTop10Debtors() {
    return customers
        .filter(c => c.currentBalance > 0)
        .sort((a, b) => b.currentBalance - a.currentBalance)
        .slice(0, 10)
        .map(customer => ({
            name: customer.name,
            balance: customer.currentBalance
        }));
}

// ===== تقرير شامل للوحة المعلومات =====

function getDashboardReport() {
    return {
        purchases: {
            today: getPurchasesToday(),
            week: getPurchasesThisWeek(),
            month: getPurchasesThisMonth(),
            total: getTotalPurchases()
        },
        sales: {
            today: getSalesToday(),
            week: getSalesThisWeek(),
            month: getSalesThisMonth(),
            total: getTotalSales()
        },
        expenses: {
            today: getExpensesToday(),
            week: getExpensesThisWeek(),
            month: getExpensesThisMonth(),
            total: getTotalExpenses()
        },
        cashReceipts: {
            today: getCashReceiptsToday(),
            week: getCashReceiptsThisWeek(),
            month: getCashReceiptsThisMonth(),
            year: getCashReceiptsThisYear(),
            total: getTotalCashReceipts()
        },
        cashPayments: {
            today: getCashPaymentsToday(),
            week: getCashPaymentsThisWeek(),
            month: getCashPaymentsThisMonth(),
            year: getCashPaymentsThisYear(),
            total: getTotalCashPayments()
        },
        topProducts: getTop10ProductsBySales(),
        topDebtors: getTop10Debtors()
    };
}

// ===== تقارير مخصصة بفترة زمنية =====

function getCustomPeriodReport(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    const filteredPurchases = purchases.filter(p => {
        const date = new Date(p.date);
        return date >= start && date <= end;
    });
    
    const filteredSales = sales.filter(s => {
        const date = new Date(s.date);
        return date >= start && date <= end;
    });
    
    const filteredExpenses = expenses.filter(e => {
        const date = new Date(e.date);
        return date >= start && date <= end;
    });
    
    return {
        period: { start: startDate, end: endDate },
        purchases: filteredPurchases.reduce((sum, p) => sum + p.totalAmount, 0),
        sales: filteredSales.reduce((sum, s) => sum + s.totalAmount, 0),
        expenses: filteredExpenses.reduce((sum, e) => sum + e.amount, 0),
        profit: filteredSales.reduce((sum, s) => sum + s.totalAmount, 0) - 
                filteredPurchases.reduce((sum, p) => sum + p.totalAmount, 0) - 
                filteredExpenses.reduce((sum, e) => sum + e.amount, 0)
    };
}
