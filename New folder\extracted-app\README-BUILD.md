# دليل تحويل نظام إدارة نقاط البيع إلى برنامج سطح مكتب

## 📋 المتطلبات الأساسية

### 1. تثبيت Node.js
- قم بتحميل Node.js من: https://nodejs.org
- اختر النسخة LTS (الموصى بها)
- تأكد من تثبيت npm معه

### 2. التحقق من التثبيت
افتح Command Prompt أو PowerShell وتأكد من:
```bash
node --version
npm --version
```

## 🚀 خطوات البناء

### الطريقة الأولى: استخدام الملفات الجاهزة

1. **تثبيت التبعيات:**
   - انقر نقراً مزدوجاً على `start-dev.bat`
   - أو افتح Command Prompt في مجلد المشروع واكتب:
   ```bash
   npm install
   ```

2. **اختبار التطبيق:**
   ```bash
   npm start
   ```

3. **بناء ملف exe:**
   - انقر نقراً مزدوجاً على `build-exe.bat`
   - أو اكتب في Command Prompt:
   ```bash
   npm run build-win
   ```

### الطريقة الثانية: الخطوات اليدوية

1. **فتح Command Prompt في مجلد المشروع**

2. **تثبيت Electron:**
   ```bash
   npm install electron --save-dev
   npm install electron-builder --save-dev
   ```

3. **تشغيل التطبيق للاختبار:**
   ```bash
   npx electron .
   ```

4. **بناء التطبيق:**
   ```bash
   npx electron-builder --win
   ```

## 📁 هيكل الملفات

```
المشروع/
├── package.json          # إعدادات المشروع
├── electron-main.js      # الملف الرئيسي لـ Electron
├── preload.js           # ملف الأمان
├── index.html           # واجهة التطبيق
├── main.js              # منطق التطبيق
├── app.js               # وظائف التطبيق
├── style.css            # أنماط التطبيق
├── assets/              # مجلد الأيقونات
├── dist/                # مجلد النتائج (يُنشأ تلقائياً)
├── start-dev.bat        # ملف تشغيل التطوير
└── build-exe.bat        # ملف البناء
```

## 🎯 النتائج المتوقعة

بعد البناء الناجح، ستجد في مجلد `dist`:

### Windows:
- `نظام إدارة نقاط البيع - تكنوفلاش Setup.exe` - ملف التثبيت
- مجلد `win-unpacked/` - النسخة المحمولة

### الميزات المضافة:
- ✅ قائمة تطبيق احترافية
- ✅ اختصارات لوحة المفاتيح
- ✅ تصدير/استيراد البيانات من القائمة
- ✅ حفظ البيانات محلياً
- ✅ واجهة سطح مكتب كاملة

## 🔧 حل المشاكل الشائعة

### مشكلة: "node is not recognized"
**الحل:** تأكد من تثبيت Node.js وإعادة تشغيل Command Prompt

### مشكلة: "npm install fails"
**الحل:** 
```bash
npm cache clean --force
npm install
```

### مشكلة: "electron-builder fails"
**الحل:**
```bash
npm install electron-builder --save-dev --force
```

### مشكلة: الأيقونة لا تظهر
**الحل:** أضف ملفات الأيقونات في مجلد `assets/`:
- `icon.ico` للـ Windows
- `icon.png` للـ Linux

## 📱 إنشاء أيقونة مخصصة

1. أنشئ صورة PNG بحجم 512x512 بكسل
2. استخدم أدوات التحويل مثل:
   - https://convertio.co/png-ico/
   - https://icoconvert.com/

3. احفظ الأيقونات في مجلد `assets/`

## 🌟 ميزات إضافية

### تخصيص التطبيق:
- عدّل `package.json` لتغيير اسم التطبيق
- عدّل `electron-main.js` لتخصيص النافذة
- أضف المزيد من القوائم والاختصارات

### بناء لأنظمة أخرى:
```bash
npm run build-mac    # لـ macOS
npm run build-linux  # لـ Linux
```

## 📞 الدعم

في حالة وجود مشاكل:
1. تأكد من تثبيت Node.js بشكل صحيح
2. تأكد من وجود اتصال بالإنترنت لتحميل التبعيات
3. تشغيل Command Prompt كمدير إذا لزم الأمر

**تم تطوير هذا النظام بواسطة**
👨‍💻 Eng / Hossam Osama

📱 تواصل مباشر على واتساب:
📲 اضغط هنا للتواصل
📞 01225396729

📘 تابع صفحتنا على فيسبوك:
👉 H - TECH


