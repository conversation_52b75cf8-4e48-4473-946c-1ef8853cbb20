// ===== الخزينة =====

const treasury = (() => {
    if (!window.cashboxes) window.cashboxes = [];
    if (!window.cashTransactions) window.cashTransactions = [];

    function showAddCashbox(){
        const c = document.getElementById('treasuryContent');
        c.innerHTML = `
            <div class="form-container">
                <h4>إضافة خزينة</h4>
                <div class="form-grid">
                    <input id="tr_name" class="neumorphic-input" placeholder="اسم الخزينة (رئيسية/فرعية)">
                    <input id="tr_opening" type="number" step="0.01" class="neumorphic-input" placeholder="الرصيد الافتتاحي">
                </div>
                <div class="form-actions">
                    <button onclick="treasury.saveCashbox()" class="neumorphic-button primary">حفظ</button>
                </div>
            </div>`;
    }

    function saveCashbox(){
        window.cashboxes.push({ id: generateId(), name: document.getElementById('tr_name').value.trim(), balance: safeNumber(parseFloat(document.getElementById('tr_opening').value),0) });
        saveData();
        if (typeof onDataChange === 'function') { try { onDataChange(); } catch(e) { console.error(e); } }
        showNotification('تم إضافة الخزينة', 'success');
        list();
    }

    function showCashIn(){
        const c = document.getElementById('treasuryContent');
        c.innerHTML = `
            <div class="form-container">
                <h4>تسجيل إيراد</h4>
                <div class="form-grid">
                    <select id="tr_box" class="neumorphic-input">${(cashboxes||[]).map(b=>`<option value="${b.id}">${b.name}</option>`).join('')}</select>
                    <input id="tr_amount" type="number" step="0.01" class="neumorphic-input" placeholder="المبلغ">
                    <input id="tr_desc" class="neumorphic-input" placeholder="الوصف">
                </div>
                <div class="form-actions">
                    <button onclick="treasury.saveCashIn()" class="neumorphic-button primary">حفظ</button>
                </div>
            </div>`;
    }

    function showCashOut(){
        const c = document.getElementById('treasuryContent');
        c.innerHTML = `
            <div class="form-container">
                <h4>تسجيل مصروف</h4>
                <div class="form-grid">
                    <select id="tr_box" class="neumorphic-input">${(cashboxes||[]).map(b=>`<option value="${b.id}">${b.name}</option>`).join('')}</select>
                    <input id="tr_amount" type="number" step="0.01" class="neumorphic-input" placeholder="المبلغ">
                    <input id="tr_desc" class="neumorphic-input" placeholder="الوصف">
                </div>
                <div class="form-actions">
                    <button onclick="treasury.saveCashOut()" class="neumorphic-button primary">حفظ</button>
                </div>
            </div>`;
    }

    function saveCashIn(){
        const boxId = document.getElementById('tr_box').value; const amount = safeNumber(parseFloat(document.getElementById('tr_amount').value),0); const desc = document.getElementById('tr_desc').value.trim();
        const box = cashboxes.find(b=>b.id===boxId); if(!box){ showNotification('اختر خزينة', 'error'); return; }
        box.balance += amount; cashTransactions.push({id:generateId(), boxId, type:'in', amount, desc, date:new Date().toISOString()}); saveData(); if (typeof onDataChange === 'function') { try { onDataChange(); } catch(e) { console.error(e); } } showNotification('تم تسجيل الإيراد', 'success'); list();
    }

    function saveCashOut(){
        const boxId = document.getElementById('tr_box').value; const amount = safeNumber(parseFloat(document.getElementById('tr_amount').value),0); const desc = document.getElementById('tr_desc').value.trim();
        const box = cashboxes.find(b=>b.id===boxId); if(!box){ showNotification('اختر خزينة', 'error'); return; }
        box.balance -= amount; cashTransactions.push({id:generateId(), boxId, type:'out', amount, desc, date:new Date().toISOString()}); saveData(); if (typeof onDataChange === 'function') { try { onDataChange(); } catch(e) { console.error(e); } } showNotification('تم تسجيل المصروف', 'success'); list();
    }

    function list(){
        const c = document.getElementById('treasuryContent');
        c.innerHTML = `
            <h4>الخزائن</h4>
            <div>${(cashboxes||[]).map(b=>`<div class="summary-card">${b.name}: ${formatCurrency(b.balance)}</div>`).join('') || '<div style="color:#666;">لا توجد خزائن</div>'}</div>
            <h4 style="margin-top:20px;">حركة اليوم</h4>
            <div>${(cashTransactions||[]).slice(-20).reverse().map(t=>`<div class="transaction-item">${formatDateShort(t.date)} - ${t.type==='in'?'+':'-'}${formatCurrency(t.amount)} - ${t.desc||''}</div>`).join('') || '<div style="color:#666;">لا توجد حركات</div>'}</div>
        `;
    }

    function showReports(){
        const c = document.getElementById('treasuryContent');
        const balance = (cashboxes||[]).reduce((s,b)=> s+b.balance, 0);
        c.innerHTML = `<div class="report-summary">الرصيد الحالي: ${formatCurrency(balance)}</div>`;
    }

    return { showAddCashbox, saveCashbox, showCashIn, showCashOut, saveCashIn, saveCashOut, list, showReports };
})();


