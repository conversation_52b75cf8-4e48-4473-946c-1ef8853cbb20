// ===== إدارة الموظفين =====

let editingEmployeeId = null;

// تهيئة صفحة الموظفين
function initializeEmployees() {
    displayEmployeesList();
    
    // تعيين التاريخ الحالي لتاريخ التعيين
    document.getElementById('employeeHireDate').value = new Date().toISOString().split('T')[0];
}

// حفظ الموظف
function saveEmployee() {
    const name = document.getElementById('employeeName').value.trim();
    const phone = document.getElementById('employeePhone').value.trim();
    const position = document.getElementById('employeePosition').value.trim();
    const salary = parseFloat(document.getElementById('employeeSalary').value);
    const hireDate = document.getElementById('employeeHireDate').value;
    const status = document.getElementById('employeeStatus').value;
    const notes = document.getElementById('employeeNotes').value.trim();
    
    // التحقق من صحة البيانات
    if (!name || !phone || !position || !salary || salary < 0 || !hireDate) {
        showNotification('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }
    
    // التحقق من عدم تكرار رقم الهاتف
    const existingEmployee = employees.find(e => e.phone === phone && e.id !== editingEmployeeId);
    if (existingEmployee) {
        showNotification('رقم الهاتف مستخدم بالفعل', 'error');
        return;
    }
    
    // جمع الصلاحيات
    const permissions = {
        sales: document.getElementById('permSales').checked,
        purchases: document.getElementById('permPurchases').checked,
        products: document.getElementById('permProducts').checked,
        manufacturing: document.getElementById('permManufacturing').checked,
        wastage: document.getElementById('permWastage').checked,
        reports: document.getElementById('permReports').checked,
        settings: document.getElementById('permSettings').checked
    };
    
    const employee = {
        id: editingEmployeeId || generateId(),
        name: name,
        phone: phone,
        position: position,
        salary: salary,
        hireDate: hireDate,
        status: status,
        notes: notes,
        permissions: permissions,
        createdAt: editingEmployeeId ? employees.find(e => e.id === editingEmployeeId).createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    
    if (editingEmployeeId) {
        const index = employees.findIndex(e => e.id === editingEmployeeId);
        employees[index] = employee;
        showNotification('تم تحديث بيانات الموظف بنجاح', 'success');
    } else {
        employees.push(employee);
        showNotification('تم إضافة الموظف بنجاح', 'success');
    }
    
    saveData();
    clearEmployeeForm();
    displayEmployeesList();
    
    // تحديث قوائم الموظفين في الصفحات الأخرى
    updateEmployeeSelects();
}

// مسح نموذج الموظف
function clearEmployeeForm() {
    document.getElementById('employeeName').value = '';
    document.getElementById('employeePhone').value = '';
    document.getElementById('employeePosition').value = '';
    document.getElementById('employeeSalary').value = '';
    document.getElementById('employeeHireDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('employeeStatus').value = 'active';
    document.getElementById('employeeNotes').value = '';
    
    // مسح الصلاحيات
    document.getElementById('permSales').checked = false;
    document.getElementById('permPurchases').checked = false;
    document.getElementById('permProducts').checked = false;
    document.getElementById('permManufacturing').checked = false;
    document.getElementById('permWastage').checked = false;
    document.getElementById('permReports').checked = false;
    document.getElementById('permSettings').checked = false;
    
    editingEmployeeId = null;
}

// عرض قائمة الموظفين
function displayEmployeesList() {
    const container = document.getElementById('employeesList');
    
    if (employees.length === 0) {
        container.innerHTML = '<p>لا يوجد موظفين مسجلين</p>';
        return;
    }
    
    let html = '<div class="employees-grid">';
    
    employees.forEach(employee => {
        const statusClass = employee.status === 'active' ? 'active' : 'inactive';
        const statusText = employee.status === 'active' ? 'نشط' : 'غير نشط';
        
        html += `<div class="employee-card ${statusClass}">`;
        html += `<div class="employee-header">`;
        html += `<h5>${employee.name}</h5>`;
        html += `<span class="employee-status ${statusClass}">${statusText}</span>`;
        html += `</div>`;
        html += `<div class="employee-info">`;
        html += `<p><strong>الوظيفة:</strong> ${employee.position}</p>`;
        html += `<p><strong>الهاتف:</strong> ${employee.phone}</p>`;
        html += `<p><strong>المرتب:</strong> ${employee.salary.toFixed(2)} ${settings.currencySymbol}</p>`;
        html += `<p><strong>تاريخ التعيين:</strong> ${new Date(employee.hireDate).toLocaleDateString('ar-SA')}</p>`;
        html += `</div>`;
        
        // عرض الصلاحيات
        html += `<div class="employee-permissions">`;
        html += `<strong>الصلاحيات:</strong> `;
        const activePermissions = Object.entries(employee.permissions)
            .filter(([key, value]) => value)
            .map(([key, value]) => getPermissionName(key));
        html += activePermissions.length > 0 ? activePermissions.join(', ') : 'لا توجد صلاحيات';
        html += `</div>`;
        
        html += `<div class="employee-actions">`;
        html += `<button onclick="editEmployee('${employee.id}')" class="edit-btn">تعديل</button>`;
        html += `<button onclick="toggleEmployeeStatus('${employee.id}')" class="toggle-btn">`;
        html += employee.status === 'active' ? 'إيقاف' : 'تفعيل';
        html += `</button>`;
        html += `<button onclick="deleteEmployee('${employee.id}')" class="delete-btn">حذف</button>`;
        html += `</div>`;
        html += `</div>`;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// الحصول على اسم الصلاحية بالعربية
function getPermissionName(permission) {
    const permissionNames = {
        sales: 'المبيعات',
        purchases: 'المشتريات',
        products: 'الأصناف',
        manufacturing: 'التصنيع',
        wastage: 'الهالك',
        reports: 'التقارير',
        settings: 'الإعدادات'
    };
    return permissionNames[permission] || permission;
}

// تعديل الموظف
function editEmployee(employeeId) {
    const employee = employees.find(e => e.id === employeeId);
    if (!employee) return;
    
    editingEmployeeId = employeeId;
    
    document.getElementById('employeeName').value = employee.name;
    document.getElementById('employeePhone').value = employee.phone;
    document.getElementById('employeePosition').value = employee.position;
    document.getElementById('employeeSalary').value = employee.salary;
    document.getElementById('employeeHireDate').value = employee.hireDate;
    document.getElementById('employeeStatus').value = employee.status;
    document.getElementById('employeeNotes').value = employee.notes || '';
    
    // تحديد الصلاحيات
    document.getElementById('permSales').checked = employee.permissions.sales || false;
    document.getElementById('permPurchases').checked = employee.permissions.purchases || false;
    document.getElementById('permProducts').checked = employee.permissions.products || false;
    document.getElementById('permManufacturing').checked = employee.permissions.manufacturing || false;
    document.getElementById('permWastage').checked = employee.permissions.wastage || false;
    document.getElementById('permReports').checked = employee.permissions.reports || false;
    document.getElementById('permSettings').checked = employee.permissions.settings || false;
    
    showNotification('تم تحميل بيانات الموظف للتعديل', 'info');
}

// تبديل حالة الموظف (نشط/غير نشط)
function toggleEmployeeStatus(employeeId) {
    const employee = employees.find(e => e.id === employeeId);
    if (!employee) return;
    
    const newStatus = employee.status === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? 'تفعيل' : 'إيقاف';
    
    if (!confirm(`هل أنت متأكد من ${action} هذا الموظف؟`)) return;
    
    employee.status = newStatus;
    employee.updatedAt = new Date().toISOString();
    
    saveData();
    displayEmployeesList();
    updateEmployeeSelects();
    
    showNotification(`تم ${action} الموظف بنجاح`, 'success');
}

// حذف الموظف
function deleteEmployee(employeeId) {
    if (!confirm('هل أنت متأكد من حذف هذا الموظف؟\nسيتم حذف جميع سجلات الحضور والانصراف المرتبطة به.')) return;
    
    const index = employees.findIndex(e => e.id === employeeId);
    if (index !== -1) {
        // حذف سجلات الحضور والانصراف المرتبطة
        attendance = attendance.filter(a => a.employeeId !== employeeId);
        
        // حذف الموظف
        employees.splice(index, 1);
        
        saveData();
        displayEmployeesList();
        updateEmployeeSelects();
        
        showNotification('تم حذف الموظف وجميع سجلاته بنجاح', 'success');
    }
}

// تحديث قوائم الموظفين في جميع الصفحات
function updateEmployeeSelects() {
    // تحديث قائمة الموظفين في صفحة التصنيع
    const manufacturingSelect = document.getElementById('manufacturingEmployee');
    if (manufacturingSelect) {
        loadEmployeesToSelect('manufacturingEmployee');
    }
    
    // تحديث قائمة الموظفين في صفحة الهالك
    const wastageSelect = document.getElementById('wastageEmployee');
    if (wastageSelect) {
        loadEmployeesToWastageSelect();
    }
    
    // تحديث قائمة الموظفين في صفحة الحضور والانصراف
    const attendanceSelect = document.getElementById('attendanceEmployee');
    if (attendanceSelect) {
        loadEmployeesToAttendanceSelect();
    }
    
    const attendanceFilterSelect = document.getElementById('attendanceFilterEmployee');
    if (attendanceFilterSelect) {
        loadEmployeesToAttendanceSelect('attendanceFilterEmployee');
    }
}

// حساب إجمالي مرتبات الموظفين النشطين
function calculateTotalSalaries() {
    return employees
        .filter(e => e.status === 'active')
        .reduce((sum, e) => sum + e.salary, 0);
}

// الحصول على الموظفين النشطين
function getActiveEmployees() {
    return employees.filter(e => e.status === 'active');
}

// البحث عن موظف بالاسم أو الهاتف
function findEmployee(searchTerm) {
    return employees.find(e => 
        e.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        e.phone.includes(searchTerm)
    );
}
