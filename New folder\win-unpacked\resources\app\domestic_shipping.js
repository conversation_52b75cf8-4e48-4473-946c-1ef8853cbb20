// ===== الشحن الداخلي =====

const domesticShipping = (() => {
    function showCreateForm() {
        const container = document.getElementById('domesticShippingContent');
        container.innerHTML = `
            <div class="form-container">
                <h4>إضافة نقل داخلي</h4>
                <div class="form-grid">
                    <input id="ds_number" class="neumorphic-input" placeholder="رقم النقل الداخلي">
                    <input id="ds_client" class="neumorphic-input" placeholder="العميل">
                    <input id="ds_pickup" class="neumorphic-input" placeholder="نقطة التحميل (مخزن/ميناء)">
                    <input id="ds_dropoff" class="neumorphic-input" placeholder="نقطة التسليم (مصنع/عميل)">
                    <input id="ds_vehicleType" class="neumorphic-input" placeholder="نوع وسيلة النقل (Truck/Van/Trailer)">
                    <input id="ds_driverName" class="neumorphic-input" placeholder="اسم السائق">
                    <input id="ds_driverPhone" class="neumorphic-input" placeholder="هاتف السائق">
                    <input id="ds_carPlate" class="neumorphic-input" placeholder="رقم السيارة">
                    <input id="ds_license" class="neumorphic-input" placeholder="رقم الرخصة">
                    <select id="ds_status" class="neumorphic-input">
                        <option value="Pending">في الانتظار</option>
                        <option value="Loading">جاري التحميل</option>
                        <option value="On Road">في الطريق</option>
                        <option value="Delivered">تم التسليم</option>
                    </select>
                </div>
                <div class="form-grid">
                    <input id="ds_transportCost" type="number" step="0.01" class="neumorphic-input" placeholder="تكلفة النقل">
                    <input id="ds_roadFees" type="number" step="0.01" class="neumorphic-input" placeholder="بدل الطريق/الكارتة">
                    <input id="ds_driverExpenses" type="number" step="0.01" class="neumorphic-input" placeholder="مصاريف السائق (وقود/إقامة)">
                </div>
                <div class="form-actions">
                    <button onclick="domesticShipping.save()" class="neumorphic-button primary">حفظ</button>
                </div>
            </div>
        `;
    }

    function save() {
        const record = {
            id: generateId(),
            number: document.getElementById('ds_number').value.trim(),
            client: document.getElementById('ds_client').value.trim(),
            pickup: document.getElementById('ds_pickup').value.trim(),
            dropoff: document.getElementById('ds_dropoff').value.trim(),
            vehicleType: document.getElementById('ds_vehicleType').value.trim(),
            driverName: document.getElementById('ds_driverName').value.trim(),
            driverPhone: document.getElementById('ds_driverPhone').value.trim(),
            carPlate: document.getElementById('ds_carPlate').value.trim(),
            license: document.getElementById('ds_license').value.trim(),
            status: document.getElementById('ds_status').value,
            costs: {
                transport: safeNumber(parseFloat(document.getElementById('ds_transportCost').value), 0),
                roadFees: safeNumber(parseFloat(document.getElementById('ds_roadFees').value), 0),
                driverExpenses: safeNumber(parseFloat(document.getElementById('ds_driverExpenses').value), 0)
            },
            createdAt: new Date().toISOString()
        };

        if (!window.domesticShipments) window.domesticShipments = [];
        window.domesticShipments.push(record);
        saveData();
        showNotification('تم حفظ النقل الداخلي', 'success');
        list();
    }

    function list() {
        const container = document.getElementById('domesticShippingContent');
        const rows = window.domesticShipments || [];
        if (rows.length === 0) {
            container.innerHTML = '<div style="text-align:center;color:#666;padding:20px;">لا توجد عمليات نقل</div>';
            return;
        }
        container.innerHTML = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>رقم النقل</th><th>العميل</th><th>من</th><th>إلى</th><th>الوسيلة</th><th>الحالة</th><th>التكلفة</th>
                    </tr>
                </thead>
                <tbody>
                    ${rows.map(r => `
                        <tr>
                            <td>${r.number}</td>
                            <td>${r.client}</td>
                            <td>${r.pickup}</td>
                            <td>${r.dropoff}</td>
                            <td>${r.vehicleType}</td>
                            <td>${r.status}</td>
                            <td>${formatCurrency((r.costs.transport||0)+(r.costs.roadFees||0)+(r.costs.driverExpenses||0))}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    function showCostsReport() {
        const container = document.getElementById('domesticShippingContent');
        const rows = window.domesticShipments || [];
        const total = rows.reduce((s,r)=> s + (r.costs.transport||0)+(r.costs.roadFees||0)+(r.costs.driverExpenses||0), 0);
        container.innerHTML = `<div class="report-summary">إجمالي التكاليف: ${formatCurrency(total)}</div>` + (rows.length? '' : '<div style="text-align:center;color:#666;padding:20px;">لا توجد بيانات</div>');
    }

    function showClientsReport() {
        const container = document.getElementById('domesticShippingContent');
        const rows = window.domesticShipments || [];
        const byClient = {};
        rows.forEach(r => {
            if (!byClient[r.client]) byClient[r.client] = 0;
            byClient[r.client] += 1;
        });
        container.innerHTML = `
            <table class="data-table">
                <thead><tr><th>العميل</th><th>عدد الشحنات</th></tr></thead>
                <tbody>
                    ${Object.keys(byClient).map(c => `<tr><td>${c}</td><td>${byClient[c]}</td></tr>`).join('')}
                </tbody>
            </table>
        `;
    }

    function showEtaVsActual() {
        const container = document.getElementById('domesticShippingContent');
        container.innerHTML = '<div style="text-align:center;color:#666;padding:20px;">تقرير زمن التسليم سيتم تفصيله لاحقاً</div>';
    }

    return { showCreateForm, save, showCostsReport, showClientsReport, showEtaVsActual };
})();


