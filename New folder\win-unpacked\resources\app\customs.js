// ===== التخليص الجمركي =====

const customs = (() => {
    function showCreateForm() {
        const container = document.getElementById('customsContent');
        container.innerHTML = `
            <div class="form-container">
                <h4>إضافة بيان جمركي</h4>
                <div class="form-grid">
                    <input id="cd_number" class="neumorphic-input" placeholder="رقم البيان الجمركي">
                    <select id="cd_type" class="neumorphic-input">
                        <option value="Import">استيراد</option>
                        <option value="Export">تصدير</option>
                    </select>
                    <input id="cd_relatedShipment" class="neumorphic-input" placeholder="رقم الشحنة المرتبطة">
                    <input id="cd_port" class="neumorphic-input" placeholder="ميناء الدخول/الخروج">
                    <input id="cd_broker" class="neumorphic-input" placeholder="المخلص الجمركي">
                </div>
                <div class="form-grid">
                    <input id="cd_duties" type="number" step="0.01" class="neumorphic-input" placeholder="رسوم جمركية">
                    <input id="cd_vat" type="number" step="0.01" class="neumorphic-input" placeholder="VAT">
                    <input id="cd_quarantine" type="number" step="0.01" class="neumorphic-input" placeholder="رسوم الحجر">
                    <input id="cd_brokerFees" type="number" step="0.01" class="neumorphic-input" placeholder="رسوم المخلص">
                    <input id="cd_adminFees" type="number" step="0.01" class="neumorphic-input" placeholder="مصاريف إدارية">
                </div>
                <div class="form-grid">
                    <select id="cd_status" class="neumorphic-input">
                        <option value="Under Review">تحت المراجعة</option>
                        <option value="Waiting Approvals">بانتظار الموافقات</option>
                        <option value="Inspection">جاري الفحص</option>
                        <option value="Approved">تمت الموافقة</option>
                        <option value="Cleared">الإفراج الجمركي</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button onclick="customs.save()" class="neumorphic-button primary">حفظ</button>
                </div>
            </div>
        `;
    }

    function save() {
        const record = {
            id: generateId(),
            number: document.getElementById('cd_number').value.trim(),
            type: document.getElementById('cd_type').value,
            relatedShipment: document.getElementById('cd_relatedShipment').value.trim(),
            port: document.getElementById('cd_port').value.trim(),
            broker: document.getElementById('cd_broker').value.trim(),
            costs: {
                duties: safeNumber(parseFloat(document.getElementById('cd_duties').value), 0),
                vat: safeNumber(parseFloat(document.getElementById('cd_vat').value), 0),
                quarantine: safeNumber(parseFloat(document.getElementById('cd_quarantine').value), 0),
                brokerFees: safeNumber(parseFloat(document.getElementById('cd_brokerFees').value), 0),
                adminFees: safeNumber(parseFloat(document.getElementById('cd_adminFees').value), 0)
            },
            status: document.getElementById('cd_status').value,
            docs: {
                bl: true,
                invoice: true,
                packingList: true,
                certificateOfOrigin: false
            },
            createdAt: new Date().toISOString()
        };
        if (!window.customsDeclarations) window.customsDeclarations = [];
        window.customsDeclarations.push(record);
        saveData();
        showNotification('تم حفظ البيان الجمركي', 'success');
        list();
    }

    function list(){
        const container = document.getElementById('customsContent');
        const rows = window.customsDeclarations || [];
        if (rows.length === 0) {
            container.innerHTML = '<div style="text-align:center;color:#666;padding:20px;">لا توجد بيانات جمركية</div>';
            return;
        }
        container.innerHTML = `
            <table class="data-table">
                <thead><tr><th>الرقم</th><th>النوع</th><th>الشحنة</th><th>الميناء</th><th>الحالة</th><th>التكلفة</th></tr></thead>
                <tbody>
                    ${rows.map(r => `
                        <tr>
                            <td>${r.number}</td>
                            <td>${r.type==='Import'?'استيراد':'تصدير'}</td>
                            <td>${r.relatedShipment}</td>
                            <td>${r.port}</td>
                            <td>${r.status}</td>
                            <td>${formatCurrency((r.costs.duties||0)+(r.costs.vat||0)+(r.costs.quarantine||0)+(r.costs.brokerFees||0)+(r.costs.adminFees||0))}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    }

    function showCostsReport(){
        const container = document.getElementById('customsContent');
        const rows = window.customsDeclarations || [];
        const total = rows.reduce((s,r)=> s + (r.costs.duties||0)+(r.costs.vat||0)+(r.costs.quarantine||0)+(r.costs.brokerFees||0)+(r.costs.adminFees||0), 0);
        container.innerHTML = `<div class="report-summary">إجمالي تكاليف التخليص: ${formatCurrency(total)}</div>`;
    }

    function showDurationReport(){
        const container = document.getElementById('customsContent');
        container.innerHTML = '<div style="text-align:center;color:#666;padding:20px;">تقرير المدة الزمنية سيتم تفصيله لاحقاً</div>';
    }

    function showHeldRejectedReport(){
        const container = document.getElementById('customsContent');
        container.innerHTML = '<div style="text-align:center;color:#666;padding:20px;">تقرير المحتجز/المرفوض سيتم تفصيله لاحقاً</div>';
    }

    return { showCreateForm, save, showCostsReport, showDurationReport, showHeldRejectedReport };
})();


