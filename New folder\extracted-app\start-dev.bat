@echo off
echo تشغيل نظام إدارة نقاط البيع في وضع التطوير...
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت على النظام
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

REM التحقق من وجود npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: npm غير متوفر
    pause
    exit /b 1
)

REM تثبيت التبعيات إذا لم تكن موجودة
if not exist "node_modules" (
    echo تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo خطأ في تثبيت التبعيات
        pause
        exit /b 1
    )
)

REM تشغيل التطبيق
echo تشغيل التطبيق...
npm start

pause
