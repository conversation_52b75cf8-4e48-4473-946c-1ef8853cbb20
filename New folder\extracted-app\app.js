// ===== إدارة المنتجات =====

// عرض المنتجات في شبكة المبيعات
function displayProducts() {
    const grid = document.getElementById('productsGrid');
    grid.innerHTML = '';
    
    products.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = `product-card ${product.stockQuantity < 5 ? 'low-stock' : ''}`;
        productCard.onclick = () => addToCart(product.id);
        
        const stockStatus = product.stockQuantity <= 0 ? 'نفد' : product.stockQuantity <= 5 ? 'منخفض' : 'متوفر';
        const stockTagClass = product.stockQuantity <= 0 ? 'tag-danger' : product.stockQuantity <= 5 ? 'tag-warning' : 'tag-success';

        productCard.innerHTML = `
            <div class="product-name">${product.name}</div>
            <div class="product-price">${formatCurrency(product.sellPrice)}</div>
            <div class="product-stock">المتوفر: ${product.stockQuantity}</div>
            <div class="product-stock"><span class="tag ${stockTagClass}">${stockStatus}</span></div>
        `;
        
        grid.appendChild(productCard);
    });
}

// عرض قائمة المنتجات في تبويب الإدارة
function displayProductsList() {
    const list = document.getElementById('productsList');
    list.innerHTML = '';
    
    if (products.length === 0) {
        list.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">لا توجد منتجات مضافة</div>';
        return;
    }
    
    // إضافة رأس الجدول
    const header = document.createElement('div');
    header.className = 'product-item';
    header.style.fontWeight = 'bold';
    header.style.background = '#d0d5dc';
    header.innerHTML = `
        <div>اسم الصنف</div>
        <div>الكود</div>
        <div>الوحدة</div>
        <div>رصيد المخزون</div>
        <div>سعر الشراء</div>
        <div>سعر البيع</div>
        <div>الوصفة</div>
        <div>الإجراءات</div>
    `;
    list.appendChild(header);
    
    products.forEach(product => {
        const item = document.createElement('div');
        item.className = `product-item ${product.stockQuantity < 5 ? 'low-stock' : ''}`;
        
        const recipeStatus = product.hasRecipe ?
            `<span class="recipe-status has-recipe" title="يحتوي على وصفة">📋 نعم</span>` :
            `<span class="recipe-status no-recipe" title="لا يحتوي على وصفة">❌ لا</span>`;

        item.innerHTML = `
            <div>${product.name}</div>
            <div>${product.code || '-'}</div>
            <div>${product.unit || '-'}</div>
            <div>${product.stockQuantity} ${product.stockQuantity < 5 ? '⚠️' : ''}</div>
            <div>${formatCurrency(product.costPrice)}</div>
            <div>${formatCurrency(product.sellPrice)}</div>
            <div>${recipeStatus}</div>
            <div class="product-actions">
                <button class="edit-btn" onclick="editProduct(${product.id})">تعديل</button>
                <button class="delete-btn" onclick="deleteProduct(${product.id})">حذف</button>
                ${product.hasRecipe ?
                    `<button class="recipe-btn" onclick="viewProductRecipe('${product.id}')" title="عرض الوصفة">📋</button>` :
                    `<button class="recipe-btn" onclick="createRecipeForProduct('${product.id}')" title="إنشاء وصفة">➕</button>`
                }
            </div>
        `;
        
        list.appendChild(item);
    });
}

// إظهار نموذج إضافة منتج
function showAddProductForm() {
    document.getElementById('addProductForm').classList.remove('hidden');
    document.getElementById('productName').focus();
}

// إخفاء نموذج إضافة منتج
function hideAddProductForm() {
    document.getElementById('addProductForm').classList.add('hidden');
    clearProductForm();
}

// مسح نموذج المنتج
function clearProductForm() {
    document.getElementById('productName').value = '';
    document.getElementById('productCode').value = '';
    document.getElementById('productUnit').value = '';
    document.getElementById('productStockQuantity').value = '';
    document.getElementById('productCostPrice').value = '';
    document.getElementById('productSellPrice').value = '';
}

// إضافة منتج جديد
function addProduct() {
    const name = sanitizeInput(document.getElementById('productName').value);
    const code = sanitizeInput(document.getElementById('productCode').value);
    const unit = sanitizeInput(document.getElementById('productUnit').value);
    const stockQuantity = parseFloat(document.getElementById('productStockQuantity').value);
    const costPrice = parseFloat(document.getElementById('productCostPrice').value);
    const sellPrice = parseFloat(document.getElementById('productSellPrice').value);
    
    // التحقق من صحة البيانات
    if (!validateInput(name, 'text')) {
        showNotification('يرجى إدخال اسم الصنف', 'error');
        return;
    }

    if (!validateInput(code, 'text')) {
        showNotification('يرجى إدخال كود الصنف', 'error');
        return;
    }

    if (!validateInput(unit, 'text')) {
        showNotification('يرجى إدخال الوحدة', 'error');
        return;
    }
    
    if (!validateInput(costPrice, 'number')) {
        showNotification('يرجى إدخال سعر شراء صحيح', 'error');
        return;
    }
    
    if (!validateInput(sellPrice, 'number')) {
        showNotification('يرجى إدخال سعر بيع صحيح', 'error');
        return;
    }
    
    if (stockQuantity < 0) {
        showNotification('يرجى إدخال رصيد مخزون صحيح', 'error');
        return;
    }

    if (sellPrice <= costPrice) {
        showNotification('سعر البيع يجب أن يكون أكبر من سعر الشراء', 'error');
        return;
    }

    // التحقق من عدم تكرار الكود
    if (products.some(p => p.code === code)) {
        showNotification('كود الصنف موجود مسبقاً', 'error');
        return;
    }

    try {
        // إنشاء المنتج الجديد
        const product = createProduct(name, code, unit, stockQuantity, costPrice, sellPrice);
    
        products.push(product);
        saveData();
        displayProducts();
        displayProductsList();
        hideAddProductForm();
        showNotification('تم إضافة الصنف بنجاح', 'success');

    } catch (error) {
        console.error('خطأ في إضافة المنتج:', error);
        showNotification('حدث خطأ أثناء إضافة الصنف', 'error');
    }
}

// تعديل منتج
function editProduct(id) {
    const product = products.find(p => p.id === id);
    if (!product) return;
    
    // ملء النموذج بالبيانات الحالية
    document.getElementById('productName').value = product.name;
    document.getElementById('productCategory').value = product.category;
    document.getElementById('productCostPrice').value = product.costPrice;
    document.getElementById('productSellPrice').value = product.sellPrice;
    document.getElementById('productQuantity').value = product.quantity;
    document.getElementById('productBarcode').value = product.barcode;
    
    // إظهار النموذج
    showAddProductForm();
    
    // تغيير زر الإضافة إلى تحديث
    const addButton = document.querySelector('#addProductForm .form-actions .neumorphic-button.primary');
    addButton.textContent = 'تحديث المنتج';
    addButton.onclick = () => updateProduct(id);
}

// تحديث منتج
function updateProduct(id) {
    const name = sanitizeInput(document.getElementById('productName').value);
    const category = sanitizeInput(document.getElementById('productCategory').value);
    const costPrice = parseFloat(document.getElementById('productCostPrice').value);
    const sellPrice = parseFloat(document.getElementById('productSellPrice').value);
    const quantity = parseInt(document.getElementById('productQuantity').value);
    const barcode = sanitizeInput(document.getElementById('productBarcode').value);
    
    // التحقق من صحة البيانات
    if (!validateInput(name, 'text') || !validateInput(category, 'text') || 
        !validateInput(costPrice, 'number') || !validateInput(sellPrice, 'number') || 
        !validateInput(quantity, 'number')) {
        showNotification('يرجى ملء جميع الحقول بشكل صحيح', 'error');
        return;
    }
    
    if (sellPrice <= costPrice) {
        showNotification('سعر البيع يجب أن يكون أكبر من سعر الشراء', 'error');
        return;
    }
    
    // التحقق من عدم تكرار الاسم (باستثناء المنتج الحالي)
    if (products.some(p => p.id !== id && p.name.toLowerCase() === name.toLowerCase())) {
        showNotification('يوجد منتج بنفس الاسم مسبقاً', 'error');
        return;
    }
    
    // تحديث المنتج
    const productIndex = products.findIndex(p => p.id === id);
    if (productIndex !== -1) {
        products[productIndex] = {
            ...products[productIndex],
            name,
            category,
            costPrice,
            sellPrice,
            quantity,
            barcode,
            updatedAt: new Date().toISOString()
        };
        
        saveData();
        displayProducts();
        displayProductsList();
        hideAddProductForm();
        
        // إعادة تعيين زر الإضافة
        const addButton = document.querySelector('#addProductForm .form-actions .neumorphic-button.primary');
        addButton.textContent = 'إضافة المنتج';
        addButton.onclick = addProduct;
        
        showNotification('تم تحديث المنتج بنجاح', 'success');
    }
}

// حذف منتج
function deleteProduct(id) {
    const product = products.find(p => p.id === id);
    if (!product) return;
    
    if (confirm(`هل أنت متأكد من حذف المنتج "${product.name}"؟`)) {
        products = products.filter(p => p.id !== id);
        saveData();
        displayProducts();
        displayProductsList();
        showNotification('تم حذف المنتج بنجاح', 'success');
    }
}

// ===== إدارة سلة المشتريات =====

// إضافة منتج للسلة
function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    if (product.stockQuantity <= 0) {
        showNotification('هذا المنتج غير متوفر في المخزون', 'error');
        return;
    }

    const existingItem = cart.find(item => item.productId === productId);

    if (existingItem) {
        if (existingItem.quantity >= product.stockQuantity) {
            showNotification('لا يمكن إضافة كمية أكثر من المتوفر في المخزون', 'error');
            return;
        }
        existingItem.quantity++;
    } else {
        cart.push({
            productId,
            name: product.name,
            price: product.sellPrice,
            costPrice: product.costPrice,
            quantity: 1
        });
    }
    
    updateCart();
    saveData();
}

// تحديث عرض السلة
function updateCart() {
    const cartItems = document.getElementById('cartItems');
    cartItems.innerHTML = '';
    
    if (cart.length === 0) {
        cartItems.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">السلة فارغة</div>';
        updateCartTotals();
        return;
    }
    
    cart.forEach((item, index) => {
        const cartItem = document.createElement('div');
        cartItem.className = 'cart-item';
        
        cartItem.innerHTML = `
            <div class="cart-item-info">
                <div class="cart-item-name">${item.name}</div>
                <div class="cart-item-price">${formatCurrency(item.price)} × ${item.quantity}</div>
            </div>
            <div class="cart-item-controls">
                <button class="quantity-btn" onclick="decreaseQuantity(${index})">-</button>
                <span class="quantity-display">${item.quantity}</span>
                <button class="quantity-btn" onclick="increaseQuantity(${index})">+</button>
                <button class="remove-btn" onclick="removeFromCart(${index})">×</button>
            </div>
        `;
        
        cartItems.appendChild(cartItem);
    });
    
    updateCartTotals();
}

// زيادة الكمية
function increaseQuantity(index) {
    const item = cart[index];
    const product = products.find(p => p.id === item.productId);
    
    if (item.quantity >= product.stockQuantity) {
        showNotification('لا يمكن إضافة كمية أكثر من المتوفر في المخزون', 'error');
        return;
    }
    
    item.quantity++;
    updateCart();
    saveData();
}

// تقليل الكمية
function decreaseQuantity(index) {
    const item = cart[index];
    
    if (item.quantity > 1) {
        item.quantity--;
        updateCart();
        saveData();
    } else {
        removeFromCart(index);
    }
}

// إزالة عنصر من السلة
function removeFromCart(index) {
    cart.splice(index, 1);
    updateCart();
    saveData();
}

// مسح السلة
function clearCart() {
    if (cart.length === 0) return;
    
    if (confirm('هل أنت متأكد من مسح السلة؟')) {
        cart = [];
        updateCart();
        saveData();
        showNotification('تم مسح السلة', 'info');
    }
}

// تحديث إجماليات السلة
function updateCartTotals() {
    const subtotal = cart.reduce((sum, item) => sum + safeNumber(item.price * item.quantity, 0), 0);
    const tax = safeNumber(subtotal * settings.taxRate, 0);
    const total = safeNumber(subtotal + tax, 0);
    
    document.getElementById('subtotal').textContent = formatCurrency(subtotal);
    document.getElementById('tax').textContent = formatCurrency(tax);
    document.getElementById('total').textContent = formatCurrency(total);
}

// تبديل عرض اختيار العميل
function toggleCustomerSelection() {
    const paymentMethod = document.querySelector('input[name="salePayment"]:checked').value;
    const customerSelection = document.getElementById('customerSelection');

    if (paymentMethod === 'credit') {
        customerSelection.classList.remove('hidden');
        updateCustomersList();
    } else {
        customerSelection.classList.add('hidden');
    }
}

// تحديث قائمة العملاء في المبيعات
function updateCustomersList() {
    const select = document.getElementById('saleCustomer');
    select.innerHTML = '<option value="">اختر العميل</option>';

    customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = customer.name;
        select.appendChild(option);
    });
}

// إنهاء عملية البيع
function completeSale() {
    if (cart.length === 0) {
        showNotification('السلة فارغة', 'error');
        return;
    }

    // التحقق من توفر المنتجات والمكونات
    for (let item of cart) {
        const product = products.find(p => p.id === item.productId);
        if (!product || product.stockQuantity < item.quantity) {
            showNotification(`الكمية المطلوبة من "${item.name}" غير متوفرة`, 'error');
            return;
        }

        // التحقق من توفر مكونات الوصفة إذا كان المنتج يحتوي على وصفة
        if (product.hasRecipe) {
            const recipe = productRecipes.find(r => r.productId === product.id);
            if (recipe) {
                const missingIngredients = checkRecipeIngredients(recipe, item.quantity);
                if (missingIngredients.length > 0) {
                    const missingList = missingIngredients.map(ing =>
                        `${ing.name}: مطلوب ${ing.required} ${ing.unit || ''} - متوفر ${ing.available} ${ing.unit || ''}`
                    ).join('\n');
                    showNotification(`لا يمكن بيع "${item.name}" - مكونات غير متوفرة:\n${missingList}`, 'error');
                    return;
                }
            }
        }
    }

    // الحصول على طريقة الدفع
    const paymentMethod = document.querySelector('input[name="salePayment"]:checked').value;
    let customerId = null;

    // التحقق من اختيار العميل للمبيعات الآجلة
    if (paymentMethod === 'credit') {
        customerId = document.getElementById('saleCustomer').value;
        if (!customerId) {
            showNotification('يرجى اختيار العميل للمبيعات الآجلة', 'error');
            return;
        }
    }

    // حساب الإجماليات
    const subtotal = cart.reduce((sum, item) => sum + safeNumber(item.price * item.quantity, 0), 0);
    const tax = safeNumber(subtotal * settings.taxRate, 0);
    const total = safeNumber(subtotal + tax, 0);

    // إنشاء عملية البيع
    const sale = {
        id: generateId(),
        items: [...cart],
        subtotal,
        tax,
        total,
        paymentMethod,
        customerId,
        date: new Date().toISOString(),
        invoiceNumber: `INV-${Date.now()}`
    };

    // تحديث المخزون وخصم المكونات
    const recipeDeductions = []; // لتتبع خصم المكونات

    cart.forEach(item => {
        const product = products.find(p => p.id === item.productId);
        if (product) {
            // خصم المنتج النهائي من المخزون
            product.stockQuantity -= item.quantity;

            // إذا كان المنتج يحتوي على وصفة، خصم المكونات
            if (product.hasRecipe) {
                const recipe = productRecipes.find(r => r.productId === product.id);
                if (recipe) {
                    const deductedIngredients = deductRecipeIngredients(recipe, item.quantity);
                    recipeDeductions.push({
                        productName: product.name,
                        quantity: item.quantity,
                        ingredients: deductedIngredients
                    });
                }
            }
        }
    });

    // تحديث بيانات العميل
    if (customerId) {
        const customer = customers.find(c => c.id === customerId);
        if (customer) {
            // تحديث الرصيد للمبيعات الآجلة فقط
            if (paymentMethod === 'credit') {
                if (!customer.currentBalance) customer.currentBalance = 0;
                customer.currentBalance += total;
            }

            // تحديث إجمالي المشتريات وتاريخ آخر معاملة
            if (!customer.totalPurchases) customer.totalPurchases = 0;
            customer.totalPurchases += total;
            customer.lastTransactionDate = new Date().toISOString();
        }
    } else if (paymentMethod === 'cash') {
        // ربط المبيعات النقدية بالعميل الافتراضي "ضيف"
        const guestCustomer = customers.find(c => c.id === 'guest');
        if (guestCustomer) {
            sale.customerId = 'guest';
            if (!guestCustomer.totalPurchases) guestCustomer.totalPurchases = 0;
            guestCustomer.totalPurchases += total;
            guestCustomer.lastTransactionDate = new Date().toISOString();
            // لا نحدث رصيد الضيف (يبقى صفر دائماً)
        }
    }

    // حفظ البيع
    sales.push(sale);

    // إنشاء الفاتورة
    generateInvoice(sale);

    // مسح السلة
    cart = [];

    // إعادة تعيين طريقة الدفع
    document.querySelector('input[name="salePayment"][value="cash"]').checked = true;
    toggleCustomerSelection();

    // حفظ البيانات وتحديث العرض
    saveData();
    updateCart();
    displayProducts();
    displayProductsList();
    checkLowStock();

    // إشعار بخصم المكونات إذا كان هناك وصفات
    let notificationMessage = 'تم إنهاء عملية البيع بنجاح';
    if (recipeDeductions.length > 0) {
        notificationMessage += '\n\nتم خصم المكونات التالية:';
        recipeDeductions.forEach(deduction => {
            notificationMessage += `\n• ${deduction.productName} (${deduction.quantity}):`;
            deduction.ingredients.forEach(ingredient => {
                notificationMessage += `\n  - ${ingredient.name}: ${ingredient.deductedQuantity} ${ingredient.unit}`;
            });
        });
    }

    showNotification(notificationMessage, 'success');
}

// التحقق من المخزون المنخفض
function checkLowStock() {
    const lowStockProducts = products.filter(p => p.stockQuantity < 5);
    const alertDiv = document.getElementById('lowStockAlert');

    if (lowStockProducts.length > 0) {
        alertDiv.innerHTML = `
            ⚠️ تنبيه: يوجد ${lowStockProducts.length} منتج بمخزون منخفض
            <br>
            ${lowStockProducts.map(p => `${p.name} (${p.stockQuantity})`).join(', ')}
        `;
        alertDiv.style.display = 'block';
    } else {
        alertDiv.style.display = 'none';
    }
}

// ===== إدارة العملاء =====

// عرض قائمة العملاء
function displayCustomersList() {
    const list = document.getElementById('customersList');
    list.innerHTML = '';

    if (customers.length === 0) {
        list.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">لا توجد عملاء مضافين</div>';
        return;
    }

    customers.forEach(customer => {
        const item = document.createElement('div');
        item.className = 'customer-item';
        item.style.cssText = `
            background: var(--bg-primary);
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 15px;
            box-shadow: 6px 6px 12px var(--shadow-dark), -6px -6px 12px var(--shadow-light);
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto;
            gap: 15px;
            align-items: center;
            color: var(--text-primary);
        `;

        const balanceColor = customer.currentBalance > 0 ? '#e17055' : customer.currentBalance < 0 ? '#00b894' : '#666';
        const balanceText = customer.currentBalance > 0 ? 'مدين' : customer.currentBalance < 0 ? 'دائن' : 'متوازن';

        item.innerHTML = `
            <div>
                <div style="font-weight: bold; margin-bottom: 5px;">
                    ${customer.name}
                    ${customer.isGuest ? '<span class="tag tag-info" style="margin-right: 5px;">ضيف</span>' : ''}
                </div>
                <div style="color: var(--text-secondary); font-size: 12px;">${customer.address || 'لا يوجد عنوان'}</div>
            </div>
            <div style="color: var(--text-secondary);">${customer.phone || 'لا يوجد هاتف'}</div>
            <div style="color: var(--text-secondary);">${customer.email || 'لا يوجد إيميل'}</div>
            <div>
                <div style="font-weight: bold; color: ${balanceColor};">${formatCurrency(Math.abs(customer.currentBalance || 0))}</div>
                <div style="font-size: 12px; color: ${balanceColor};">${balanceText}</div>
            </div>
            <div class="customer-actions">
                <button class="view-btn" onclick="viewCustomerDetails('${customer.id}')" style="background: #74b9ff; color: white; margin-left: 5px;">تفاصيل</button>
                ${!customer.isGuest ? `
                    <button class="edit-btn" onclick="editCustomer('${customer.id}')">تعديل</button>
                    <button class="delete-btn" onclick="deleteCustomer('${customer.id}')">حذف</button>
                ` : '<span style="color: var(--text-secondary); font-size: 12px;">عميل افتراضي</span>'}
            </div>
        `;

        list.appendChild(item);
    });
}

// إظهار نموذج إضافة عميل
function showAddCustomerForm() {
    document.getElementById('addCustomerForm').classList.remove('hidden');
    document.getElementById('customerName').focus();
}

// إخفاء نموذج إضافة عميل
function hideAddCustomerForm() {
    document.getElementById('addCustomerForm').classList.add('hidden');
    clearCustomerForm();
}

// مسح نموذج العميل
function clearCustomerForm() {
    document.getElementById('customerName').value = '';
    document.getElementById('customerPhone').value = '';
    document.getElementById('customerEmail').value = '';
    document.getElementById('customerAddress').value = '';
}

// إضافة عميل جديد
function addCustomer() {
    const name = sanitizeInput(document.getElementById('customerName').value);
    const phone = sanitizeInput(document.getElementById('customerPhone').value);
    const email = sanitizeInput(document.getElementById('customerEmail').value);
    const address = sanitizeInput(document.getElementById('customerAddress').value);

    // التحقق من صحة البيانات
    if (!validateInput(name, 'text')) {
        showNotification('يرجى إدخال اسم العميل', 'error');
        return;
    }

    if (phone && !validateInput(phone, 'phone')) {
        showNotification('رقم الهاتف غير صحيح', 'error');
        return;
    }

    if (email && !validateInput(email, 'email')) {
        showNotification('البريد الإلكتروني غير صحيح', 'error');
        return;
    }

    // التحقق من عدم تكرار الاسم
    if (customers.some(c => c.name.toLowerCase() === name.toLowerCase())) {
        showNotification('يوجد عميل بنفس الاسم مسبقاً', 'error');
        return;
    }

    // إضافة العميل
    const customer = {
        id: generateId(),
        name,
        phone,
        email,
        address,
        currentBalance: 0,
        totalPurchases: 0,
        lastTransactionDate: null,
        createdAt: new Date().toISOString()
    };

    customers.push(customer);
    saveData();
    displayCustomersList();
    hideAddCustomerForm();
    showNotification('تم إضافة العميل بنجاح', 'success');
}

// تعديل عميل
function editCustomer(id) {
    const customer = customers.find(c => c.id === id);
    if (!customer) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('customerName').value = customer.name;
    document.getElementById('customerPhone').value = customer.phone || '';
    document.getElementById('customerEmail').value = customer.email || '';
    document.getElementById('customerAddress').value = customer.address || '';

    // إظهار النموذج
    showAddCustomerForm();

    // تغيير زر الإضافة إلى تحديث
    const addButton = document.querySelector('#addCustomerForm .form-actions .neumorphic-button.primary');
    addButton.textContent = 'تحديث العميل';
    addButton.onclick = () => updateCustomer(id);
}

// تحديث عميل
function updateCustomer(id) {
    const name = sanitizeInput(document.getElementById('customerName').value);
    const phone = sanitizeInput(document.getElementById('customerPhone').value);
    const email = sanitizeInput(document.getElementById('customerEmail').value);
    const address = sanitizeInput(document.getElementById('customerAddress').value);

    // التحقق من صحة البيانات
    if (!validateInput(name, 'text')) {
        showNotification('يرجى إدخال اسم العميل', 'error');
        return;
    }

    if (phone && !validateInput(phone, 'phone')) {
        showNotification('رقم الهاتف غير صحيح', 'error');
        return;
    }

    if (email && !validateInput(email, 'email')) {
        showNotification('البريد الإلكتروني غير صحيح', 'error');
        return;
    }

    // التحقق من عدم تكرار الاسم (باستثناء العميل الحالي)
    if (customers.some(c => c.id !== id && c.name.toLowerCase() === name.toLowerCase())) {
        showNotification('يوجد عميل بنفس الاسم مسبقاً', 'error');
        return;
    }

    // تحديث العميل
    const customerIndex = customers.findIndex(c => c.id === id);
    if (customerIndex !== -1) {
        customers[customerIndex] = {
            ...customers[customerIndex],
            name,
            phone,
            email,
            address,
            updatedAt: new Date().toISOString()
        };

        saveData();
        displayCustomersList();
        hideAddCustomerForm();

        // إعادة تعيين زر الإضافة
        const addButton = document.querySelector('#addCustomerForm .form-actions .neumorphic-button.primary');
        addButton.textContent = 'إضافة العميل';
        addButton.onclick = addCustomer;

        showNotification('تم تحديث العميل بنجاح', 'success');
    }
}

// حذف عميل
function deleteCustomer(id) {
    const customer = customers.find(c => c.id === id);
    if (!customer) return;

    // منع حذف العميل الافتراضي "ضيف"
    if (customer.isGuest) {
        showNotification('لا يمكن حذف العميل الافتراضي "ضيف"', 'error');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف العميل "${customer.name}"؟`)) {
        customers = customers.filter(c => c.id !== id);
        saveData();
        displayCustomersList();
        showNotification('تم حذف العميل بنجاح', 'success');
    }
}

// عرض تفاصيل العميل
function viewCustomerDetails(customerId) {
    const customer = customers.find(c => c.id === customerId);
    if (!customer) return;

    const modal = document.getElementById('customerDetailsModal');
    const content = document.getElementById('customerDetailsContent');

    // حفظ معرف العميل في النافذة
    modal.dataset.customerId = customerId;

    // حساب إحصائيات العميل
    const customerSales = sales.filter(s => s.customerId === customerId);
    const customerPayments = getCustomerPayments(customerId);

    const totalSales = customerSales.reduce((sum, sale) => sum + sale.total, 0);
    const totalPayments = customerPayments.reduce((sum, payment) => sum + payment.amount, 0);

    // تحديد لون الرصيد
    let balanceClass = 'neutral';
    let balanceText = 'متوازن';
    if (customer.currentBalance > 0) {
        balanceClass = 'debt';
        balanceText = 'مدين';
    } else if (customer.currentBalance < 0) {
        balanceClass = 'credit';
        balanceText = 'دائن';
    }

    content.innerHTML = `
        <div class="customer-info-header">
            <div>
                <h4>معلومات العميل</h4>
                <p><strong>الاسم:</strong> ${customer.name}</p>
                <p><strong>الهاتف:</strong> ${customer.phone || 'غير محدد'}</p>
                <p><strong>البريد:</strong> ${customer.email || 'غير محدد'}</p>
                <p><strong>العنوان:</strong> ${customer.address || 'غير محدد'}</p>
                <p><strong>تاريخ التسجيل:</strong> ${formatDate(customer.createdAt)}</p>
            </div>
            <div>
                <h4>الإحصائيات</h4>
                <p><strong>إجمالي المشتريات:</strong> ${formatCurrency(totalSales)}</p>
                <p><strong>إجمالي المدفوعات:</strong> ${formatCurrency(totalPayments)}</p>
                <p><strong>عدد المعاملات:</strong> ${customerSales.length}</p>
                <p><strong>آخر معاملة:</strong> ${customer.lastTransactionDate ? formatDate(customer.lastTransactionDate) : 'لا توجد معاملات'}</p>
            </div>
        </div>

        <div class="customer-balance ${balanceClass}">
            <h3>الرصيد الحالي</h3>
            <div style="font-size: 24px; font-weight: bold;">${formatCurrency(Math.abs(customer.currentBalance || 0))}</div>
            <div>${balanceText}</div>
        </div>

        <div class="transactions-section">
            <h4>المعاملات الأخيرة</h4>
            <div class="transactions-list">
                ${generateCustomerTransactionsList(customerId)}
            </div>
        </div>
    `;

    modal.classList.remove('hidden');
}

// إنشاء قائمة معاملات العميل
function generateCustomerTransactionsList(customerId) {
    const customerSales = sales.filter(s => s.customerId === customerId);
    const customerPayments = getCustomerPayments(customerId);

    // دمج المعاملات وترتيبها حسب التاريخ
    const allTransactions = [
        ...customerSales.map(sale => ({
            type: 'sale',
            date: sale.date,
            amount: sale.total,
            description: `فاتورة رقم ${sale.invoiceNumber}`,
            id: sale.id
        })),
        ...customerPayments.map(payment => ({
            type: 'payment',
            date: payment.date,
            amount: payment.amount,
            description: payment.notes || 'دفعة',
            id: payment.id
        }))
    ].sort((a, b) => new Date(b.date) - new Date(a.date));

    if (allTransactions.length === 0) {
        return '<div style="text-align: center; color: var(--text-secondary); padding: 20px;">لا توجد معاملات</div>';
    }

    return allTransactions.slice(0, 10).map(transaction => `
        <div class="transaction-item">
            <div class="transaction-type ${transaction.type}">
                ${transaction.type === 'sale' ? '🛒 مبيعات' : '💰 دفعة'}
            </div>
            <div>${transaction.description}</div>
            <div style="font-weight: bold; color: ${transaction.type === 'sale' ? '#e17055' : '#00b894'};">
                ${transaction.type === 'sale' ? '+' : '-'}${formatCurrency(transaction.amount)}
            </div>
            <div style="font-size: 12px; color: var(--text-secondary);">${formatDateShort(transaction.date)}</div>
        </div>
    `).join('');
}

// الحصول على مدفوعات العميل
function getCustomerPayments(customerId) {
    return customerPayments.filter(payment => payment.customerId === customerId);
}

// إغلاق نافذة تفاصيل العميل
function closeCustomerDetailsModal() {
    document.getElementById('customerDetailsModal').classList.add('hidden');
}

// عرض نموذج تسجيل الدفعة
function showPaymentForm() {
    const customerDetailsModal = document.getElementById('customerDetailsModal');
    const customerId = customerDetailsModal.dataset.customerId;

    if (!customerId) return;

    document.getElementById('paymentCustomerId').value = customerId;
    document.getElementById('paymentAmount').value = '';
    document.getElementById('paymentNotes').value = '';

    document.getElementById('paymentModal').classList.remove('hidden');
}

// إغلاق نافذة تسجيل الدفعة
function closePaymentModal() {
    document.getElementById('paymentModal').classList.add('hidden');
}

// تسجيل دفعة من العميل
function recordPayment() {
    const customerId = document.getElementById('paymentCustomerId').value;
    const amount = parseFloat(document.getElementById('paymentAmount').value);
    const notes = document.getElementById('paymentNotes').value.trim();

    // التحقق من صحة البيانات
    if (!customerId) {
        showNotification('خطأ في تحديد العميل', 'error');
        return;
    }

    if (!amount || amount <= 0) {
        showNotification('يرجى إدخال مبلغ صحيح', 'error');
        return;
    }

    const customer = customers.find(c => c.id === customerId);
    if (!customer) {
        showNotification('العميل غير موجود', 'error');
        return;
    }

    // إنشاء سجل الدفعة
    const payment = {
        id: generateId(),
        customerId: customerId,
        amount: amount,
        notes: notes,
        date: new Date().toISOString(),
        createdAt: new Date().toISOString()
    };

    // إضافة الدفعة
    customerPayments.push(payment);

    // تحديث رصيد العميل
    if (!customer.currentBalance) customer.currentBalance = 0;
    customer.currentBalance -= amount;
    customer.lastTransactionDate = new Date().toISOString();

    // حفظ البيانات
    saveData();

    // إنشاء إيصال الاستلام
    generatePaymentReceipt(payment, customer);

    // إغلاق النوافذ وتحديث العرض
    closePaymentModal();
    closeCustomerDetailsModal();
    displayCustomersList();

    showNotification('تم تسجيل الدفعة بنجاح', 'success');
}

// إنشاء إيصال استلام المبلغ
function generatePaymentReceipt(payment, customer) {
    let companyInfo = '';
    if (settings.showCompanyInInvoice) {
        companyInfo = `
            <div class="company-info" style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 15px;">
                <h2 style="margin: 0; color: #333;">${settings.companyName || 'اسم الشركة'}</h2>
                <div style="margin: 5px 0;">${settings.activity || 'النشاط'}</div>
                ${settings.commercialRegister ? `<div style="margin: 5px 0;">س.ت: ${settings.commercialRegister}</div>` : ''}
                ${settings.phone ? `<div style="margin: 5px 0;">هاتف: ${settings.phone}</div>` : ''}
                ${settings.address ? `<div style="margin: 5px 0;">العنوان: ${settings.address}</div>` : ''}
                ${settings.email ? `<div style="margin: 5px 0;">البريد: ${settings.email}</div>` : ''}
            </div>
        `;
    }

    const receiptContent = `
        ${companyInfo}
        <div class="receipt-header">
            <h2>إيصال استلام مبلغ</h2>
            <div class="receipt-info">
                <div>رقم الإيصال: ${payment.id}</div>
                <div>التاريخ: ${formatDate(payment.date)}</div>
                <div>العميل: ${customer.name}</div>
            </div>
        </div>

        <div class="receipt-body">
            <p>استلمت أنا / المقر أدناه من السيد/ة <strong>${customer.name}</strong></p>
            <p>مبلغ وقدره: <strong>${formatCurrency(payment.amount)}</strong></p>
            ${payment.notes ? `<p>البيان: ${payment.notes}</p>` : ''}

            <div class="receipt-summary">
                <div class="summary-row">
                    <span>المبلغ المستلم:</span>
                    <span style="font-weight: bold; font-size: 18px;">${formatCurrency(payment.amount)}</span>
                </div>
                <div class="summary-row">
                    <span>الرصيد الحالي للعميل:</span>
                    <span style="font-weight: bold; color: ${customer.currentBalance > 0 ? '#e17055' : '#00b894'};">
                        ${formatCurrency(Math.abs(customer.currentBalance))} ${customer.currentBalance > 0 ? '(مدين)' : '(دائن)'}
                    </span>
                </div>
            </div>

            <div class="receipt-footer">
                <div class="signature-section">
                    <div>المستلم: _______________</div>
                    <div>التوقيع: _______________</div>
                    <div>التاريخ: ${formatDateShort(new Date())}</div>
                </div>
            </div>
        </div>
    `;

    // عرض الإيصال في نافذة منبثقة
    showReceiptModal(receiptContent, 'إيصال استلام مبلغ');
}

// عرض نافذة الإيصال
function showReceiptModal(content, title) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content receipt-modal">
            <div class="modal-header">
                <h3>${title}</h3>
                <button onclick="this.closest('.modal').remove()" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            <div class="modal-actions">
                <button onclick="printReceipt()" class="neumorphic-button primary">طباعة</button>
                <button onclick="this.closest('.modal').remove()" class="neumorphic-button secondary">إغلاق</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// طباعة الإيصال
function printReceipt() {
    const receiptContent = document.querySelector('.receipt-modal .modal-body').innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>إيصال</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; }
                    .receipt-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                    .receipt-table th, .receipt-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                    .receipt-table th { background-color: #f5f5f5; }
                    .receipt-summary { margin: 20px 0; }
                    .summary-row { display: flex; justify-content: space-between; margin: 5px 0; }
                    .signature-section { margin-top: 40px; }
                    .signature-section div { margin: 10px 0; }
                </style>
            </head>
            <body>
                ${receiptContent}
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// ===== إدارة الديون =====

// عرض صفحة الديون
function displayDebtsPage() {
    refreshDebtsData();
    displayDebtsList();
    displayRecentPayments();
}

// تحديث بيانات الديون
function refreshDebtsData() {
    // حساب إجمالي الديون
    const totalDebts = customers.reduce((sum, customer) => {
        return sum + Math.max(0, customer.currentBalance || 0);
    }, 0);

    // عدد العملاء المدينين
    const debtorsCount = customers.filter(customer => (customer.currentBalance || 0) > 0).length;

    // مدفوعات اليوم
    const today = new Date().toISOString().split('T')[0];
    const todayPayments = customerPayments
        .filter(payment => payment.date.startsWith(today))
        .reduce((sum, payment) => sum + payment.amount, 0);

    // تحديث العرض
    document.getElementById('totalDebts').textContent = formatCurrency(totalDebts);
    document.getElementById('debtorsCount').textContent = debtorsCount;
    document.getElementById('todayPayments').textContent = formatCurrency(todayPayments);
}

// عرض قائمة الديون
function displayDebtsList() {
    const list = document.getElementById('debtsList');
    const filter = document.getElementById('debtsFilter')?.value || 'all';
    const sortBy = document.getElementById('debtsSortBy')?.value || 'balance';

    // فلترة العملاء
    let filteredCustomers = customers.filter(customer => {
        if (customer.isGuest) return false; // استبعاد العميل الافتراضي

        const balance = customer.currentBalance || 0;
        switch(filter) {
            case 'debtors':
                return balance > 0;
            case 'creditors':
                return balance < 0;
            default:
                return true;
        }
    });

    // ترتيب العملاء
    filteredCustomers.sort((a, b) => {
        switch(sortBy) {
            case 'name':
                return a.name.localeCompare(b.name);
            case 'lastTransaction':
                const dateA = new Date(a.lastTransactionDate || 0);
                const dateB = new Date(b.lastTransactionDate || 0);
                return dateB - dateA;
            case 'balance':
            default:
                return (b.currentBalance || 0) - (a.currentBalance || 0);
        }
    });

    if (filteredCustomers.length === 0) {
        list.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 20px;">لا توجد بيانات للعرض</div>';
        return;
    }

    list.innerHTML = filteredCustomers.map(customer => {
        const balance = customer.currentBalance || 0;
        const balanceClass = balance > 0 ? 'positive' : balance < 0 ? 'negative' : '';
        const itemClass = balance > 1000 ? 'high-debt' : balance < 0 ? 'credit' : '';
        const balanceText = balance > 0 ? 'مدين' : balance < 0 ? 'دائن' : 'متوازن';

        return `
            <div class="debt-item ${itemClass}">
                <div class="debt-customer-info">
                    <div class="debt-customer-name">${customer.name}</div>
                    <div class="debt-customer-contact">${customer.phone || 'لا يوجد هاتف'}</div>
                </div>
                <div class="debt-balance ${balanceClass}">
                    ${formatCurrency(Math.abs(balance))}
                    <div style="font-size: 12px; font-weight: normal;">${balanceText}</div>
                </div>
                <div>
                    ${formatCurrency(customer.totalPurchases || 0)}
                    <div style="font-size: 12px; color: var(--text-secondary);">إجمالي المشتريات</div>
                </div>
                <div class="debt-last-transaction">
                    ${customer.lastTransactionDate ? formatDateShort(customer.lastTransactionDate) : 'لا توجد معاملات'}
                </div>
                <div class="customer-actions">
                    <button class="view-btn" onclick="viewCustomerDetails('${customer.id}')">تفاصيل</button>
                    ${balance > 0 ? `<button class="neumorphic-button" style="background: #00b894; color: white; font-size: 12px; padding: 5px 10px;" onclick="quickPayment('${customer.id}')">دفعة سريعة</button>` : ''}
                </div>
            </div>
        `;
    }).join('');
}

// عرض المدفوعات الأخيرة
function displayRecentPayments() {
    const list = document.getElementById('recentPaymentsList');

    // ترتيب المدفوعات حسب التاريخ (الأحدث أولاً)
    const recentPayments = [...customerPayments]
        .sort((a, b) => new Date(b.date) - new Date(a.date))
        .slice(0, 10);

    if (recentPayments.length === 0) {
        list.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 20px;">لا توجد مدفوعات</div>';
        return;
    }

    list.innerHTML = recentPayments.map(payment => {
        const customer = customers.find(c => c.id === payment.customerId);
        return `
            <div class="payment-item">
                <div class="payment-customer">${customer ? customer.name : 'عميل محذوف'}</div>
                <div class="payment-amount">${formatCurrency(payment.amount)}</div>
                <div class="payment-date">${formatDateShort(payment.date)}</div>
                <div class="payment-notes">${payment.notes || 'لا توجد ملاحظات'}</div>
            </div>
        `;
    }).join('');
}

// فلترة قائمة الديون
function filterDebtsList() {
    displayDebtsList();
}

// ترتيب قائمة الديون
function sortDebtsList() {
    displayDebtsList();
}

// دفعة سريعة
function quickPayment(customerId) {
    const customer = customers.find(c => c.id === customerId);
    if (!customer) return;

    const amount = prompt(`أدخل مبلغ الدفعة للعميل "${customer.name}":`);
    if (!amount || isNaN(amount) || parseFloat(amount) <= 0) {
        showNotification('يرجى إدخال مبلغ صحيح', 'error');
        return;
    }

    // إنشاء سجل الدفعة
    const payment = {
        id: generateId(),
        customerId: customerId,
        amount: parseFloat(amount),
        notes: 'دفعة سريعة',
        date: new Date().toISOString(),
        createdAt: new Date().toISOString()
    };

    // إضافة الدفعة
    customerPayments.push(payment);

    // تحديث رصيد العميل
    customer.currentBalance -= parseFloat(amount);
    customer.lastTransactionDate = new Date().toISOString();

    // حفظ البيانات
    saveData();

    // تحديث العرض
    refreshDebtsData();
    displayDebtsList();
    displayRecentPayments();

    showNotification('تم تسجيل الدفعة بنجاح', 'success');
}

// ===== إدارة المصروفات =====

// عرض قائمة المصروفات
function displayExpensesList() {
    const list = document.getElementById('expensesList');
    list.innerHTML = '';

    if (expenses.length === 0) {
        list.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">لا توجد مصروفات مسجلة</div>';
        return;
    }

    // ترتيب المصروفات حسب التاريخ (الأحدث أولاً)
    const sortedExpenses = [...expenses].sort((a, b) => new Date(b.date) - new Date(a.date));

    sortedExpenses.forEach(expense => {
        const item = document.createElement('div');
        item.className = 'expense-item';
        item.style.cssText = `
            background: #e0e5ec;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 15px;
            box-shadow: 6px 6px 12px #a3b1c6, -6px -6px 12px #ffffff;
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto;
            gap: 15px;
            align-items: center;
        `;

        const categoryNames = {
            'rent': 'إيجار',
            'utilities': 'مرافق',
            'supplies': 'مستلزمات',
            'maintenance': 'صيانة',
            'other': 'أخرى'
        };

        const categoryColors = {
            'rent': 'tag-danger',
            'utilities': 'tag-warning',
            'supplies': 'tag-info',
            'maintenance': 'tag-success',
            'other': 'tag-secondary'
        };

        item.innerHTML = `
            <div>
                <div style="font-weight: bold; margin-bottom: 5px;">${expense.description}</div>
                <div style="margin-bottom: 5px;">
                    <span class="tag ${categoryColors[expense.category] || 'tag-secondary'}">
                        ${categoryNames[expense.category] || expense.category}
                    </span>
                </div>
            </div>
            <div style="color: #e17055; font-weight: bold;">${formatCurrency(expense.amount)}</div>
            <div>${formatDateShort(expense.date)}</div>
            <div style="font-size: 12px; color: #666;">${formatDate(expense.createdAt)}</div>
            <div class="expense-actions">
                <button class="edit-btn" onclick="editExpense('${expense.id}')">تعديل</button>
                <button class="delete-btn" onclick="deleteExpense('${expense.id}')">حذف</button>
            </div>
        `;

        list.appendChild(item);
    });
}

// إظهار نموذج إضافة مصروف
function showAddExpenseForm() {
    document.getElementById('addExpenseForm').classList.remove('hidden');
    document.getElementById('expenseDescription').focus();
}

// إخفاء نموذج إضافة مصروف
function hideAddExpenseForm() {
    document.getElementById('addExpenseForm').classList.add('hidden');
    clearExpenseForm();
}

// مسح نموذج المصروف
function clearExpenseForm() {
    document.getElementById('expenseDescription').value = '';
    document.getElementById('expenseAmount').value = '';
    document.getElementById('expenseDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('expenseCategory').value = '';
}

// إضافة مصروف جديد
function addExpense() {
    const description = sanitizeInput(document.getElementById('expenseDescription').value);
    const amount = parseFloat(document.getElementById('expenseAmount').value);
    const date = document.getElementById('expenseDate').value;
    const category = document.getElementById('expenseCategory').value;

    // التحقق من صحة البيانات
    if (!validateInput(description, 'text')) {
        showNotification('يرجى إدخال وصف المصروف', 'error');
        return;
    }

    if (!validateInput(amount, 'number')) {
        showNotification('يرجى إدخال مبلغ صحيح', 'error');
        return;
    }

    if (!date) {
        showNotification('يرجى اختيار تاريخ المصروف', 'error');
        return;
    }

    if (!category) {
        showNotification('يرجى اختيار فئة المصروف', 'error');
        return;
    }

    // إضافة المصروف
    const expense = {
        id: generateId(),
        description,
        amount,
        date,
        category,
        createdAt: new Date().toISOString()
    };

    expenses.push(expense);
    saveData();
    displayExpensesList();
    hideAddExpenseForm();
    showNotification('تم إضافة المصروف بنجاح', 'success');
}

// تعديل مصروف
function editExpense(id) {
    const expense = expenses.find(e => e.id === id);
    if (!expense) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('expenseDescription').value = expense.description;
    document.getElementById('expenseAmount').value = expense.amount;
    document.getElementById('expenseDate').value = expense.date;
    document.getElementById('expenseCategory').value = expense.category;

    // إظهار النموذج
    showAddExpenseForm();

    // تغيير زر الإضافة إلى تحديث
    const addButton = document.querySelector('#addExpenseForm .form-actions .neumorphic-button.primary');
    addButton.textContent = 'تحديث المصروف';
    addButton.onclick = () => updateExpense(id);
}

// تحديث مصروف
function updateExpense(id) {
    const description = sanitizeInput(document.getElementById('expenseDescription').value);
    const amount = parseFloat(document.getElementById('expenseAmount').value);
    const date = document.getElementById('expenseDate').value;
    const category = document.getElementById('expenseCategory').value;

    // التحقق من صحة البيانات
    if (!validateInput(description, 'text') || !validateInput(amount, 'number') || !date || !category) {
        showNotification('يرجى ملء جميع الحقول بشكل صحيح', 'error');
        return;
    }

    // تحديث المصروف
    const expenseIndex = expenses.findIndex(e => e.id === id);
    if (expenseIndex !== -1) {
        expenses[expenseIndex] = {
            ...expenses[expenseIndex],
            description,
            amount,
            date,
            category,
            updatedAt: new Date().toISOString()
        };

        saveData();
        displayExpensesList();
        hideAddExpenseForm();

        // إعادة تعيين زر الإضافة
        const addButton = document.querySelector('#addExpenseForm .form-actions .neumorphic-button.primary');
        addButton.textContent = 'إضافة المصروف';
        addButton.onclick = addExpense;

        showNotification('تم تحديث المصروف بنجاح', 'success');
    }
}

// حذف مصروف
function deleteExpense(id) {
    const expense = expenses.find(e => e.id === id);
    if (!expense) return;

    if (confirm(`هل أنت متأكد من حذف المصروف "${expense.description}"؟`)) {
        expenses = expenses.filter(e => e.id !== id);
        saveData();
        displayExpensesList();
        showNotification('تم حذف المصروف بنجاح', 'success');
    }
}

// ===== التقارير =====

// إنشاء التقرير
function generateReport() {
    const period = document.getElementById('reportPeriod').value;
    const reportContent = document.getElementById('reportContent');

    let startDate, endDate;
    const now = new Date();

    switch(period) {
        case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
            break;
        case 'week':
            const weekStart = new Date(now);
            weekStart.setDate(now.getDate() - now.getDay());
            startDate = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate());
            endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 7);
            break;
        case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
            break;
        case 'custom':
            const startInput = document.getElementById('startDate').value;
            const endInput = document.getElementById('endDate').value;
            if (!startInput || !endInput) {
                reportContent.innerHTML = '<div style="text-align: center; color: #666;">يرجى اختيار تاريخ البداية والنهاية</div>';
                return;
            }
            startDate = new Date(startInput);
            endDate = new Date(endInput);
            endDate.setDate(endDate.getDate() + 1); // تضمين يوم النهاية
            break;
    }

    // فلترة المبيعات والمصروفات حسب الفترة
    const periodSales = sales.filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate >= startDate && saleDate < endDate;
    });

    const periodExpenses = expenses.filter(expense => {
        const expenseDate = new Date(expense.date);
        return expenseDate >= startDate && expenseDate < endDate;
    });

    // حساب الإحصائيات
    const totalSales = periodSales.reduce((sum, sale) => sum + sale.total, 0);
    const totalExpenses = periodExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    const salesCount = periodSales.length;

    // إنشاء التقرير
    reportContent.innerHTML = `
        <div class="report-summary">
            <div class="summary-card">
                <div class="summary-title">إجمالي المبيعات</div>
                <div class="summary-value">${formatCurrency(totalSales)}</div>
            </div>
            <div class="summary-card">
                <div class="summary-title">إجمالي المصروفات</div>
                <div class="summary-value negative">${formatCurrency(totalExpenses)}</div>
            </div>
            <div class="summary-card">
                <div class="summary-title">عدد المبيعات</div>
                <div class="summary-value">${salesCount}</div>
            </div>
        </div>

        ${periodSales.length > 0 ? `
            <h4 style="margin: 30px 0 15px 0; color: #333;">تفاصيل المبيعات</h4>
            <div style="background: #e0e5ec; padding: 15px; border-radius: 15px; box-shadow: inset 6px 6px 12px #a3b1c6, inset -6px -6px 12px #ffffff;">
                ${periodSales.map(sale => `
                    <div style="background: #e0e5ec; padding: 10px; margin-bottom: 10px; border-radius: 10px; box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff; display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 10px; align-items: center;">
                        <div>
                            <div style="font-weight: bold;">${sale.invoiceNumber}</div>
                            <div style="font-size: 12px; color: #666;">${formatDate(sale.date)}</div>
                        </div>
                        <div>المجموع: ${formatCurrency(sale.total)}</div>
                        <div>${sale.items ? sale.items.length : saleItems.filter(item => item.saleId === sale.id).length} عنصر</div>
                        <div>
                            <button onclick="openReturnModal('${sale.id}')" class="neumorphic-button" style="background: #ff6b6b; color: white; padding: 5px 10px; font-size: 12px;">مرتجع</button>
                        </div>
                    </div>
                `).join('')}
            </div>
        ` : ''}

        ${periodExpenses.length > 0 ? `
            <h4 style="margin: 30px 0 15px 0; color: #333;">تفاصيل المصروفات</h4>
            <div style="background: #e0e5ec; padding: 15px; border-radius: 15px; box-shadow: inset 6px 6px 12px #a3b1c6, inset -6px -6px 12px #ffffff;">
                ${periodExpenses.map(expense => `
                    <div style="background: #e0e5ec; padding: 10px; margin-bottom: 10px; border-radius: 10px; box-shadow: 4px 4px 8px #a3b1c6, -4px -4px 8px #ffffff; display: grid; grid-template-columns: 2fr 1fr 1fr; gap: 10px; align-items: center;">
                        <div>
                            <div style="font-weight: bold;">${expense.description}</div>
                            <div style="font-size: 12px; color: #666;">${expense.category}</div>
                        </div>
                        <div style="color: #e17055; font-weight: bold;">${formatCurrency(expense.amount)}</div>
                        <div style="font-size: 12px; color: #666;">${formatDateShort(expense.date)}</div>
                    </div>
                `).join('')}
            </div>
        ` : ''}
    `;
}

// ===== الفواتير =====

// إنشاء الفاتورة
function generateInvoice(sale) {
    const invoiceContent = document.getElementById('invoiceContent');

    let companyInfo = '';
    if (settings.showCompanyInInvoice) {
        companyInfo = `
            <div class="company-info" style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 15px;">
                <h2 style="margin: 0; color: #333;">${settings.companyName || 'اسم الشركة'}</h2>
                <div style="margin: 5px 0;">${settings.activity || 'النشاط'}</div>
                ${settings.commercialRegister ? `<div style="margin: 5px 0;">س.ت: ${settings.commercialRegister}</div>` : ''}
                ${settings.phone ? `<div style="margin: 5px 0;">هاتف: ${settings.phone}</div>` : ''}
                ${settings.address ? `<div style="margin: 5px 0;">العنوان: ${settings.address}</div>` : ''}
                ${settings.email ? `<div style="margin: 5px 0;">البريد: ${settings.email}</div>` : ''}
            </div>
        `;
    }

    invoiceContent.innerHTML = `
        ${companyInfo}
        <div class="invoice-header">
            <h2>فاتورة بيع</h2>
            <div style="margin-top: 10px;">
                <div>رقم الفاتورة: ${sale.invoiceNumber}</div>
                <div>التاريخ: ${formatDate(sale.date)}</div>
                <div>طريقة الدفع: <span class="tag ${sale.paymentMethod === 'cash' ? 'tag-cash' : 'tag-credit'}">${sale.paymentMethod === 'cash' ? 'نقداً' : 'على الحساب'}</span></div>
                ${sale.customerId ? `<div>العميل: ${customers.find(c => c.id === sale.customerId)?.name || 'غير محدد'}</div>` : ''}
            </div>
        </div>

        <div class="invoice-details">
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    ${sale.items.map(item => `
                        <tr>
                            <td>${item.name}</td>
                            <td>${item.quantity}</td>
                            <td>${formatCurrency(item.price)}</td>
                            <td>${formatCurrency(safeNumber(item.price * item.quantity, 0))}</td>
                            <td>${formatCurrency(safeNumber((item.price - item.costPrice) * item.quantity, 0))}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>

        <div class="invoice-total">
            <div style="margin-bottom: 5px;">المجموع الفرعي: ${formatCurrency(sale.subtotal)}</div>
            <div style="margin-bottom: 5px;">${getSalesTaxLabel()}: ${formatCurrency(sale.tax)}</div>
            <div style="margin-bottom: 10px; font-size: 20px; font-weight: bold;">المجموع النهائي: ${formatCurrency(sale.total)}</div>
        </div>

        <div style="text-align: center; margin-top: 20px; font-size: 14px; color: #666;">
            شكراً لتعاملكم معنا
        </div>
    `;

    // إظهار نافذة الفاتورة
    document.getElementById('invoiceModal').classList.remove('hidden');
}

// إغلاق نافذة الفاتورة
function closeInvoiceModal() {
    document.getElementById('invoiceModal').classList.add('hidden');
}

// طباعة الفاتورة
function printInvoice() {
    const invoiceContent = document.getElementById('invoiceContent').innerHTML;
    const printWindow = window.open('', '_blank');

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة بيع</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                .invoice-header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #333; padding-bottom: 15px; }
                .invoice-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                .invoice-table th, .invoice-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                .invoice-table th { background: #f5f5f5; font-weight: bold; }
                .invoice-total { text-align: right; font-size: 16px; margin-top: 20px; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            ${invoiceContent}
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

// ===== الإعدادات =====
// تم نقل وظائف الإعدادات إلى main.js

// تغيير كلمة المرور
function changePassword() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    // التحقق من كلمة المرور الحالية
    if (currentPassword !== settings.password) {
        showNotification('كلمة المرور الحالية غير صحيحة', 'error');
        return;
    }

    // التحقق من كلمة المرور الجديدة
    if (!newPassword || newPassword.length < 6) {
        showNotification('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل', 'error');
        return;
    }

    // التحقق من تطابق كلمة المرور
    if (newPassword !== confirmPassword) {
        showNotification('كلمة المرور الجديدة غير متطابقة', 'error');
        return;
    }

    // تحديث كلمة المرور
    settings.password = newPassword;
    saveData();

    // مسح الحقول
    document.getElementById('currentPassword').value = '';
    document.getElementById('newPassword').value = '';
    document.getElementById('confirmPassword').value = '';

    showNotification('تم تغيير كلمة المرور بنجاح', 'success');
}

// ===== وظائف إضافية =====

// البحث في المنتجات
function searchProducts(searchTerm) {
    const filteredProducts = searchArray(products, searchTerm, ['name', 'category', 'barcode']);
    displayFilteredProducts(filteredProducts);
}

// عرض المنتجات المفلترة
function displayFilteredProducts(filteredProducts) {
    const grid = document.getElementById('productsGrid');
    grid.innerHTML = '';

    filteredProducts.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = `product-card ${product.stockQuantity < 5 ? 'low-stock' : ''}`;
        productCard.onclick = () => addToCart(product.id);

        const stockStatus = product.stockQuantity <= 0 ? 'نفد' : product.stockQuantity <= 5 ? 'منخفض' : 'متوفر';
        const stockTagClass = product.stockQuantity <= 0 ? 'tag-danger' : product.stockQuantity <= 5 ? 'tag-warning' : 'tag-success';

        productCard.innerHTML = `
            <div class="product-name">${product.name}</div>
            <div class="product-price">${formatCurrency(product.sellPrice)}</div>
            <div class="product-stock">المتوفر: ${product.stockQuantity}</div>
            <div class="product-stock"><span class="tag ${stockTagClass}">${stockStatus}</span></div>
        `;

        grid.appendChild(productCard);
    });

    if (filteredProducts.length === 0) {
        grid.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">لا توجد منتجات تطابق البحث</div>';
    }
}

// إضافة شريط البحث للمنتجات
function addProductSearchBar() {
    const productsGrid = document.querySelector('.products-grid');
    const searchContainer = document.createElement('div');
    searchContainer.style.cssText = 'margin-bottom: 15px;';

    searchContainer.innerHTML = `
        <input type="text" id="productSearch" placeholder="البحث في المنتجات..."
               class="neumorphic-input" style="margin-bottom: 0;"
               oninput="searchProducts(this.value)">
    `;

    productsGrid.insertBefore(searchContainer, productsGrid.querySelector('.grid'));
}

// حساب إحصائيات سريعة
function getQuickStats() {
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    // مبيعات اليوم
    const todaySales = sales.filter(sale => new Date(sale.date) >= todayStart);
    const todayRevenue = todaySales.reduce((sum, sale) => sum + sale.total, 0);

    // إجمالي المنتجات
    const totalProducts = products.length;
    const lowStockProducts = products.filter(p => p.quantity < 5).length;

    // إجمالي العملاء
    const totalCustomers = customers.length;

    return {
        todayRevenue,
        todaySalesCount: todaySales.length,
        totalProducts,
        lowStockProducts,
        totalCustomers
    };
}

// عرض لوحة المعلومات السريعة
function showQuickDashboard() {
    const stats = getQuickStats();

    const dashboard = document.createElement('div');
    dashboard.id = 'quickDashboard';
    dashboard.style.cssText = `
        position: fixed;
        top: 80px;
        right: 20px;
        background: #e0e5ec;
        padding: 15px;
        border-radius: 15px;
        box-shadow: 6px 6px 12px #a3b1c6, -6px -6px 12px #ffffff;
        z-index: 1000;
        min-width: 250px;
        font-size: 12px;
    `;

    dashboard.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 10px; text-align: center;">إحصائيات سريعة</div>
        <div style="margin-bottom: 5px;">مبيعات اليوم: ${formatCurrency(stats.todayRevenue)}</div>
        <div style="margin-bottom: 5px;">عدد المبيعات: ${stats.todaySalesCount}</div>
        <div style="margin-bottom: 5px;">إجمالي المنتجات: ${stats.totalProducts}</div>
        <div style="margin-bottom: 5px;">مخزون منخفض: ${stats.lowStockProducts}</div>
        <div style="margin-bottom: 10px;">العملاء: ${stats.totalCustomers}</div>
        <button onclick="hideQuickDashboard()" style="width: 100%; padding: 5px; border: none; border-radius: 8px; background: #ff6b6b; color: white; cursor: pointer;">إغلاق</button>
    `;

    document.body.appendChild(dashboard);
}

// إخفاء لوحة المعلومات السريعة
function hideQuickDashboard() {
    const dashboard = document.getElementById('quickDashboard');
    if (dashboard) {
        dashboard.remove();
    }
}

// إضافة اختصارات لوحة المفاتيح
function addKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + S: حفظ البيانات
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveData();
            showNotification('تم حفظ البيانات', 'success');
        }

        // Ctrl + D: عرض لوحة المعلومات
        if (e.ctrlKey && e.key === 'd') {
            e.preventDefault();
            showQuickDashboard();
        }

        // Escape: إغلاق النوافذ المفتوحة
        if (e.key === 'Escape') {
            closeInvoiceModal();
            hideQuickDashboard();
            hideAddProductForm();
            hideAddCustomerForm();
            hideAddExpenseForm();
        }

        // F1: عرض المساعدة
        if (e.key === 'F1') {
            e.preventDefault();
            showHelp();
        }
    });
}

// عرض نافذة المساعدة
function showHelp() {
    const helpModal = document.createElement('div');
    helpModal.className = 'modal';
    helpModal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>المساعدة - اختصارات لوحة المفاتيح</h3>
                <button onclick="this.closest('.modal').remove()" class="close-btn">&times;</button>
            </div>
            <div style="padding: 20px;">
                <div style="margin-bottom: 10px;"><strong>Ctrl + S:</strong> حفظ البيانات</div>
                <div style="margin-bottom: 10px;"><strong>Ctrl + D:</strong> عرض الإحصائيات السريعة</div>
                <div style="margin-bottom: 10px;"><strong>Escape:</strong> إغلاق النوافذ المفتوحة</div>
                <div style="margin-bottom: 10px;"><strong>F1:</strong> عرض هذه المساعدة</div>
                <div style="margin-bottom: 10px;"><strong>Enter:</strong> تسجيل الدخول (في شاشة الدخول)</div>
            </div>
            <div class="modal-actions">
                <button onclick="this.closest('.modal').remove()" class="neumorphic-button">إغلاق</button>
            </div>
        </div>
    `;

    document.body.appendChild(helpModal);
}

// تهيئة الوظائف الإضافية
function initializeAdvancedFeatures() {
    // إضافة شريط البحث
    setTimeout(() => {
        addProductSearchBar();
    }, 1000);

    // إضافة اختصارات لوحة المفاتيح
    addKeyboardShortcuts();

    // إضافة أزرار الأدوات المتقدمة للإعدادات
    setTimeout(() => {
        addAdvancedResetOptions();
    }, 1500);
}

// تشغيل الوظائف الإضافية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initializeAdvancedFeatures();
    }, 2000);
});

// عرض وصفة المنتج
function viewProductRecipe(productId) {
    const product = products.find(p => p.id === productId);
    if (!product || !product.hasRecipe) {
        showNotification('هذا المنتج لا يحتوي على وصفة', 'error');
        return;
    }

    const recipe = productRecipes.find(r => r.productId === productId);
    if (!recipe) {
        showNotification('لم يتم العثور على الوصفة', 'error');
        return;
    }

    // التبديل إلى تبويب الوصفات وعرض الوصفة
    showTab('recipes');
    if (typeof editRecipe === 'function') {
        editRecipe(recipe.id);
    }
}

// إنشاء وصفة جديدة للمنتج
function createRecipeForProduct(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) {
        showNotification('المنتج غير موجود', 'error');
        return;
    }

    // التبديل إلى تبويب الوصفات وإنشاء وصفة جديدة
    showTab('recipes');
    if (typeof showAddRecipeForm === 'function') {
        showAddRecipeForm();
        // تعيين المنتج مسبقاً
        setTimeout(() => {
            const recipeProductSelect = document.getElementById('recipeProduct');
            if (recipeProductSelect) {
                recipeProductSelect.value = productId;
            }
        }, 100);
    }
}
