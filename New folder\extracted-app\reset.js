// ===== وظائف إعادة الضبط =====

// إعادة ضبط جميع البيانات
function resetAllData() {
    const confirmation = prompt('تحذير: هذا الإجراء سيحذف جميع البيانات نهائياً!\n\nلتأكيد العملية، اكتب "حذف البيانات" في الحقل أدناه:');
    
    if (confirmation === 'حذف البيانات') {
        // مسح جميع البيانات
        products.length = 0;
        customers.length = 0;
        expenses.length = 0;
        sales.length = 0;
        cart.length = 0;
        
        // إعادة تعيين الإعدادات للقيم الافتراضية
        settings = {
            // إعدادات الشركة
            companyName: 'اسم الشركة',
            activity: 'النشاط',
            commercialRegister: '',
            phone: '',
            address: '',
            email: '',
            customField1: '',
            customField2: '',

            // إعدادات العملة
            mainCurrency: 'ريال سعودي',
            subCurrency: 'هللة',
            currencySymbol: 'ر.س',

            // إعدادات الضريبة
            isTaxable: true,
            taxRate: 0.15,
            companyTaxNumber: '',

            // إعدادات النظام
            password: 'admin123'
        };
        
        // إضافة بيانات تجريبية
        addSampleData();
        
        // حفظ البيانات
        saveData();
        
        // تحديث العرض
        initializeApp();
        
        showNotification('تم إعادة ضبط جميع البيانات بنجاح', 'success');
        
        // العودة لتبويب المبيعات
        showTab('sales');
    } else if (confirmation !== null) {
        showNotification('لم يتم تأكيد العملية بشكل صحيح', 'error');
    }
}

// إضافة بيانات تجريبية
function addSampleData() {
    // منتجات تجريبية
    products.push(
        {
            id: generateId(),
            name: 'قلم أزرق',
            category: 'قرطاسية',
            costPrice: 1.50,
            sellPrice: 2.00,
            quantity: 50,
            barcode: '123456789',
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            name: 'دفتر A4',
            category: 'قرطاسية',
            costPrice: 8.00,
            sellPrice: 12.00,
            quantity: 3,
            barcode: '987654321',
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            name: 'مبراة',
            category: 'قرطاسية',
            costPrice: 0.75,
            sellPrice: 1.25,
            quantity: 25,
            barcode: '456789123',
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            name: 'مسطرة 30 سم',
            category: 'قرطاسية',
            costPrice: 2.00,
            sellPrice: 3.50,
            quantity: 15,
            barcode: '789123456',
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            name: 'ممحاة',
            category: 'قرطاسية',
            costPrice: 0.50,
            sellPrice: 1.00,
            quantity: 30,
            barcode: '321654987',
            createdAt: new Date().toISOString()
        }
    );
    
    // عملاء تجريبيين
    customers.push(
        {
            id: generateId(),
            name: 'أحمد محمد',
            phone: '0501234567',
            email: '<EMAIL>',
            address: 'الرياض، المملكة العربية السعودية',
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            name: 'فاطمة علي',
            phone: '0507654321',
            email: '<EMAIL>',
            address: 'جدة، المملكة العربية السعودية',
            createdAt: new Date().toISOString()
        }
    );
    
    // مصروفات تجريبية
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    expenses.push(
        {
            id: generateId(),
            description: 'فاتورة كهرباء',
            amount: 250.00,
            date: yesterday.toISOString().split('T')[0],
            category: 'utilities',
            createdAt: new Date().toISOString()
        },
        {
            id: generateId(),
            description: 'شراء أقلام جديدة',
            amount: 75.00,
            date: today.toISOString().split('T')[0],
            category: 'supplies',
            createdAt: new Date().toISOString()
        }
    );
}

// إعادة ضبط المنتجات فقط
function resetProducts() {
    if (confirm('هل أنت متأكد من حذف جميع المنتجات؟')) {
        products.length = 0;
        cart.length = 0;
        saveData();
        displayProducts();
        displayProductsList();
        updateCart();
        showNotification('تم حذف جميع المنتجات', 'success');
    }
}

// إعادة ضبط العملاء فقط
function resetCustomers() {
    if (confirm('هل أنت متأكد من حذف جميع العملاء؟')) {
        customers.length = 0;
        saveData();
        displayCustomersList();
        showNotification('تم حذف جميع العملاء', 'success');
    }
}

// إعادة ضبط المصروفات فقط
function resetExpenses() {
    if (confirm('هل أنت متأكد من حذف جميع المصروفات؟')) {
        expenses.length = 0;
        saveData();
        displayExpensesList();
        showNotification('تم حذف جميع المصروفات', 'success');
    }
}

// إعادة ضبط المبيعات فقط
function resetSales() {
    if (confirm('هل أنت متأكد من حذف جميع المبيعات؟ هذا سيؤثر على التقارير المالية.')) {
        sales.length = 0;
        saveData();
        generateReport();
        showNotification('تم حذف جميع المبيعات', 'success');
    }
}

// إعادة ضبط السلة فقط
function resetCart() {
    clearCart();
}

// إعادة ضبط الإعدادات للقيم الافتراضية
function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة ضبط الإعدادات للقيم الافتراضية؟')) {
        settings = {
            // إعدادات الشركة
            companyName: 'اسم الشركة',
            activity: 'النشاط',
            commercialRegister: '',
            phone: '',
            address: '',
            email: '',
            customField1: '',
            customField2: '',

            // إعدادات العملة
            mainCurrency: 'ريال سعودي',
            subCurrency: 'هللة',
            currencySymbol: 'ر.س',

            // إعدادات الضريبة
            isTaxable: true,
            taxRate: 0.15,
            companyTaxNumber: '',

            // إعدادات النظام
            password: 'admin123'
        };

        // تحديث حقول الإعدادات
        updateSettingsForm();

        saveData();
        showNotification('تم إعادة ضبط الإعدادات للقيم الافتراضية', 'success');
    }
}

// تصدير البيانات كنسخة احتياطية
function createBackup() {
    const data = {
        products,
        customers,
        expenses,
        sales,
        settings: { ...settings, password: undefined }, // استبعاد كلمة المرور
        exportDate: new Date().toISOString(),
        version: '1.0'
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `pos_backup_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
}

// استيراد البيانات من نسخة احتياطية
function restoreBackup() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = function(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = JSON.parse(e.target.result);
                
                // التحقق من صحة البيانات
                if (!data.version || !data.exportDate) {
                    showNotification('ملف النسخة الاحتياطية غير صحيح', 'error');
                    return;
                }
                
                if (confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
                    // استعادة البيانات
                    if (data.products) products.splice(0, products.length, ...data.products);
                    if (data.customers) customers.splice(0, customers.length, ...data.customers);
                    if (data.expenses) expenses.splice(0, expenses.length, ...data.expenses);
                    if (data.sales) sales.splice(0, sales.length, ...data.sales);
                    if (data.settings) settings = { ...settings, ...data.settings };
                    
                    // مسح السلة
                    cart.length = 0;
                    
                    saveData();
                    initializeApp();
                    showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                }
            } catch (error) {
                console.error('خطأ في استعادة النسخة الاحتياطية:', error);
                showNotification('خطأ في تنسيق ملف النسخة الاحتياطية', 'error');
            }
        };
        reader.readAsText(file);
    };
    
    input.click();
}

// مسح البيانات المحفوظة في المتصفح
function clearBrowserData() {
    const confirmation = prompt('تحذير: هذا سيحذف جميع البيانات المحفوظة في المتصفح!\n\nلتأكيد العملية، اكتب "مسح البيانات" في الحقل أدناه:');
    
    if (confirmation === 'مسح البيانات') {
        // مسح localStorage
        localStorage.removeItem('pos_products');
        localStorage.removeItem('pos_customers');
        localStorage.removeItem('pos_expenses');
        localStorage.removeItem('pos_sales');
        localStorage.removeItem('pos_settings');
        localStorage.removeItem('pos_cart');
        
        showNotification('تم مسح جميع البيانات من المتصفح', 'success');
        
        // إعادة تحميل الصفحة
        setTimeout(() => {
            location.reload();
        }, 2000);
    } else if (confirmation !== null) {
        showNotification('لم يتم تأكيد العملية بشكل صحيح', 'error');
    }
}

// إضافة أزرار إضافية للإعدادات (يمكن استخدامها في المستقبل)
function addAdvancedResetOptions() {
    const settingsContainer = document.querySelector('.settings-container');
    
    // إضافة قسم الأدوات المتقدمة
    const advancedSection = document.createElement('div');
    advancedSection.className = 'settings-section';
    advancedSection.innerHTML = `
        <h4>أدوات متقدمة</h4>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <button onclick="createBackup()" class="neumorphic-button">إنشاء نسخة احتياطية</button>
            <button onclick="restoreBackup()" class="neumorphic-button">استعادة نسخة احتياطية</button>
            <button onclick="resetProducts()" class="neumorphic-button secondary">حذف المنتجات</button>
            <button onclick="resetCustomers()" class="neumorphic-button secondary">حذف العملاء</button>
            <button onclick="resetExpenses()" class="neumorphic-button secondary">حذف المصروفات</button>
            <button onclick="resetSales()" class="neumorphic-button secondary">حذف المبيعات</button>
            <button onclick="resetSettings()" class="neumorphic-button secondary">إعادة ضبط الإعدادات</button>
            <button onclick="clearBrowserData()" class="neumorphic-button danger">مسح بيانات المتصفح</button>
        </div>
    `;
    
    settingsContainer.appendChild(advancedSection);
}
