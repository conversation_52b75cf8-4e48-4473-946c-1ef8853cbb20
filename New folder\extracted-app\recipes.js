// ===== إدارة وصفات المنتجات (الرُسبي) =====

// متغيرات الوصفات
let currentRecipeIngredients = [];
let editingRecipeId = null;

// تهيئة صفحة الوصفات
function initializeRecipes() {
    displayRecipesList();
    loadProductsForRecipes();
    clearRecipeForm();

    // تعيين التواريخ الافتراضية للتقارير
    const today = new Date().toISOString().split('T')[0];
    const monthAgo = new Date(Date.now() - 30*24*60*60*1000).toISOString().split('T')[0];

    const startDateInput = document.getElementById('reportStartDate');
    const endDateInput = document.getElementById('reportEndDate');

    if (startDateInput) startDateInput.value = monthAgo;
    if (endDateInput) endDateInput.value = today;

    // تهيئة التحقق من المكونات
    initializeIngredientsStockCheck();
}

// عرض قائمة الوصفات
function displayRecipesList() {
    const container = document.getElementById('recipesList');
    if (!container) return;

    let html = `
        <div class="recipes-header">
            <h3>قائمة الوصفات</h3>
            <button onclick="showAddRecipeForm()" class="neumorphic-button primary">إضافة وصفة جديدة</button>
        </div>
    `;

    if (productRecipes.length === 0) {
        html += '<div class="empty-state">لا توجد وصفات محفوظة</div>';
    } else {
        html += '<div class="recipes-grid">';
        productRecipes.forEach(recipe => {
            const product = products.find(p => p.id === recipe.productId);
            const productName = product ? product.name : 'منتج محذوف';
            
            html += `
                <div class="recipe-card">
                    <div class="recipe-header">
                        <h4>${recipe.name}</h4>
                        <span class="recipe-product">المنتج: ${productName}</span>
                    </div>
                    <div class="recipe-ingredients">
                        <h5>المكونات:</h5>
                        <ul>
            `;
            
            recipe.ingredients.forEach(ingredient => {
                const ingredientProduct = products.find(p => p.id === ingredient.productId);
                const ingredientName = ingredientProduct ? ingredientProduct.name : 'مكون محذوف';
                html += `<li>${ingredient.quantity} ${ingredient.unit} من ${ingredientName}</li>`;
            });
            
            html += `
                        </ul>
                    </div>
                    <div class="recipe-actions">
                        <button onclick="editRecipe('${recipe.id}')" class="neumorphic-button secondary">تعديل</button>
                        <button onclick="deleteRecipe('${recipe.id}')" class="neumorphic-button danger">حذف</button>
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }

    container.innerHTML = html;
}

// إظهار نموذج إضافة وصفة
function showAddRecipeForm() {
    editingRecipeId = null;
    currentRecipeIngredients = [];
    clearRecipeForm();
    document.getElementById('recipeFormModal').classList.remove('hidden');
    document.getElementById('recipeFormTitle').textContent = 'إضافة وصفة جديدة';
}

// إخفاء نموذج الوصفة
function hideRecipeForm() {
    document.getElementById('recipeFormModal').classList.add('hidden');
    clearRecipeForm();
}

// مسح نموذج الوصفة
function clearRecipeForm() {
    document.getElementById('recipeName').value = '';
    document.getElementById('recipeProduct').value = '';
    currentRecipeIngredients = [];
    updateIngredientsDisplay();
}

// تحميل المنتجات في القوائم المنسدلة
function loadProductsForRecipes() {
    const recipeProductSelect = document.getElementById('recipeProduct');
    const ingredientProductSelect = document.getElementById('ingredientProduct');
    
    if (recipeProductSelect) {
        recipeProductSelect.innerHTML = '<option value="">اختر المنتج النهائي</option>';
        products.forEach(product => {
            const option = document.createElement('option');
            option.value = product.id;
            option.textContent = `${product.name} - ${product.stockQuantity} ${product.unit}`;
            recipeProductSelect.appendChild(option);
        });
    }
    
    if (ingredientProductSelect) {
        ingredientProductSelect.innerHTML = '<option value="">اختر المكون</option>';
        products.forEach(product => {
            const option = document.createElement('option');
            option.value = product.id;
            option.textContent = `${product.name} - متوفر: ${product.stockQuantity} ${product.unit}`;
            ingredientProductSelect.appendChild(option);
        });
    }
}

// إضافة مكون للوصفة
function addIngredientToRecipe() {
    const productId = document.getElementById('ingredientProduct').value;
    const quantity = parseFloat(document.getElementById('ingredientQuantity').value);
    const unit = document.getElementById('ingredientUnit').value.trim();

    if (!productId || !quantity || quantity <= 0 || !unit) {
        showNotification('يرجى إدخال جميع بيانات المكون', 'error');
        return;
    }

    const product = products.find(p => p.id === productId);
    if (!product) {
        showNotification('المنتج المحدد غير موجود', 'error');
        return;
    }

    // التحقق من عدم تكرار المكون
    const existingIngredient = currentRecipeIngredients.find(ing => ing.productId === productId);
    if (existingIngredient) {
        showNotification('هذا المكون موجود بالفعل في الوصفة', 'error');
        return;
    }

    // إضافة المكون
    currentRecipeIngredients.push({
        productId: productId,
        productName: product.name,
        quantity: quantity,
        unit: unit
    });

    // مسح الحقول
    document.getElementById('ingredientProduct').value = '';
    document.getElementById('ingredientQuantity').value = '';
    document.getElementById('ingredientUnit').value = '';

    updateIngredientsDisplay();
}

// تحديث عرض المكونات
function updateIngredientsDisplay() {
    const container = document.getElementById('currentIngredients');
    if (!container) return;

    if (currentRecipeIngredients.length === 0) {
        container.innerHTML = '<div class="empty-ingredients">لم يتم إضافة مكونات بعد</div>';
        return;
    }

    let html = '<div class="ingredients-list">';
    currentRecipeIngredients.forEach((ingredient, index) => {
        html += `
            <div class="ingredient-item">
                <span class="ingredient-info">
                    ${ingredient.quantity} ${ingredient.unit} من ${ingredient.productName}
                </span>
                <button onclick="removeIngredient(${index})" class="btn-remove">×</button>
            </div>
        `;
    });
    html += '</div>';

    container.innerHTML = html;
}

// حذف مكون من الوصفة
function removeIngredient(index) {
    currentRecipeIngredients.splice(index, 1);
    updateIngredientsDisplay();
}

// حفظ الوصفة
function saveRecipe() {
    const name = document.getElementById('recipeName').value.trim();
    const productId = document.getElementById('recipeProduct').value;

    if (!name) {
        showNotification('يرجى إدخال اسم الوصفة', 'error');
        return;
    }

    if (!productId) {
        showNotification('يرجى اختيار المنتج النهائي', 'error');
        return;
    }

    if (currentRecipeIngredients.length === 0) {
        showNotification('يرجى إضافة مكون واحد على الأقل', 'error');
        return;
    }

    // التحقق من عدم وجود وصفة أخرى لنفس المنتج
    const existingRecipe = productRecipes.find(r => 
        r.productId === productId && r.id !== editingRecipeId
    );
    if (existingRecipe) {
        showNotification('يوجد وصفة أخرى لهذا المنتج بالفعل', 'error');
        return;
    }

    const recipe = {
        id: editingRecipeId || generateId(),
        productId: productId,
        name: name,
        ingredients: [...currentRecipeIngredients],
        createdAt: editingRecipeId ? 
            productRecipes.find(r => r.id === editingRecipeId).createdAt : 
            new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    if (editingRecipeId) {
        const index = productRecipes.findIndex(r => r.id === editingRecipeId);
        if (index !== -1) {
            productRecipes[index] = recipe;
        }
    } else {
        productRecipes.push(recipe);
    }

    // تحديث خاصية hasRecipe للمنتج
    const product = products.find(p => p.id === productId);
    if (product) {
        product.hasRecipe = true;
        product.recipeId = recipe.id;
    }

    saveData();
    displayRecipesList();
    hideRecipeForm();
    
    showNotification(editingRecipeId ? 'تم تحديث الوصفة بنجاح' : 'تم إضافة الوصفة بنجاح', 'success');
}

// تعديل وصفة
function editRecipe(recipeId) {
    const recipe = productRecipes.find(r => r.id === recipeId);
    if (!recipe) return;

    editingRecipeId = recipeId;
    currentRecipeIngredients = [...recipe.ingredients];

    document.getElementById('recipeName').value = recipe.name;
    document.getElementById('recipeProduct').value = recipe.productId;
    
    updateIngredientsDisplay();
    document.getElementById('recipeFormModal').classList.remove('hidden');
    document.getElementById('recipeFormTitle').textContent = 'تعديل الوصفة';
}

// حذف وصفة
function deleteRecipe(recipeId) {
    if (!confirm('هل أنت متأكد من حذف هذه الوصفة؟')) return;

    const recipe = productRecipes.find(r => r.id === recipeId);
    if (recipe) {
        // إزالة خاصية hasRecipe من المنتج
        const product = products.find(p => p.id === recipe.productId);
        if (product) {
            product.hasRecipe = false;
            delete product.recipeId;
        }
    }

    const index = productRecipes.findIndex(r => r.id === recipeId);
    if (index !== -1) {
        productRecipes.splice(index, 1);
        saveData();
        displayRecipesList();
        showNotification('تم حذف الوصفة بنجاح', 'success');
    }
}

// ===== تقارير الاستهلاك والإنتاج =====

// إنشاء تقرير استهلاك المواد الخام
function generateConsumptionReport(startDate, endDate) {
    const consumptionData = {};

    // فلترة المبيعات حسب الفترة
    const periodSales = sales.filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate >= new Date(startDate) && saleDate <= new Date(endDate);
    });

    // حساب الاستهلاك لكل مبيعة
    periodSales.forEach(sale => {
        sale.items.forEach(item => {
            const product = products.find(p => p.id === item.productId);
            if (product && product.hasRecipe) {
                const recipe = productRecipes.find(r => r.productId === product.id);
                if (recipe) {
                    recipe.ingredients.forEach(ingredient => {
                        const ingredientProduct = products.find(p => p.id === ingredient.productId);
                        if (ingredientProduct) {
                            const consumedAmount = ingredient.quantity * item.quantity;
                            const key = ingredient.productId;

                            if (!consumptionData[key]) {
                                consumptionData[key] = {
                                    name: ingredientProduct.name,
                                    unit: ingredient.unit || ingredientProduct.unit,
                                    totalConsumed: 0,
                                    costPrice: ingredientProduct.costPrice,
                                    totalCost: 0
                                };
                            }

                            consumptionData[key].totalConsumed += consumedAmount;
                            consumptionData[key].totalCost += consumedAmount * ingredientProduct.costPrice;
                        }
                    });
                }
            }
        });
    });

    return Object.values(consumptionData);
}

// إنشاء تقرير الإنتاج
function generateProductionReport(startDate, endDate) {
    const productionData = {};

    // فلترة المبيعات حسب الفترة
    const periodSales = sales.filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate >= new Date(startDate) && saleDate <= new Date(endDate);
    });

    // حساب الإنتاج لكل منتج
    periodSales.forEach(sale => {
        sale.items.forEach(item => {
            const product = products.find(p => p.id === item.productId);
            if (product && product.hasRecipe) {
                const key = product.id;

                if (!productionData[key]) {
                    productionData[key] = {
                        name: product.name,
                        unit: product.unit,
                        totalProduced: 0,
                        sellPrice: product.sellPrice,
                        totalRevenue: 0,
                        hasRecipe: true
                    };
                }

                productionData[key].totalProduced += item.quantity;
                productionData[key].totalRevenue += item.quantity * item.price;
            }
        });
    });

    return Object.values(productionData);
}

// عرض تقرير الاستهلاك
function displayConsumptionReport() {
    const startDate = document.getElementById('reportStartDate')?.value || new Date(Date.now() - 30*24*60*60*1000).toISOString().split('T')[0];
    const endDate = document.getElementById('reportEndDate')?.value || new Date().toISOString().split('T')[0];

    const consumptionData = generateConsumptionReport(startDate, endDate);
    const container = document.getElementById('consumptionReportContainer');

    if (!container) return;

    if (consumptionData.length === 0) {
        container.innerHTML = '<div class="empty-state">لا توجد بيانات استهلاك في هذه الفترة</div>';
        return;
    }

    let html = `
        <div class="report-header">
            <h4>تقرير استهلاك المواد الخام</h4>
            <p>من ${formatDate(startDate)} إلى ${formatDate(endDate)}</p>
        </div>
        <div class="report-table">
            <table>
                <thead>
                    <tr>
                        <th>المادة الخام</th>
                        <th>الكمية المستهلكة</th>
                        <th>الوحدة</th>
                        <th>التكلفة الإجمالية</th>
                    </tr>
                </thead>
                <tbody>
    `;

    let totalCost = 0;
    consumptionData.forEach(item => {
        totalCost += item.totalCost;
        html += `
            <tr>
                <td>${item.name}</td>
                <td>${item.totalConsumed.toFixed(2)}</td>
                <td>${item.unit}</td>
                <td>${formatCurrency(item.totalCost)}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
                <tfoot>
                    <tr>
                        <th colspan="3">الإجمالي</th>
                        <th>${formatCurrency(totalCost)}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    container.innerHTML = html;
}

// عرض تقرير الإنتاج
function displayProductionReport() {
    const startDate = document.getElementById('reportStartDate')?.value || new Date(Date.now() - 30*24*60*60*1000).toISOString().split('T')[0];
    const endDate = document.getElementById('reportEndDate')?.value || new Date().toISOString().split('T')[0];

    const productionData = generateProductionReport(startDate, endDate);
    const container = document.getElementById('productionReportContainer');

    if (!container) return;

    if (productionData.length === 0) {
        container.innerHTML = '<div class="empty-state">لا توجد بيانات إنتاج في هذه الفترة</div>';
        return;
    }

    let html = `
        <div class="report-header">
            <h4>تقرير الإنتاج</h4>
            <p>من ${formatDate(startDate)} إلى ${formatDate(endDate)}</p>
        </div>
        <div class="report-table">
            <table>
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>الكمية المنتجة</th>
                        <th>الوحدة</th>
                        <th>الإيرادات</th>
                    </tr>
                </thead>
                <tbody>
    `;

    let totalRevenue = 0;
    let totalQuantity = 0;
    productionData.forEach(item => {
        totalRevenue += item.totalRevenue;
        totalQuantity += item.totalProduced;
        html += `
            <tr>
                <td>${item.name}</td>
                <td>${item.totalProduced.toFixed(2)}</td>
                <td>${item.unit}</td>
                <td>${formatCurrency(item.totalRevenue)}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
                <tfoot>
                    <tr>
                        <th>الإجمالي</th>
                        <th>${totalQuantity.toFixed(2)}</th>
                        <th>-</th>
                        <th>${formatCurrency(totalRevenue)}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;

    container.innerHTML = html;
}

// ===== إشعارات نفاد المكونات =====

// التحقق من نفاد مكونات الوصفات
function checkRecipeIngredientsStock() {
    const lowStockIngredients = [];
    const outOfStockIngredients = [];

    productRecipes.forEach(recipe => {
        const product = products.find(p => p.id === recipe.productId);
        if (!product) return;

        recipe.ingredients.forEach(ingredient => {
            const ingredientProduct = products.find(p => p.id === ingredient.productId);
            if (!ingredientProduct) return;

            const stockLevel = ingredientProduct.stockQuantity;
            const minRequired = ingredient.quantity; // الحد الأدنى المطلوب لوحدة واحدة

            if (stockLevel <= 0) {
                outOfStockIngredients.push({
                    recipeName: recipe.name,
                    productName: product.name,
                    ingredientName: ingredientProduct.name,
                    required: minRequired,
                    available: stockLevel,
                    unit: ingredient.unit || ingredientProduct.unit
                });
            } else if (stockLevel < minRequired * 5) { // تحذير عند وجود أقل من 5 وحدات
                lowStockIngredients.push({
                    recipeName: recipe.name,
                    productName: product.name,
                    ingredientName: ingredientProduct.name,
                    required: minRequired,
                    available: stockLevel,
                    unit: ingredient.unit || ingredientProduct.unit,
                    canMake: Math.floor(stockLevel / minRequired)
                });
            }
        });
    });

    return { lowStockIngredients, outOfStockIngredients };
}

// عرض إشعارات نفاد المكونات
function displayIngredientsStockAlerts() {
    const { lowStockIngredients, outOfStockIngredients } = checkRecipeIngredientsStock();
    const alertContainer = document.getElementById('ingredientsStockAlert');

    if (!alertContainer) return;

    if (outOfStockIngredients.length === 0 && lowStockIngredients.length === 0) {
        alertContainer.style.display = 'none';
        return;
    }

    let alertHtml = '<div class="stock-alerts">';

    if (outOfStockIngredients.length > 0) {
        alertHtml += `
            <div class="alert alert-danger">
                <h5>⚠️ مكونات نفدت من المخزون:</h5>
                <ul>
        `;
        outOfStockIngredients.forEach(item => {
            alertHtml += `
                <li>
                    <strong>${item.ingredientName}</strong>
                    (مطلوب لوصفة ${item.recipeName})
                    - لا يمكن تحضير ${item.productName}
                </li>
            `;
        });
        alertHtml += '</ul></div>';
    }

    if (lowStockIngredients.length > 0) {
        alertHtml += `
            <div class="alert alert-warning">
                <h5>⚡ مكونات بمخزون منخفض:</h5>
                <ul>
        `;
        lowStockIngredients.forEach(item => {
            alertHtml += `
                <li>
                    <strong>${item.ingredientName}</strong>
                    (متوفر: ${item.available} ${item.unit})
                    - يمكن تحضير ${item.canMake} وحدة من ${item.productName}
                </li>
            `;
        });
        alertHtml += '</ul></div>';
    }

    alertHtml += '</div>';
    alertContainer.innerHTML = alertHtml;
    alertContainer.style.display = 'block';
}

// إضافة التحقق من المكونات إلى التهيئة
function initializeIngredientsStockCheck() {
    // إنشاء عنصر الإشعارات إذا لم يكن موجوداً
    let alertContainer = document.getElementById('ingredientsStockAlert');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'ingredientsStockAlert';
        alertContainer.className = 'ingredients-stock-alert';
        alertContainer.style.display = 'none';

        // إدراج الإشعارات في بداية صفحة الوصفات
        const recipesContainer = document.querySelector('.recipes-container');
        if (recipesContainer) {
            recipesContainer.insertBefore(alertContainer, recipesContainer.firstChild.nextSibling);
        }
    }

    // عرض الإشعارات
    displayIngredientsStockAlerts();
}
