// ===== إدارة وصفات المنتجات (الرُسبي) =====

// متغيرات الوصفات
let currentRecipeIngredients = [];
let editingRecipeId = null;

// تهيئة صفحة الوصفات
function initializeRecipes() {
    displayRecipesList();
    loadProductsForRecipes();
    clearRecipeForm();
}

// عرض قائمة الوصفات
function displayRecipesList() {
    const container = document.getElementById('recipesList');
    if (!container) return;

    let html = `
        <div class="recipes-header">
            <h3>قائمة الوصفات</h3>
            <button onclick="showAddRecipeForm()" class="neumorphic-button primary">إضافة وصفة جديدة</button>
        </div>
    `;

    if (productRecipes.length === 0) {
        html += '<div class="empty-state">لا توجد وصفات محفوظة</div>';
    } else {
        html += '<div class="recipes-grid">';
        productRecipes.forEach(recipe => {
            const product = products.find(p => p.id === recipe.productId);
            const productName = product ? product.name : 'منتج محذوف';
            
            html += `
                <div class="recipe-card">
                    <div class="recipe-header">
                        <h4>${recipe.name}</h4>
                        <span class="recipe-product">المنتج: ${productName}</span>
                    </div>
                    <div class="recipe-ingredients">
                        <h5>المكونات:</h5>
                        <ul>
            `;
            
            recipe.ingredients.forEach(ingredient => {
                const ingredientProduct = products.find(p => p.id === ingredient.productId);
                const ingredientName = ingredientProduct ? ingredientProduct.name : 'مكون محذوف';
                html += `<li>${ingredient.quantity} ${ingredient.unit} من ${ingredientName}</li>`;
            });
            
            html += `
                        </ul>
                    </div>
                    <div class="recipe-actions">
                        <button onclick="editRecipe('${recipe.id}')" class="neumorphic-button secondary">تعديل</button>
                        <button onclick="deleteRecipe('${recipe.id}')" class="neumorphic-button danger">حذف</button>
                    </div>
                </div>
            `;
        });
        html += '</div>';
    }

    container.innerHTML = html;
}

// إظهار نموذج إضافة وصفة
function showAddRecipeForm() {
    editingRecipeId = null;
    currentRecipeIngredients = [];
    clearRecipeForm();
    document.getElementById('recipeFormModal').classList.remove('hidden');
    document.getElementById('recipeFormTitle').textContent = 'إضافة وصفة جديدة';
}

// إخفاء نموذج الوصفة
function hideRecipeForm() {
    document.getElementById('recipeFormModal').classList.add('hidden');
    clearRecipeForm();
}

// مسح نموذج الوصفة
function clearRecipeForm() {
    document.getElementById('recipeName').value = '';
    document.getElementById('recipeProduct').value = '';
    currentRecipeIngredients = [];
    updateIngredientsDisplay();
}

// تحميل المنتجات في القوائم المنسدلة
function loadProductsForRecipes() {
    const recipeProductSelect = document.getElementById('recipeProduct');
    const ingredientProductSelect = document.getElementById('ingredientProduct');
    
    if (recipeProductSelect) {
        recipeProductSelect.innerHTML = '<option value="">اختر المنتج النهائي</option>';
        products.forEach(product => {
            const option = document.createElement('option');
            option.value = product.id;
            option.textContent = `${product.name} - ${product.stockQuantity} ${product.unit}`;
            recipeProductSelect.appendChild(option);
        });
    }
    
    if (ingredientProductSelect) {
        ingredientProductSelect.innerHTML = '<option value="">اختر المكون</option>';
        products.forEach(product => {
            const option = document.createElement('option');
            option.value = product.id;
            option.textContent = `${product.name} - متوفر: ${product.stockQuantity} ${product.unit}`;
            ingredientProductSelect.appendChild(option);
        });
    }
}

// إضافة مكون للوصفة
function addIngredientToRecipe() {
    const productId = document.getElementById('ingredientProduct').value;
    const quantity = parseFloat(document.getElementById('ingredientQuantity').value);
    const unit = document.getElementById('ingredientUnit').value.trim();

    if (!productId || !quantity || quantity <= 0 || !unit) {
        showNotification('يرجى إدخال جميع بيانات المكون', 'error');
        return;
    }

    const product = products.find(p => p.id === productId);
    if (!product) {
        showNotification('المنتج المحدد غير موجود', 'error');
        return;
    }

    // التحقق من عدم تكرار المكون
    const existingIngredient = currentRecipeIngredients.find(ing => ing.productId === productId);
    if (existingIngredient) {
        showNotification('هذا المكون موجود بالفعل في الوصفة', 'error');
        return;
    }

    // إضافة المكون
    currentRecipeIngredients.push({
        productId: productId,
        productName: product.name,
        quantity: quantity,
        unit: unit
    });

    // مسح الحقول
    document.getElementById('ingredientProduct').value = '';
    document.getElementById('ingredientQuantity').value = '';
    document.getElementById('ingredientUnit').value = '';

    updateIngredientsDisplay();
}

// تحديث عرض المكونات
function updateIngredientsDisplay() {
    const container = document.getElementById('currentIngredients');
    if (!container) return;

    if (currentRecipeIngredients.length === 0) {
        container.innerHTML = '<div class="empty-ingredients">لم يتم إضافة مكونات بعد</div>';
        return;
    }

    let html = '<div class="ingredients-list">';
    currentRecipeIngredients.forEach((ingredient, index) => {
        html += `
            <div class="ingredient-item">
                <span class="ingredient-info">
                    ${ingredient.quantity} ${ingredient.unit} من ${ingredient.productName}
                </span>
                <button onclick="removeIngredient(${index})" class="btn-remove">×</button>
            </div>
        `;
    });
    html += '</div>';

    container.innerHTML = html;
}

// حذف مكون من الوصفة
function removeIngredient(index) {
    currentRecipeIngredients.splice(index, 1);
    updateIngredientsDisplay();
}

// حفظ الوصفة
function saveRecipe() {
    const name = document.getElementById('recipeName').value.trim();
    const productId = document.getElementById('recipeProduct').value;

    if (!name) {
        showNotification('يرجى إدخال اسم الوصفة', 'error');
        return;
    }

    if (!productId) {
        showNotification('يرجى اختيار المنتج النهائي', 'error');
        return;
    }

    if (currentRecipeIngredients.length === 0) {
        showNotification('يرجى إضافة مكون واحد على الأقل', 'error');
        return;
    }

    // التحقق من عدم وجود وصفة أخرى لنفس المنتج
    const existingRecipe = productRecipes.find(r => 
        r.productId === productId && r.id !== editingRecipeId
    );
    if (existingRecipe) {
        showNotification('يوجد وصفة أخرى لهذا المنتج بالفعل', 'error');
        return;
    }

    const recipe = {
        id: editingRecipeId || generateId(),
        productId: productId,
        name: name,
        ingredients: [...currentRecipeIngredients],
        createdAt: editingRecipeId ? 
            productRecipes.find(r => r.id === editingRecipeId).createdAt : 
            new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    if (editingRecipeId) {
        const index = productRecipes.findIndex(r => r.id === editingRecipeId);
        if (index !== -1) {
            productRecipes[index] = recipe;
        }
    } else {
        productRecipes.push(recipe);
    }

    // تحديث خاصية hasRecipe للمنتج
    const product = products.find(p => p.id === productId);
    if (product) {
        product.hasRecipe = true;
        product.recipeId = recipe.id;
    }

    saveData();
    displayRecipesList();
    hideRecipeForm();
    
    showNotification(editingRecipeId ? 'تم تحديث الوصفة بنجاح' : 'تم إضافة الوصفة بنجاح', 'success');
}

// تعديل وصفة
function editRecipe(recipeId) {
    const recipe = productRecipes.find(r => r.id === recipeId);
    if (!recipe) return;

    editingRecipeId = recipeId;
    currentRecipeIngredients = [...recipe.ingredients];

    document.getElementById('recipeName').value = recipe.name;
    document.getElementById('recipeProduct').value = recipe.productId;
    
    updateIngredientsDisplay();
    document.getElementById('recipeFormModal').classList.remove('hidden');
    document.getElementById('recipeFormTitle').textContent = 'تعديل الوصفة';
}

// حذف وصفة
function deleteRecipe(recipeId) {
    if (!confirm('هل أنت متأكد من حذف هذه الوصفة؟')) return;

    const recipe = productRecipes.find(r => r.id === recipeId);
    if (recipe) {
        // إزالة خاصية hasRecipe من المنتج
        const product = products.find(p => p.id === recipe.productId);
        if (product) {
            product.hasRecipe = false;
            delete product.recipeId;
        }
    }

    const index = productRecipes.findIndex(r => r.id === recipeId);
    if (index !== -1) {
        productRecipes.splice(index, 1);
        saveData();
        displayRecipesList();
        showNotification('تم حذف الوصفة بنجاح', 'success');
    }
}
